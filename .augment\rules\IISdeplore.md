---
alwaysApply: true
---

# IIS部署规范

## 项目构建
- 输出目录设置为`dist`
- 静态资源路径使用相对路径
- 启用Gzip压缩
- 生产环境关闭sourcemap

## web.config配置
必须包含以下规则：
```xml
<configuration>
    <system.webServer>
        <rewrite>
            <rules>
                <rule name="Handle History Mode" stopProcessing="true">
                    <match url=".*" />
                    <conditions>
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
                        <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
                    </conditions>
                    <action type="Rewrite" url="/index.html" />
                </rule>
            </rules>
        </rewrite>
    </system.webServer>

</configuration>