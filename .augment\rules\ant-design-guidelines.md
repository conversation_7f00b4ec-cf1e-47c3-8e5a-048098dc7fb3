---
alwaysApply: true
---

# AntDesign 设计规范

## 色彩系统
- 主色使用 `#1890ff`
- 成功状态使用 `#52c41a`
- 警告状态使用 `#faad14`
- 错误状态使用 `#f5222d`

## 间距与布局
- 基础间距单位8px
- 使用24栅格系统
- 表单元素间距16px
- 弹窗边距24px

## 组件规范
- 按钮：主按钮类型优先，危险操作使用危险样式
- 表单：标签右对齐，校验信息显示在表单项下方
- 表格：分页器默认显示在右下方
- 导航：顶部导航高度64px

## 响应式断点
- xs: <576px
- sm: 576-768px
- md: 768-992px
- lg: 992-1200px
- xl: 1200-1600px


# 组件使用规则

1. 表单组件
- 使用Form.Item包裹每个表单项
- 必填项添加红色星号标记
- 校验规则使用rules属性定义

2. 表格组件
- 分页默认每页显示10条数据
- 列宽使用固定宽度或响应式
- 操作列放在最右侧

3. 弹窗组件
- 确认对话框使用Modal.confirm
- 普通弹窗宽度默认520px

-  footer固定显示确定和取消按钮