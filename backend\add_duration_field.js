const mysql = require('mysql2/promise');

async function addDurationField() {
  let connection;
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'Aiwa75210!',
      database: 'patrol_system'
    });
    
    console.log('✅ 数据库连接成功');
    
    // 检查duration字段是否存在
    const [columns] = await connection.execute(
      `SHOW COLUMNS FROM batch_osd_room_channel_bindings LIKE 'duration'`
    );
    
    if (columns.length === 0) {
      // 添加duration字段
      await connection.execute(`
        ALTER TABLE batch_osd_room_channel_bindings 
        ADD COLUMN duration INT DEFAULT NULL COMMENT '执行耗时(毫秒)'
      `);
      console.log('✅ duration字段添加成功');
    } else {
      console.log('ℹ️  duration字段已存在');
    }
    
    // 验证字段
    const [result] = await connection.execute('DESCRIBE batch_osd_room_channel_bindings');
    const durationField = result.find(field => field.Field === 'duration');
    if (durationField) {
      console.log('duration字段信息:', durationField);
    }
    
  } catch (error) {
    console.error('❌ 操作失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

addDurationField();
