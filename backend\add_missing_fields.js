const mysql = require('mysql2/promise');

async function addMissingFields() {
  let connection;
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'Aiwa75210!',
      database: 'patrol_system'
    });
    
    console.log('✅ 数据库连接成功');
    
    // 检查并添加status字段
    const [statusColumns] = await connection.execute(
      `SHOW COLUMNS FROM batch_osd_room_channel_bindings LIKE 'status'`
    );
    
    if (statusColumns.length === 0) {
      await connection.execute(`
        ALTER TABLE batch_osd_room_channel_bindings 
        ADD COLUMN status ENUM('pending', 'running', 'success', 'failed') DEFAULT 'pending' COMMENT '执行状态'
      `);
      console.log('✅ status字段添加成功');
    } else {
      console.log('ℹ️  status字段已存在');
    }
    
    // 检查并添加start_time字段
    const [startTimeColumns] = await connection.execute(
      `SHOW COLUMNS FROM batch_osd_room_channel_bindings LIKE 'start_time'`
    );
    
    if (startTimeColumns.length === 0) {
      await connection.execute(`
        ALTER TABLE batch_osd_room_channel_bindings 
        ADD COLUMN start_time DATETIME DEFAULT NULL COMMENT '开始时间'
      `);
      console.log('✅ start_time字段添加成功');
    } else {
      console.log('ℹ️  start_time字段已存在');
    }
    
    // 检查并添加end_time字段
    const [endTimeColumns] = await connection.execute(
      `SHOW COLUMNS FROM batch_osd_room_channel_bindings LIKE 'end_time'`
    );
    
    if (endTimeColumns.length === 0) {
      await connection.execute(`
        ALTER TABLE batch_osd_room_channel_bindings 
        ADD COLUMN end_time DATETIME DEFAULT NULL COMMENT '结束时间'
      `);
      console.log('✅ end_time字段添加成功');
    } else {
      console.log('ℹ️  end_time字段已存在');
    }
    
    // 检查并添加message字段
    const [messageColumns] = await connection.execute(
      `SHOW COLUMNS FROM batch_osd_room_channel_bindings LIKE 'message'`
    );
    
    if (messageColumns.length === 0) {
      await connection.execute(`
        ALTER TABLE batch_osd_room_channel_bindings 
        ADD COLUMN message TEXT DEFAULT NULL COMMENT '执行消息'
      `);
      console.log('✅ message字段添加成功');
    } else {
      console.log('ℹ️  message字段已存在');
    }
    
    // 验证表结构
    const [result] = await connection.execute('DESCRIBE batch_osd_room_channel_bindings');
    console.log('\n📋 当前表结构:');
    result.forEach(field => {
      console.log(`  ${field.Field}: ${field.Type} ${field.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${field.Default ? `DEFAULT ${field.Default}` : ''}`);
    });
    
  } catch (error) {
    console.error('❌ 操作失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

addMissingFields();
