const mysql = require('mysql2/promise');

async function addSiteRoomFields() {
  let connection;
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'Aiwa75210!',
      database: 'patrol_system'
    });
    
    console.log('✅ 数据库连接成功');
    
    // 检查并添加site_name字段
    const [siteNameColumns] = await connection.execute(
      `SHOW COLUMNS FROM batch_osd_room_channel_bindings LIKE 'site_name'`
    );
    
    if (siteNameColumns.length === 0) {
      await connection.execute(`
        ALTER TABLE batch_osd_room_channel_bindings 
        ADD COLUMN site_name VARCHAR(200) DEFAULT NULL COMMENT '考点名称'
      `);
      console.log('✅ site_name字段添加成功');
    } else {
      console.log('ℹ️  site_name字段已存在');
    }
    
    // 检查并添加room_name字段
    const [roomNameColumns] = await connection.execute(
      `SHOW COLUMNS FROM batch_osd_room_channel_bindings LIKE 'room_name'`
    );
    
    if (roomNameColumns.length === 0) {
      await connection.execute(`
        ALTER TABLE batch_osd_room_channel_bindings 
        ADD COLUMN room_name VARCHAR(100) DEFAULT NULL COMMENT '考场名称'
      `);
      console.log('✅ room_name字段添加成功');
    } else {
      console.log('ℹ️  room_name字段已存在');
    }
    
    // 验证表结构
    const [result] = await connection.execute('DESCRIBE batch_osd_room_channel_bindings');
    console.log('\n📋 更新后的表结构:');
    result.forEach(field => {
      console.log(`  ${field.Field}: ${field.Type} ${field.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${field.Default ? `DEFAULT ${field.Default}` : ''}`);
    });
    
  } catch (error) {
    console.error('❌ 操作失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

addSiteRoomFields();
