const { query } = require('./src/config/database');

async function checkAndCreateBackupTables() {
  try {
    console.log('🔍 检查备份相关表...');

    // 检查batch_osd_backup_data表是否存在
    const backupTableExists = await query(`
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_schema = 'patrol_system' 
      AND table_name = 'batch_osd_backup_data'
    `);

    if (backupTableExists[0].count === 0) {
      console.log('📋 创建batch_osd_backup_data表...');
      await query(`
        CREATE TABLE batch_osd_backup_data (
          id INT AUTO_INCREMENT PRIMARY KEY,
          plan_id INT NOT NULL,
          channel_id VARCHAR(50) NOT NULL,
          channel_name VARCHAR(100),
          site_id VARCHAR(50),
          site_name VARCHAR(100),
          room_id VARCHAR(50),
          room_name VARCHAR(100),
          original_osd_content TEXT,
          backup_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          backup_strategy ENUM('auto', 'manual') DEFAULT 'auto',
          retention_days INT DEFAULT 30,
          status ENUM('backed_up', 'restored', 'restore_failed', 'expired') DEFAULT 'backed_up',
          restore_time TIMESTAMP NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_plan_id (plan_id),
          INDEX idx_channel_id (channel_id),
          INDEX idx_backup_time (backup_time),
          INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OSD备份数据表'
      `);
      console.log('✅ batch_osd_backup_data表创建成功');
    } else {
      console.log('✅ batch_osd_backup_data表已存在');
    }

    // 检查batch_osd_plan_configs表是否存在
    const configTableExists = await query(`
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_schema = 'patrol_system' 
      AND table_name = 'batch_osd_plan_configs'
    `);

    if (configTableExists[0].count === 0) {
      console.log('📋 创建batch_osd_plan_configs表...');
      await query(`
        CREATE TABLE batch_osd_plan_configs (
          id INT AUTO_INCREMENT PRIMARY KEY,
          plan_id INT NOT NULL,
          config_type ENUM('osd_config', 'room_config', 'schedule_config', 'backup_config') NOT NULL,
          config_data JSON,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_plan_id (plan_id),
          INDEX idx_config_type (config_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量OSD计划配置表'
      `);
      console.log('✅ batch_osd_plan_configs表创建成功');
    } else {
      console.log('✅ batch_osd_plan_configs表已存在');
    }

    // 检查batch_osd_execution_logs表是否存在
    const logsTableExists = await query(`
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_schema = 'patrol_system' 
      AND table_name = 'batch_osd_execution_logs'
    `);

    if (logsTableExists[0].count === 0) {
      console.log('📋 创建batch_osd_execution_logs表...');
      await query(`
        CREATE TABLE batch_osd_execution_logs (
          id INT AUTO_INCREMENT PRIMARY KEY,
          plan_id INT NOT NULL,
          exam_site_id VARCHAR(50) NOT NULL,
          exam_site_name VARCHAR(200) NOT NULL,
          status ENUM('pending', 'running', 'success', 'failed', 'skipped') NOT NULL DEFAULT 'pending',
          message TEXT,
          osd_config JSON,
          execute_time TIMESTAMP NULL,
          duration INT,
          retry_count INT NOT NULL DEFAULT 0,
          execution_type ENUM('site', 'room', 'channel', 'restore') DEFAULT 'site',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_plan_id (plan_id),
          INDEX idx_exam_site_id (exam_site_id),
          INDEX idx_status (status),
          INDEX idx_execute_time (execute_time),
          INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量OSD执行日志表'
      `);
      console.log('✅ batch_osd_execution_logs表创建成功');
    } else {
      console.log('✅ batch_osd_execution_logs表已存在');
    }

    console.log('🎉 所有备份相关表检查完成！');

  } catch (error) {
    console.error('❌ 检查备份表失败:', error);
  }
}

// 运行检查
checkAndCreateBackupTables().then(() => {
  console.log('✅ 备份表检查完成');
  process.exit(0);
}).catch(error => {
  console.error('❌ 备份表检查失败:', error);
  process.exit(1);
}); 