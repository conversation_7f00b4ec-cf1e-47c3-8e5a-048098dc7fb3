const mysql = require('mysql2/promise');

async function checkBindingsTable() {
  let connection;
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'Aiwa75210!',
      database: 'patrol_system'
    });
    
    console.log('✅ 数据库连接成功');
    
    // 检查batch_osd_room_channel_bindings表结构
    console.log('\n📋 batch_osd_room_channel_bindings 表结构:');
    const [result] = await connection.execute('DESCRIBE batch_osd_room_channel_bindings');
    result.forEach(field => {
      console.log(`  ${field.Field}: ${field.Type} ${field.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${field.Default ? `DEFAULT ${field.Default}` : ''}`);
    });
    
    // 检查是否有数据
    const [count] = await connection.execute('SELECT COUNT(*) as count FROM batch_osd_room_channel_bindings');
    console.log(`\n📊 表中数据条数: ${count[0].count}`);
    
    // 如果有数据，显示前几条
    if (count[0].count > 0) {
      const [sample] = await connection.execute('SELECT * FROM batch_osd_room_channel_bindings LIMIT 3');
      console.log('\n📝 示例数据:');
      sample.forEach((row, index) => {
        console.log(`  记录 ${index + 1}:`, row);
      });
    }
    
  } catch (error) {
    console.error('❌ 操作失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

checkBindingsTable();
