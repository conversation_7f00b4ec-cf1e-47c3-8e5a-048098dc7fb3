// 检查数据库数据的脚本
const mysql = require('mysql2/promise');

const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: 'Aiwa75210!',
  database: 'patrol_system',
  charset: 'utf8mb4'
};

async function checkData() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功\n');

    // 检查行政区划数据
    console.log('=== 行政区划数据 ===');
    const [adminRows] = await connection.execute(`
      SELECT id, parent_id, name, level, sort_order 
      FROM administrative_divisions 
      ORDER BY level, sort_order, id
    `);
    
    adminRows.forEach(row => {
      console.log(`${row.level}: ${row.name} (${row.id}) - Parent: ${row.parent_id || 'null'}`);
    });

    // 检查考点数据
    console.log('\n=== 考点数据 ===');
    const [examRows] = await connection.execute(`
      SELECT id, parent_id, parent_level, name, sort_order 
      FROM exam_sites 
      ORDER BY parent_level, sort_order, id
    `);
    
    examRows.forEach(row => {
      console.log(`${row.parent_level} 考点: ${row.name} (${row.id}) - Parent: ${row.parent_id}`);
    });

    console.log(`\n总计: ${adminRows.length} 个行政区划, ${examRows.length} 个考点`);

  } catch (error) {
    console.error('检查失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行检查
checkData();
