const mysql = require('mysql2/promise');

async function checkDatabase() {
  let connection;
  try {
    // 连接数据库
    // 尝试多种连接方式
    const connectionConfigs = [
      {
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: 'Aiwa75210！',
        database: 'patrol_system'
      },
      {
        host: '127.0.0.1',
        port: 3306,
        user: 'root',
        password: 'Aiwa75210！',
        database: 'patrol_system'
      },
      {
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: 'root',
        database: 'patrol_system'
      }
    ];

    let connected = false;
    for (const config of connectionConfigs) {
      try {
        console.log(`尝试连接: ${config.user}@${config.host}:${config.port}`);
        connection = await mysql.createConnection(config);
        connected = true;
        console.log('✅ 数据库连接成功');
        break;
      } catch (error) {
        console.log(`❌ 连接失败: ${error.message}`);
      }
    }

    if (!connected) {
      throw new Error('所有连接方式都失败了');
    }



    // 检查所有表
    console.log('\n=== 检查数据库表结构 ===');
    const [tables] = await connection.execute('SHOW TABLES');
    const tableNames = tables.map(t => Object.values(t)[0]);
    console.log('现有表:', tableNames);

    // 检查每个表的记录数
    for (const tableName of tableNames) {
      try {
        const [result] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName}`);
        console.log(`${tableName}: ${result[0].count} 条记录`);
      } catch (error) {
        console.log(`${tableName}: 查询失败 - ${error.message}`);
      }
    }

    // 检查是否缺少关键表
    const requiredTables = ['osd_rules', 'batch_osd_plans'];
    const missingTables = requiredTables.filter(table => !tableNames.includes(table));

    if (missingTables.length > 0) {
      console.log('\n❌ 缺少关键业务表:', missingTables);
      console.log('需要执行数据库初始化脚本');
    } else {
      console.log('\n✅ 所有关键业务表都存在');
    }

  } catch (error) {
    console.error('❌ 数据库检查失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

checkDatabase();
