const { query } = require('./src/config/database');

async function checkLatestPlans() {
  try {
    console.log('🔍 检查最新计划数据...');

    // 检查最新的计划
    const plans = await query(`
      SELECT id, name, description, status, create_time
      FROM batch_osd_plans
      ORDER BY create_time DESC
      LIMIT 10
    `);

    console.log('最新计划列表:');
    plans.forEach(plan => {
      console.log(`ID: ${plan.id}, 名称: ${plan.name}, 状态: ${plan.status}, 创建时间: ${plan.create_time}`);
    });

    // 检查配置数据
    const configs = await query(`
      SELECT plan_id, config_type, config_data
      FROM batch_osd_plan_configs
      ORDER BY plan_id DESC
      LIMIT 20
    `);

    console.log('\n配置数据:');
    configs.forEach(config => {
      console.log(`计划ID: ${config.plan_id}, 类型: ${config.config_type}, 数据: ${config.config_data}`);
    });

    // 检查备份数据
    const backups = await query(`
      SELECT plan_id, COUNT(*) as count
      FROM batch_osd_backup_data
      GROUP BY plan_id
    `);

    console.log('\n备份数据统计:');
    backups.forEach(backup => {
      console.log(`计划ID: ${backup.plan_id}, 备份数量: ${backup.count}`);
    });

  } catch (error) {
    console.error('检查失败:', error);
  }
}

checkLatestPlans().then(() => {
  console.log('✅ 检查完成');
  process.exit(0);
}).catch(error => {
  console.error('❌ 检查失败:', error);
  process.exit(1);
}); 