const { query } = require('./src/config/database');

async function checkTableStructure() {
  try {
    console.log('🔍 检查表结构...');

    // 检查batch_osd_execution_logs表结构
    const tableStructure = await query(`
      DESCRIBE batch_osd_execution_logs
    `);
    
    console.log('batch_osd_execution_logs表结构:');
    tableStructure.forEach(field => {
      console.log(`${field.Field}: ${field.Type} ${field.Null === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });

  } catch (error) {
    console.error('检查表结构失败:', error);
  }
}

checkTableStructure().then(() => {
  console.log('✅ 表结构检查完成');
  process.exit(0);
}).catch(error => {
  console.error('❌ 表结构检查失败:', error);
  process.exit(1);
}); 