// 检查考点URI数据的脚本
const mysql = require('mysql2/promise');

const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: 'Aiwa75210!',
  database: 'patrol_system',
  charset: 'utf8mb4'
};

async function checkUriData() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功\n');

    // 检查考点URI数据
    console.log('=== 考点URI数据检查 ===');
    const [examRows] = await connection.execute(`
      SELECT
        es.id,
        es.name,
        es.short_name,
        es.parent_id,
        es.parent_level,
        es.uri
      FROM exam_sites es
      ORDER BY es.parent_level, es.id
    `);

    // 单独查询上级机构名称
    const [adminRows] = await connection.execute(`
      SELECT id, name FROM administrative_divisions
    `);

    const adminMap = {};
    adminRows.forEach(row => {
      adminMap[row.id] = row.name;
    });
    
    examRows.forEach(row => {
      const parentName = adminMap[row.parent_id] || '未知';
      console.log(`${row.parent_level} 考点: ${row.name}`);
      console.log(`  ID: ${row.id}, 上级: ${parentName} (${row.parent_id})`);
      console.log(`  URI: ${row.uri || '未设置'}`);
      console.log('');
    });

    console.log(`总计: ${examRows.length} 个考点`);
    
    // 统计URI设置情况
    const withUri = examRows.filter(row => row.uri && row.uri.trim() !== '');
    const withoutUri = examRows.filter(row => !row.uri || row.uri.trim() === '');
    
    console.log(`\n=== URI统计 ===`);
    console.log(`已设置URI: ${withUri.length} 个`);
    console.log(`未设置URI: ${withoutUri.length} 个`);
    
    if (withoutUri.length > 0) {
      console.log('\n未设置URI的考点:');
      withoutUri.forEach(row => {
        console.log(`  - ${row.name} (${row.id})`);
      });
    }

  } catch (error) {
    console.error('检查失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行检查
checkUriData();
