// 清理包含字母T的测试数据
const mysql = require('mysql2/promise');

const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: 'Aiwa75210!',
  database: 'patrol_system',
  charset: 'utf8mb4'
};

async function cleanTestData() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功\n');

    // 查找包含字母T的机构码
    console.log('=== 查找包含字母T的机构码 ===');
    const [examRows] = await connection.execute(`
      SELECT id, name, short_name FROM exam_sites WHERE id LIKE '%T%'
    `);
    
    const [adminRows] = await connection.execute(`
      SELECT id, name, short_name FROM administrative_divisions WHERE id LIKE '%T%'
    `);

    console.log(`考点中包含T的记录: ${examRows.length} 个`);
    examRows.forEach(row => {
      console.log(`  - ${row.name} (${row.id})`);
    });

    console.log(`行政区划中包含T的记录: ${adminRows.length} 个`);
    adminRows.forEach(row => {
      console.log(`  - ${row.name} (${row.id})`);
    });

    // 删除包含字母T的测试数据
    if (examRows.length > 0 || adminRows.length > 0) {
      console.log('\n=== 开始清理测试数据 ===');
      
      // 删除考点中的测试数据
      if (examRows.length > 0) {
        await connection.execute(`DELETE FROM exam_sites WHERE id LIKE '%T%'`);
        console.log(`✓ 已删除 ${examRows.length} 个包含T的考点记录`);
      }

      // 删除行政区划中的测试数据
      if (adminRows.length > 0) {
        await connection.execute(`DELETE FROM administrative_divisions WHERE id LIKE '%T%'`);
        console.log(`✓ 已删除 ${adminRows.length} 个包含T的行政区划记录`);
      }

      console.log('\n✅ 测试数据清理完成！');
    } else {
      console.log('\n✅ 没有发现包含字母T的测试数据');
    }

    // 验证清理结果
    console.log('\n=== 验证清理结果 ===');
    const [remainingExam] = await connection.execute(`
      SELECT COUNT(*) as count FROM exam_sites WHERE id LIKE '%T%'
    `);
    const [remainingAdmin] = await connection.execute(`
      SELECT COUNT(*) as count FROM administrative_divisions WHERE id LIKE '%T%'
    `);

    console.log(`剩余包含T的考点记录: ${remainingExam[0].count} 个`);
    console.log(`剩余包含T的行政区划记录: ${remainingAdmin[0].count} 个`);

    // 显示当前所有机构码
    console.log('\n=== 当前所有机构码 ===');
    const [allExam] = await connection.execute(`
      SELECT id, name FROM exam_sites ORDER BY id
    `);
    const [allAdmin] = await connection.execute(`
      SELECT id, name FROM administrative_divisions ORDER BY id
    `);

    console.log('行政区划:');
    allAdmin.forEach(row => {
      console.log(`  ${row.id} - ${row.name}`);
    });

    console.log('考点:');
    allExam.forEach(row => {
      console.log(`  ${row.id} - ${row.name}`);
    });

  } catch (error) {
    console.error('清理失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行清理
cleanTestData();
