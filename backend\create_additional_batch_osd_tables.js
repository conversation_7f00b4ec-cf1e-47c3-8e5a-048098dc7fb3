// 创建批量OSD功能的额外数据表
const mysql = require('mysql2/promise');

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'Aiwa75210!',
  database: 'patrol_system',
  charset: 'utf8mb4'
};

async function createAdditionalTables() {
  let connection;

  try {
    console.log('🔗 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 1. 创建生成的考场数据表
    console.log('📋 创建生成的考场数据表...');
    const createGeneratedRoomsSQL = `
      CREATE TABLE IF NOT EXISTS batch_osd_generated_rooms (
        id INT AUTO_INCREMENT PRIMARY KEY,
        plan_id INT NOT NULL COMMENT '计划ID',
        room_id VARCHAR(50) NOT NULL COMMENT '考场ID',
        site_id VARCHAR(50) COMMENT '考点ID',
        room_name VARCHAR(100) COMMENT '考场名称',
        room_number VARCHAR(50) COMMENT '考场编号',
        room_type ENUM('room', 'backup') DEFAULT 'room' COMMENT '考场类型',
        osd_label TEXT COMMENT 'OSD标签内容',
        floor_number INT DEFAULT 1 COMMENT '楼层号',
        capacity INT DEFAULT 30 COMMENT '容量',
        status ENUM('generated', 'bound', 'executed') DEFAULT 'generated' COMMENT '状态',
        create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        INDEX idx_plan_id (plan_id),
        INDEX idx_room_id (room_id),
        INDEX idx_site_id (site_id),
        INDEX idx_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量OSD生成的考场数据表';
    `;

    await connection.execute(createGeneratedRoomsSQL);
    console.log('✅ 生成的考场数据表创建成功');

    // 2. 创建考场与视频通道绑定关系表
    console.log('📋 创建考场与视频通道绑定关系表...');
    const createRoomChannelBindingsSQL = `
      CREATE TABLE IF NOT EXISTS batch_osd_room_channel_bindings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        plan_id INT NOT NULL COMMENT '计划ID',
        room_id VARCHAR(50) NOT NULL COMMENT '考场ID',
        channel_id VARCHAR(50) NOT NULL COMMENT '视频通道ID',
        binding_type ENUM('auto', 'manual') DEFAULT 'manual' COMMENT '绑定类型',
        binding_order INT DEFAULT 0 COMMENT '绑定顺序',
        is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
        create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        INDEX idx_plan_id (plan_id),
        INDEX idx_room_id (room_id),
        INDEX idx_channel_id (channel_id),
        INDEX idx_binding_type (binding_type),
        UNIQUE KEY uk_plan_room_channel (plan_id, room_id, channel_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量OSD考场与视频通道绑定关系表';
    `;

    await connection.execute(createRoomChannelBindingsSQL);
    console.log('✅ 考场与视频通道绑定关系表创建成功');

    // 3. 创建计划配置信息表
    console.log('📋 创建计划配置信息表...');
    const createPlanConfigsSQL = `
      CREATE TABLE IF NOT EXISTS batch_osd_plan_configs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        plan_id INT NOT NULL COMMENT '计划ID',
        config_type ENUM('osd_config', 'room_config', 'schedule_config', 'backup_config') NOT NULL COMMENT '配置类型',
        config_data JSON COMMENT '配置数据',
        create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        INDEX idx_plan_id (plan_id),
        INDEX idx_config_type (config_type),
        UNIQUE KEY uk_plan_config_type (plan_id, config_type)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量OSD计划配置信息表';
    `;

    await connection.execute(createPlanConfigsSQL);
    console.log('✅ 计划配置信息表创建成功');

    console.log('🎉 所有额外数据表创建完成！');

    // 验证表是否创建成功
    console.log('🔍 验证表创建结果...');
    const tables = ['batch_osd_generated_rooms', 'batch_osd_room_channel_bindings', 'batch_osd_plan_configs'];

    for (const tableName of tables) {
      const [rows] = await connection.execute(`SHOW TABLES LIKE '${tableName}'`);
      if (rows.length > 0) {
        console.log(`✅ 表 ${tableName} 创建成功`);
      } else {
        console.log(`❌ 表 ${tableName} 创建失败`);
      }
    }

  } catch (error) {
    console.error('❌ 创建数据表失败:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔗 数据库连接已关闭');
    }
  }
}

// 执行创建表操作
if (require.main === module) {
  createAdditionalTables()
    .then(() => {
      console.log('✅ 数据表创建脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 数据表创建脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { createAdditionalTables };
