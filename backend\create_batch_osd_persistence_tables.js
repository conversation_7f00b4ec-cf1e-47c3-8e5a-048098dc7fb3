const mysql = require('mysql2/promise');

async function createBatchOsdPersistenceTables() {
  let connection;
  try {
    // 连接数据库
    connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'Aiwa75210!',
      database: 'patrol_system'
    });
    
    console.log('✅ 数据库连接成功');
    
    // 1. 创建批量OSD计划选定考点表
    console.log('📋 创建批量OSD计划选定考点表...');
    const createPlanSitesSQL = `
      CREATE TABLE IF NOT EXISTS batch_osd_plan_sites (
        id INT AUTO_INCREMENT PRIMARY KEY,
        plan_id INT NOT NULL COMMENT '计划ID',
        site_id VARCHAR(50) NOT NULL COMMENT '考点ID',
        site_name VARCHAR(200) NOT NULL COMMENT '考点名称',
        site_code VARCHAR(50) COMMENT '考点编码',
        province_id VARCHAR(20) COMMENT '省份ID',
        province_name VARCHAR(50) COMMENT '省份名称',
        city_id VARCHAR(20) COMMENT '地市ID', 
        city_name VARCHAR(50) COMMENT '地市名称',
        district_id VARCHAR(20) COMMENT '区县ID',
        district_name VARCHAR(50) COMMENT '区县名称',
        address VARCHAR(500) COMMENT '考点地址',
        contact_person VARCHAR(100) COMMENT '联系人',
        contact_phone VARCHAR(50) COMMENT '联系电话',
        room_count INT DEFAULT 0 COMMENT '考场数量',
        status ENUM('selected', 'processing', 'completed', 'failed', 'skipped') DEFAULT 'selected' COMMENT '处理状态',
        create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        INDEX idx_plan_id (plan_id),
        INDEX idx_site_id (site_id),
        INDEX idx_status (status),
        FOREIGN KEY (plan_id) REFERENCES batch_osd_plans(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量OSD计划选定考点表'
    `;
    await connection.query(createPlanSitesSQL);
    console.log('   ✅ batch_osd_plan_sites表创建成功');
    
    // 2. 创建批量OSD考场编号生成表
    console.log('📋 创建批量OSD考场编号生成表...');
    const createExamRoomsSQL = `
      CREATE TABLE IF NOT EXISTS batch_osd_exam_rooms (
        id INT AUTO_INCREMENT PRIMARY KEY,
        plan_id INT NOT NULL COMMENT '计划ID',
        site_id VARCHAR(50) NOT NULL COMMENT '考点ID',
        room_number VARCHAR(20) NOT NULL COMMENT '考场编号',
        room_name VARCHAR(100) COMMENT '考场名称',
        room_type ENUM('standard', 'special', 'backup') DEFAULT 'standard' COMMENT '考场类型',
        capacity INT DEFAULT 30 COMMENT '考场容量',
        floor_number INT COMMENT '楼层',
        building_name VARCHAR(100) COMMENT '楼栋名称',
        equipment_info JSON COMMENT '设备信息',
        osd_config JSON COMMENT 'OSD配置信息',
        status ENUM('generated', 'configured', 'active', 'inactive') DEFAULT 'generated' COMMENT '状态',
        create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        INDEX idx_plan_id (plan_id),
        INDEX idx_site_id (site_id),
        INDEX idx_room_number (room_number),
        INDEX idx_status (status),
        UNIQUE KEY uk_plan_site_room (plan_id, site_id, room_number),
        FOREIGN KEY (plan_id) REFERENCES batch_osd_plans(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量OSD考场编号生成表'
    `;
    await connection.query(createExamRoomsSQL);
    console.log('   ✅ batch_osd_exam_rooms表创建成功');
    
    // 3. 创建批量OSD考点考场绑定关系表
    console.log('📋 创建批量OSD考点考场绑定关系表...');
    const createSiteRoomBindingsSQL = `
      CREATE TABLE IF NOT EXISTS batch_osd_site_room_bindings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        plan_id INT NOT NULL COMMENT '计划ID',
        site_id VARCHAR(50) NOT NULL COMMENT '考点ID',
        room_id INT NOT NULL COMMENT '考场ID',
        binding_type ENUM('auto', 'manual') DEFAULT 'auto' COMMENT '绑定类型',
        binding_order INT DEFAULT 0 COMMENT '绑定顺序',
        osd_template TEXT COMMENT 'OSD模板',
        osd_content TEXT COMMENT '生成的OSD内容',
        is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
        create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        INDEX idx_plan_id (plan_id),
        INDEX idx_site_id (site_id),
        INDEX idx_room_id (room_id),
        INDEX idx_binding_order (binding_order),
        UNIQUE KEY uk_plan_site_room_binding (plan_id, site_id, room_id),
        FOREIGN KEY (plan_id) REFERENCES batch_osd_plans(id) ON DELETE CASCADE,
        FOREIGN KEY (room_id) REFERENCES batch_osd_exam_rooms(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量OSD考点考场绑定关系表'
    `;
    await connection.query(createSiteRoomBindingsSQL);
    console.log('   ✅ batch_osd_site_room_bindings表创建成功');
    
    // 4. 创建批量OSD执行结果日志表
    console.log('📋 创建批量OSD执行结果日志表...');
    const createExecutionLogsSQL = `
      CREATE TABLE IF NOT EXISTS batch_osd_execution_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        plan_id INT NOT NULL COMMENT '计划ID',
        site_id VARCHAR(50) NOT NULL COMMENT '考点ID',
        site_name VARCHAR(200) NOT NULL COMMENT '考点名称',
        room_id INT COMMENT '考场ID',
        room_number VARCHAR(20) COMMENT '考场编号',
        execution_type ENUM('site', 'room', 'binding') DEFAULT 'site' COMMENT '执行类型',
        status ENUM('pending', 'running', 'success', 'failed', 'skipped', 'retry') DEFAULT 'pending' COMMENT '执行状态',
        progress INT DEFAULT 0 COMMENT '执行进度(0-100)',
        message TEXT COMMENT '执行信息',
        error_code VARCHAR(50) COMMENT '错误代码',
        error_message TEXT COMMENT '错误详情',
        request_data JSON COMMENT '请求数据',
        response_data JSON COMMENT '响应数据',
        start_time TIMESTAMP NULL COMMENT '开始时间',
        end_time TIMESTAMP NULL COMMENT '结束时间',
        duration INT COMMENT '执行耗时(毫秒)',
        retry_count INT DEFAULT 0 COMMENT '重试次数',
        max_retry INT DEFAULT 3 COMMENT '最大重试次数',
        operator VARCHAR(100) COMMENT '操作人',
        create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        INDEX idx_plan_id (plan_id),
        INDEX idx_site_id (site_id),
        INDEX idx_room_id (room_id),
        INDEX idx_status (status),
        INDEX idx_execution_type (execution_type),
        INDEX idx_start_time (start_time),
        INDEX idx_create_time (create_time),
        FOREIGN KEY (plan_id) REFERENCES batch_osd_plans(id) ON DELETE CASCADE,
        FOREIGN KEY (room_id) REFERENCES batch_osd_exam_rooms(id) ON DELETE SET NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量OSD执行结果日志表'
    `;
    await connection.query(createExecutionLogsSQL);
    console.log('   ✅ batch_osd_execution_logs表创建成功');
    
    // 5. 创建批量OSD配置历史表
    console.log('📋 创建批量OSD配置历史表...');
    const createConfigHistorySQL = `
      CREATE TABLE IF NOT EXISTS batch_osd_config_history (
        id INT AUTO_INCREMENT PRIMARY KEY,
        plan_id INT COMMENT '计划ID',
        site_id VARCHAR(50) NOT NULL COMMENT '考点ID',
        site_name VARCHAR(200) NOT NULL COMMENT '考点名称',
        room_id INT COMMENT '考场ID',
        room_number VARCHAR(20) COMMENT '考场编号',
        rule_id INT COMMENT '规则ID',
        rule_name VARCHAR(200) COMMENT '规则名称',
        old_config JSON COMMENT '旧配置',
        new_config JSON COMMENT '新配置',
        change_type ENUM('create', 'update', 'delete', 'batch') DEFAULT 'batch' COMMENT '变更类型',
        change_reason VARCHAR(500) COMMENT '变更原因',
        operator VARCHAR(100) COMMENT '操作人',
        create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '变更时间',
        INDEX idx_plan_id (plan_id),
        INDEX idx_site_id (site_id),
        INDEX idx_room_id (room_id),
        INDEX idx_rule_id (rule_id),
        INDEX idx_change_type (change_type),
        INDEX idx_create_time (create_time),
        FOREIGN KEY (plan_id) REFERENCES batch_osd_plans(id) ON DELETE SET NULL,
        FOREIGN KEY (room_id) REFERENCES batch_osd_exam_rooms(id) ON DELETE SET NULL,
        FOREIGN KEY (rule_id) REFERENCES osd_rules(id) ON DELETE SET NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量OSD配置历史表'
    `;
    await connection.query(createConfigHistorySQL);
    console.log('   ✅ batch_osd_config_history表创建成功');
    
    console.log('\n🔍 验证表创建结果:');
    const [tables] = await connection.query('SHOW TABLES');
    const tableNames = tables.map(t => Object.values(t)[0]);
    console.log(`📋 所有表: ${tableNames.join(', ')}`);
    
    // 检查新创建的表
    const newTables = [
      'batch_osd_plan_sites',
      'batch_osd_exam_rooms', 
      'batch_osd_site_room_bindings',
      'batch_osd_execution_logs',
      'batch_osd_config_history'
    ];
    
    for (const tableName of newTables) {
      if (tableNames.includes(tableName)) {
        const [count] = await connection.query(`SELECT COUNT(*) as count FROM ${tableName}`);
        console.log(`📊 ${tableName}: ${count[0].count} 条记录`);
      } else {
        console.log(`❌ 缺少表: ${tableName}`);
      }
    }
    
    console.log('\n🎉 批量OSD数据持久化表创建完成！');
    console.log('✅ 现在支持完整的数据持久化功能：');
    console.log('   - 计划选定考点数据持久化');
    console.log('   - 考场编号生成数据持久化');
    console.log('   - 考点考场绑定关系持久化');
    console.log('   - 批量设置结果数据持久化');
    console.log('   - 配置变更历史记录');
    
  } catch (error) {
    console.error('❌ 创建表失败:', error.message);
    if (error.code) {
      console.error(`🔍 错误代码: ${error.code}`);
    }
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

console.log('🚀 开始创建批量OSD数据持久化表...\n');
createBatchOsdPersistenceTables();
