const mysql = require('mysql2/promise');

async function createBusinessTables() {
  let connection;
  try {
    // 连接数据库
    connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'Aiwa75210!',
      database: 'patrol_system'
    });
    
    console.log('✅ 数据库连接成功');
    
    // 创建OSD规则表
    console.log('📋 创建OSD规则表...');
    const createOsdRulesSQL = `
      CREATE TABLE IF NOT EXISTS osd_rules (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL COMMENT '规则名称',
        type ENUM('time', 'osd') DEFAULT 'osd' COMMENT '规则类型',
        format TEXT NOT NULL COMMENT 'OSD格式模板',
        description TEXT COMMENT '规则描述',
        preview VARCHAR(500) COMMENT '预览效果',
        status TINYINT DEFAULT 1 COMMENT '状态(0:禁用,1:启用)',
        create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        INDEX idx_type (type),
        INDEX idx_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OSD设置规则表'
    `;
    
    await connection.query(createOsdRulesSQL);
    console.log('   ✅ osd_rules表创建成功');
    
    // 创建批量OSD计划表
    console.log('📋 创建批量OSD计划表...');
    const createBatchOsdPlansSQL = `
      CREATE TABLE IF NOT EXISTS batch_osd_plans (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(200) NOT NULL COMMENT '计划名称',
        description TEXT COMMENT '计划描述',
        rule_id INT NOT NULL COMMENT 'OSD规则ID',
        target_type ENUM('all', 'province', 'city', 'district', 'custom') DEFAULT 'all' COMMENT '目标类型',
        target_ids JSON COMMENT '目标ID列表',
        execute_mode ENUM('immediate', 'scheduled', 'periodic') DEFAULT 'immediate' COMMENT '执行模式',
        schedule_time DATETIME NULL COMMENT '计划执行时间',
        concurrency INT DEFAULT 3 COMMENT '并发数',
        target_count INT DEFAULT 0 COMMENT '目标总数',
        completed_count INT DEFAULT 0 COMMENT '已完成数',
        success_count INT DEFAULT 0 COMMENT '成功数',
        failed_count INT DEFAULT 0 COMMENT '失败数',
        status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending' COMMENT '执行状态',
        start_time DATETIME NULL COMMENT '开始时间',
        end_time DATETIME NULL COMMENT '结束时间',
        created_by VARCHAR(50) DEFAULT 'admin' COMMENT '创建人',
        create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        INDEX idx_status (status),
        INDEX idx_created_by (created_by),
        FOREIGN KEY (rule_id) REFERENCES osd_rules(id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量OSD设置计划表'
    `;
    
    await connection.query(createBatchOsdPlansSQL);
    console.log('   ✅ batch_osd_plans表创建成功');
    
    // 插入示例OSD规则
    console.log('📊 插入示例OSD规则...');
    const insertOsdRulesSQL = `
      INSERT IGNORE INTO osd_rules (id, name, type, format, description, preview, status) VALUES
      (1, '完整OSD格式', 'osd', '[省份][地市][区县][考点名称][考点编号] 考场[考场号]', '包含完整地理信息的OSD格式', '浙江省杭州西湖第一考点KD001 考场01', 1),
      (2, '简化OSD格式', 'osd', '[省份简称][地市][考点名称] 考场[考场号]', '简化的OSD格式，减少显示内容', '浙江杭州第一考点 考场01', 1),
      (3, '代码格式OSD', 'osd', '[省份代码][地市][区县][考点编号] 考场[考场号]', '使用省份代码的OSD格式', '33杭州西湖KD001 考场01', 1),
      (4, '单字简称格式', 'osd', '[省份单字简称][地市][考点名称] 考场[考场号]', '使用省份单字简称的OSD格式', '浙杭州第一考点 考场01', 1),
      (5, '精简格式', 'osd', '[考点名称] 考场[考场号]', '最简化的OSD格式', '第一考点 考场01', 1)
    `;
    
    await connection.query(insertOsdRulesSQL);
    console.log('   ✅ 示例OSD规则插入成功');
    
    // 插入示例批量计划
    console.log('📊 插入示例批量计划...');
    const insertPlansSQL = `
      INSERT IGNORE INTO batch_osd_plans (id, name, description, rule_id, target_type, target_ids, execute_mode, concurrency, target_count, completed_count, success_count, failed_count, status, start_time, end_time, created_by) VALUES
      (1, '全省考点OSD统一设置', '为全省所有考点统一设置OSD显示格式', 2, 'all', '[]', 'immediate', 5, 150, 150, 148, 2, 'completed', '2024-03-21 09:30:00', '2024-03-21 10:15:00', 'admin'),
      (2, '成都市考点OSD更新', '更新成都市所有考点的OSD格式为新标准', 2, 'city', '["5101"]', 'immediate', 3, 45, 32, 30, 2, 'running', '2024-03-21 14:30:00', NULL, 'admin'),
      (3, '重点考点OSD配置', '为重点考点配置特殊的OSD显示格式', 5, 'custom', '["510104001", "510105002", "440106001"]', 'scheduled', 2, 3, 0, 0, 0, 'pending', NULL, NULL, 'admin')
    `;
    
    await connection.query(insertPlansSQL);
    console.log('   ✅ 示例批量计划插入成功');
    
    // 验证创建结果
    console.log('\n🔍 验证表创建结果:');
    const [tables] = await connection.query('SHOW TABLES');
    const tableNames = tables.map(t => Object.values(t)[0]);
    console.log(`📋 所有表: ${tableNames.join(', ')}`);
    
    // 检查数据
    const [osdRulesCount] = await connection.query('SELECT COUNT(*) as count FROM osd_rules');
    const [plansCount] = await connection.query('SELECT COUNT(*) as count FROM batch_osd_plans');
    
    console.log(`📊 osd_rules表: ${osdRulesCount[0].count} 条记录`);
    console.log(`📊 batch_osd_plans表: ${plansCount[0].count} 条记录`);
    
    console.log('\n🎉 所有业务表创建完成！');
    console.log('✅ 数据库现在包含完整的OSD业务数据');
    
  } catch (error) {
    console.error('❌ 创建表失败:', error.message);
    if (error.code) {
      console.error(`🔍 错误代码: ${error.code}`);
    }
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

console.log('🚀 开始创建业务数据表...\n');
createBusinessTables();
