-- 创建数据库
CREATE DATABASE IF NOT EXISTS patrol_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE patrol_system;

-- 创建行政区划表
CREATE TABLE IF NOT EXISTS administrative_divisions (
  id VARCHAR(20) PRIMARY KEY,
  parent_id VARCHAR(20),
  name VARCHAR(100) NOT NULL,
  short_name VARCHAR(50),
  single_name VARCHAR(10),
  level ENUM('root', 'province', 'city', 'district') NOT NULL,
  sort INT DEFAULT 0,
  status TINYINT DEFAULT 1,
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_parent_id (parent_id),
  INDEX idx_level (level),
  INDEX idx_status (status)
);

-- 创建考点表
CREATE TABLE IF NOT EXISTS exam_sites (
  id VARCHAR(20) PRIMARY KEY,
  parent_id VARCHAR(20) NOT NULL,
  parent_level ENUM('province', 'city', 'district') NOT NULL,
  full_name VARCHAR(200) NOT NULL,
  short_name VARCHAR(100),
  uri VARCHAR(500),
  address TEXT,
  contact_person VARCHAR(50),
  contact_phone VARCHAR(20),
  capacity INT DEFAULT 0,
  sort INT DEFAULT 0,
  status TINYINT DEFAULT 1,
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_parent_id (parent_id),
  INDEX idx_parent_level (parent_level),
  INDEX idx_status (status)
);

-- 插入根节点
INSERT IGNORE INTO administrative_divisions (id, parent_id, name, short_name, single_name, level, sort, status) VALUES
('root', NULL, '国家考试院', '国家考试院', '国', 'root', 0, 1);

-- 插入省份数据
INSERT IGNORE INTO administrative_divisions (id, parent_id, name, short_name, single_name, level, sort, status) VALUES
('11', 'root', '北京市', '北京', '京', 'province', 2, 1),
('31', 'root', '上海市', '上海', '沪', 'province', 3, 1),
('44', 'root', '广东省', '广东', '粤', 'province', 4, 1),
('51', 'root', '四川省', '四川', '川', 'province', 1, 1),
('52', 'root', '贵州省', '贵州', '贵', 'province', 23, 1),
('62', 'root', '海南省', '海南', '海', 'province', 25, 1);

-- 插入城市数据
INSERT IGNORE INTO administrative_divisions (id, parent_id, name, short_name, single_name, level, sort, status) VALUES
('110108', '11', '海淀区', '海淀', NULL, 'district', 1, 1),
('4401', '44', '广州市', '广州', NULL, 'city', 1, 1),
('4403', '44', '深圳市', '深圳', NULL, 'city', 2, 1),
('5101', '51', '成都市', '成都', NULL, 'city', 1, 1),
('5107', '51', '绵阳市', '绵阳', NULL, 'city', 2, 1);

-- 插入区县数据
INSERT IGNORE INTO administrative_divisions (id, parent_id, name, short_name, single_name, level, sort, status) VALUES
('440106', '4401', '天河区', '天河', NULL, 'district', 1, 1),
('510104', '5101', '锦江区', '锦江', NULL, 'district', 1, 1),
('510105', '5101', '青羊区', '青羊', NULL, 'district', 2, 1),
('510106', '5101', '金牛区', '金牛', NULL, 'district', 3, 1);

-- 插入考点数据
INSERT IGNORE INTO exam_sites (id, parent_id, parent_level, full_name, short_name, uri, address, contact_person, contact_phone, capacity, sort, status) VALUES
('11001', '11', 'province', '北京师范大学附属实验中学', '北师大实验', 'bsd.bjjy.cnjy', '北京市西城区二龙路甲14号', '李校长', '010-66036431', 4500, 1, 1),
('110108001', '110108', 'district', '中国人民大学附属中学', '人大附中', 'rdfz.hdjy.bjjy.jyjy.cnjy', '北京市海淀区中关村大街37号', '张校长', '010-62512094', 3000, 1, 1),
('44001', '44', 'province', '华南师范大学附属中学', '华师附中', 'hsfz.gdjy.cnjy', '广州市天河区中山大道西1号', '王校长', '020-38630561', 4800, 1, 1),
('440106001', '440106', 'district', '华南师范大学附属中学', '华师附中', 'hsfz.thjy.gzjy.gdjy.cnjy', '广州市天河区中山大道西1号', '马校长', '020-38630561', 3200, 1, 1),
('4401001', '4401', 'city', '广东实验中学', '省实', 's.gzjy.gdjy.cnjy', '广州市荔湾区龙溪大道省实路1号', '刘校长', '020-81505520', 3800, 1, 1),
('51001', '51', 'province', '四川师范大学附属中学', '川师附中', 'csfz.scjy.cnjy', '成都市锦江区静安路5号', '张校长', '028-84760193', 5000, 1, 1),
('5101001', '5101', 'city', '成都外国语学校', '成外', 'cw.cdjy.scjy.cnjy', '成都市高新区百草路35号', '陈校长', '028-68501880', 3500, 1, 1),
('510104001', '510104', 'district', '成都市第七中学', '成都七中', 'cdqz.jjjy.cdjy.scjy.cnjy', '成都市锦江区林荫中街1号', '张主任', '028-85442231', 2000, 1, 1),
('510105001', '510105', 'district', '成都市石室中学', '石室中学', 'ss.qyjy.cdjy.scjy.cnjy', '成都市青羊区文庙前街93号', '赵主任', '028-86111271', 2200, 1, 1),
('510105002', '510105', 'district', '成都树德中学光华校区', '树德光华', 'sdgh.qyjy.cdjy.scjy.cnjy', NULL, NULL, NULL, 0, 2, 1),
('510106001', '510106', 'district', '成都树德中学宁夏校区', '树德宁夏', 'sdn.jnjy.cdjy.scjy.cnjy', NULL, NULL, NULL, 0, 1, 1),
('5107001', '5107', 'city', '绵阳中学实验学校', '绵中实验', 'mz.myjy.scjy.cnjy', '绵阳市涪城区石塘路62号', '赵校长', '0816-2312069', 2500, 1, 1);

-- 创建OSD规则表
CREATE TABLE IF NOT EXISTS osd_rules (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  type ENUM('time', 'osd') DEFAULT 'osd',
  format TEXT NOT NULL,
  description TEXT,
  preview VARCHAR(500),
  status TINYINT DEFAULT 1,
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_type (type),
  INDEX idx_status (status)
);

-- 插入OSD规则数据
INSERT IGNORE INTO osd_rules (id, name, type, format, description, preview, status) VALUES
(1, '完整OSD格式', 'osd', '[省份][地市][区县][考点名称][考点编号] 考场[考场号]', '包含完整地理信息的OSD格式', '浙江省杭州西湖第一考点KD001 考场01', 1),
(2, '简化OSD格式', 'osd', '[省份简称][地市][考点名称] 考场[考场号]', '简化的OSD格式，减少显示内容', '浙江杭州第一考点 考场01', 1),
(3, '代码格式OSD', 'osd', '[省份代码][地市][区县][考点编号] 考场[考场号]', '使用省份代码的OSD格式', '33杭州西湖KD001 考场01', 1),
(4, '单字简称格式', 'osd', '[省份单字简称][地市][考点名称] 考场[考场号]', '使用省份单字简称的OSD格式', '浙杭州第一考点 考场01', 1),
(5, '精简格式', 'osd', '[考点名称] 考场[考场号]', '最简化的OSD格式', '第一考点 考场01', 1);

-- 创建批量OSD计划表
CREATE TABLE IF NOT EXISTS batch_osd_plans (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(200) NOT NULL,
  description TEXT,
  rule_id INT NOT NULL,
  target_type ENUM('all', 'province', 'city', 'district', 'custom') DEFAULT 'all',
  target_ids JSON,
  execute_mode ENUM('immediate', 'scheduled', 'periodic') DEFAULT 'immediate',
  schedule_time DATETIME NULL,
  concurrency INT DEFAULT 3,
  target_count INT DEFAULT 0,
  completed_count INT DEFAULT 0,
  success_count INT DEFAULT 0,
  failed_count INT DEFAULT 0,
  status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
  start_time DATETIME NULL,
  end_time DATETIME NULL,
  created_by VARCHAR(50) DEFAULT 'admin',
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_status (status),
  INDEX idx_created_by (created_by),
  FOREIGN KEY (rule_id) REFERENCES osd_rules(id)
);

-- 插入示例批量计划
INSERT IGNORE INTO batch_osd_plans (name, description, rule_id, target_type, target_ids, execute_mode, concurrency, target_count, completed_count, success_count, failed_count, status, start_time, end_time, created_by) VALUES
('全省考点OSD统一设置', '为全省所有考点统一设置OSD显示格式', 2, 'all', '[]', 'immediate', 5, 150, 150, 148, 2, 'completed', '2024-03-21 09:30:00', '2024-03-21 10:15:00', 'admin'),
('成都市考点OSD更新', '更新成都市所有考点的OSD格式为新标准', 2, 'city', '["5101"]', 'immediate', 3, 45, 32, 30, 2, 'running', '2024-03-21 14:30:00', NULL, 'admin'),
('重点考点OSD配置', '为重点考点配置特殊的OSD显示格式', 5, 'custom', '["510104001", "510105002", "440106001"]', 'scheduled', 2, 3, 0, 0, 0, 'pending', NULL, NULL, 'admin');
