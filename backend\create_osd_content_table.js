const mysql = require('mysql2/promise');

async function createOsdContentTable() {
  let connection;
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'Aiwa75210!',
      database: 'patrol_system'
    });
    
    console.log('✅ 数据库连接成功');
    
    // 创建OSD内容存储表
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS channel_osd_content (
        id INT AUTO_INCREMENT PRIMARY KEY,
        channel_id VARCHAR(100) NOT NULL COMMENT '通道ID',
        site_id VARCHAR(50) DEFAULT NULL COMMENT '考点ID',
        site_name VARCHAR(200) DEFAULT NULL COMMENT '考点名称',
        room_id VARCHAR(50) DEFAULT NULL COMMENT '考场ID',
        room_name VARCHAR(100) DEFAULT NULL COMMENT '考场名称',
        original_osd TEXT DEFAULT NULL COMMENT '原始OSD内容',
        current_osd TEXT DEFAULT NULL COMMENT '当前OSD内容',
        backup_osd TEXT DEFAULT NULL COMMENT '备份OSD内容',
        plan_id INT DEFAULT NULL COMMENT '关联的计划ID',
        last_update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
        create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        UNIQUE KEY unique_channel (channel_id),
        INDEX idx_site_id (site_id),
        INDEX idx_plan_id (plan_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通道OSD内容表'
    `;
    
    await connection.execute(createTableSQL);
    console.log('✅ channel_osd_content 表创建成功');
    
    // 检查表结构
    const [result] = await connection.execute('DESCRIBE channel_osd_content');
    console.log('\n📋 表结构:');
    result.forEach(field => {
      console.log(`  ${field.Field}: ${field.Type} ${field.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${field.Default ? `DEFAULT ${field.Default}` : ''}`);
    });
    
    // 插入一些示例数据
    const sampleData = [
      {
        channel_id: 'channel_510104001_1',
        site_id: '510104001',
        site_name: '成都市第七中学',
        room_id: '510104001_room_001',
        room_name: '教学楼A-101教室',
        original_osd: '教学楼A-101教室-前门',
        current_osd: '教学楼A-101教室-前门'
      },
      {
        channel_id: 'channel_510104001_2',
        site_id: '510104001',
        site_name: '成都市第七中学',
        room_id: '510104001_room_001',
        room_name: '教学楼A-101教室',
        original_osd: '教学楼A-101教室-后门',
        current_osd: '教学楼A-101教室-后门'
      },
      {
        channel_id: 'channel_510105001_1',
        site_id: '510105001',
        site_name: '成都市石室中学',
        room_id: '510105001_room_001',
        room_name: '教学楼B-201教室',
        original_osd: '教学楼B-201教室-前门',
        current_osd: '教学楼B-201教室-前门'
      }
    ];
    
    for (const data of sampleData) {
      await connection.execute(`
        INSERT INTO channel_osd_content 
        (channel_id, site_id, site_name, room_id, room_name, original_osd, current_osd) 
        VALUES (?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        site_name = VALUES(site_name),
        room_name = VALUES(room_name),
        original_osd = VALUES(original_osd),
        current_osd = VALUES(current_osd)
      `, [data.channel_id, data.site_id, data.site_name, data.room_id, data.room_name, data.original_osd, data.current_osd]);
    }
    
    console.log('✅ 示例数据插入成功');
    
    // 查看插入的数据
    const [rows] = await connection.execute('SELECT * FROM channel_osd_content LIMIT 5');
    console.log('\n📝 示例数据:');
    rows.forEach((row, index) => {
      console.log(`  记录 ${index + 1}: ${row.channel_id} - ${row.current_osd}`);
    });
    
  } catch (error) {
    console.error('❌ 操作失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

createOsdContentTable();
