const mysql = require('mysql2/promise');

async function createTablesOnly() {
  let connection;
  try {
    // 尝试多种连接方式
    const connectionConfigs = [
      {
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: 'Aiwa75210!',
        authPlugins: {
          mysql_native_password: () => require('mysql2/lib/auth_plugins').mysql_native_password
        }
      },
      {
        host: '127.0.0.1',
        port: 3306,
        user: 'root',
        password: 'Aiwa75210!'
      },
      {
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: 'Aiwa75210!',
        ssl: false
      }
    ];

    let connected = false;
    for (const config of connectionConfigs) {
      try {
        console.log(`尝试连接: ${config.user}@${config.host}:${config.port || 3306}`);
        connection = await mysql.createConnection(config);
        connected = true;
        console.log('✅ MySQL连接成功');
        break;
      } catch (error) {
        console.log(`❌ 连接失败: ${error.message}`);
      }
    }

    if (!connected) {
      throw new Error('所有连接方式都失败了');
    }


    // 创建数据库
    console.log('创建数据库 patrol_system...');
    await connection.execute('CREATE DATABASE IF NOT EXISTS patrol_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');
    await connection.execute('USE patrol_system');

    // 创建OSD规则表
    console.log('创建OSD规则表...');
    const createOsdRulesTable = `
      CREATE TABLE IF NOT EXISTS osd_rules (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL COMMENT '规则名称',
        template TEXT NOT NULL COMMENT 'OSD模板',
        description TEXT COMMENT '规则描述',
        is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        created_by VARCHAR(100) COMMENT '创建人',
        updated_by VARCHAR(100) COMMENT '更新人'
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OSD设置规则表'
    `;
    await connection.execute(createOsdRulesTable);

    // 创建批量OSD计划表
    console.log('创建批量OSD计划表...');
    const createBatchOsdPlansTable = `
      CREATE TABLE IF NOT EXISTS batch_osd_plans (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL COMMENT '计划名称',
        description TEXT COMMENT '计划描述',
        osd_rule_id INT COMMENT 'OSD规则ID',
        target_sites JSON COMMENT '目标考点列表',
        status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending' COMMENT '执行状态',
        progress INT DEFAULT 0 COMMENT '执行进度(0-100)',
        total_count INT DEFAULT 0 COMMENT '总任务数',
        completed_count INT DEFAULT 0 COMMENT '已完成数',
        failed_count INT DEFAULT 0 COMMENT '失败数',
        start_time TIMESTAMP NULL COMMENT '开始时间',
        end_time TIMESTAMP NULL COMMENT '结束时间',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        created_by VARCHAR(100) COMMENT '创建人',
        FOREIGN KEY (osd_rule_id) REFERENCES osd_rules(id) ON DELETE SET NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量OSD设置计划表'
    `;
    await connection.execute(createBatchOsdPlansTable);

    // 创建批量OSD任务表
    console.log('创建批量OSD任务表...');
    const createBatchOsdTasksTable = `
      CREATE TABLE IF NOT EXISTS batch_osd_tasks (
        id INT AUTO_INCREMENT PRIMARY KEY,
        plan_id INT NOT NULL COMMENT '计划ID',
        site_id VARCHAR(100) NOT NULL COMMENT '考点ID',
        site_name VARCHAR(255) NOT NULL COMMENT '考点名称',
        osd_content TEXT COMMENT 'OSD内容',
        status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending' COMMENT '任务状态',
        error_message TEXT COMMENT '错误信息',
        start_time TIMESTAMP NULL COMMENT '开始时间',
        end_time TIMESTAMP NULL COMMENT '结束时间',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        FOREIGN KEY (plan_id) REFERENCES batch_osd_plans(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量OSD设置任务表'
    `;
    await connection.execute(createBatchOsdTasksTable);

    // 插入一些示例OSD规则
    console.log('插入示例OSD规则...');
    const insertOsdRules = `
      INSERT IGNORE INTO osd_rules (id, name, template, description, is_active, created_by) VALUES
      (1, '标准考试OSD格式', '[考点名称] [考场编号] [日期] [时间]', '适用于标准化考试的OSD显示格式', TRUE, 'system'),
      (2, '详细信息OSD格式', '[省份] [地市] [考点名称] [考场编号] [考试科目] [日期] [时间]', '包含详细地理和考试信息的OSD格式', TRUE, 'system'),
      (3, '简洁OSD格式', '[考点简称] [考场号] [时间]', '简洁的OSD显示格式，适用于屏幕空间有限的场景', TRUE, 'system'),
      (4, '完整层级OSD格式', '[省份单字简称] [地市] [区县] [考点名称] [考场编号] [日期] [时间]', '显示完整行政层级的OSD格式', TRUE, 'system')
    `;
    await connection.execute(insertOsdRules);

    // 插入一些示例计划
    console.log('插入示例计划...');
    const insertPlans = `
      INSERT IGNORE INTO batch_osd_plans (id, name, description, osd_rule_id, status, total_count, completed_count, created_by) VALUES
      (1, '全省考点OSD统一设置', '统一设置全省所有考点的OSD显示格式', 1, 'completed', 120, 120, 'admin'),
      (2, '成都市考点OSD更新', '更新成都市所有考点的OSD格式为新标准', 2, 'running', 45, 32, 'admin'),
      (3, '重点考点OSD配置', '为重点考点配置特殊的OSD显示格式', 4, 'pending', 20, 0, 'admin')
    `;
    await connection.execute(insertPlans);

    console.log('✅ 数据库表创建完成！');

    // 验证表创建结果
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('已创建的表:', tables.map(t => Object.values(t)[0]));

    // 检查数据
    const [osdRulesCount] = await connection.execute('SELECT COUNT(*) as count FROM osd_rules');
    const [plansCount] = await connection.execute('SELECT COUNT(*) as count FROM batch_osd_plans');

    console.log(`OSD规则表: ${osdRulesCount[0].count} 条记录`);
    console.log(`批量OSD计划表: ${plansCount[0].count} 条记录`);

  } catch (error) {
    console.error('❌ 创建表失败:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

console.log('开始创建数据库表...\n');
createTablesOnly();
