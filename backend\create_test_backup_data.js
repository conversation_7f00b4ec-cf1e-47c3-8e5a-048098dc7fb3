const { query } = require('./src/config/database');

async function createTestBackupData() {
  try {
    console.log('🔧 创建测试备份数据...');

    // 为计划11创建备份数据
    const backupData11 = [
      {
        plan_id: 11,
        channel_id: 'CH001',
        channel_name: '考场01-摄像头01',
        site_id: '510104001',
        site_name: '成都市第七中学',
        room_id: 'R001',
        room_name: '考场01',
        original_osd_content: '成都市第七中学 考场01',
        backup_strategy: 'auto',
        retention_days: 30,
        status: 'backed_up'
      },
      {
        plan_id: 11,
        channel_id: 'CH002',
        channel_name: '考场02-摄像头01',
        site_id: '510104001',
        site_name: '成都市第七中学',
        room_id: 'R002',
        room_name: '考场02',
        original_osd_content: '成都市第七中学 考场02',
        backup_strategy: 'auto',
        retention_days: 30,
        status: 'backed_up'
      },
      {
        plan_id: 11,
        channel_id: 'CH003',
        channel_name: '考场01-摄像头01',
        site_id: '510105002',
        site_name: '成都市石室中学',
        room_id: 'R001',
        room_name: '考场01',
        original_osd_content: '成都市石室中学 考场01',
        backup_strategy: 'auto',
        retention_days: 30,
        status: 'backed_up'
      }
    ];

    // 为计划10创建备份数据
    const backupData10 = [
      {
        plan_id: 10,
        channel_id: 'CH004',
        channel_name: '考场01-摄像头01',
        site_id: '510104001',
        site_name: '成都市第七中学',
        room_id: 'R001',
        room_name: '考场01',
        original_osd_content: '成都市第七中学 考场01',
        backup_strategy: 'auto',
        retention_days: 30,
        status: 'backed_up'
      }
    ];

    // 插入备份数据
    const insertSQL = `
      INSERT INTO batch_osd_backup_data
      (plan_id, channel_id, channel_name, site_id, site_name, room_id, room_name,
       original_osd_content, backup_strategy, retention_days, status)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    console.log('插入计划11的备份数据...');
    for (const backup of backupData11) {
      await query(insertSQL, [
        backup.plan_id,
        backup.channel_id,
        backup.channel_name,
        backup.site_id,
        backup.site_name,
        backup.room_id,
        backup.room_name,
        backup.original_osd_content,
        backup.backup_strategy,
        backup.retention_days,
        backup.status
      ]);
    }

    console.log('插入计划10的备份数据...');
    for (const backup of backupData10) {
      await query(insertSQL, [
        backup.plan_id,
        backup.channel_id,
        backup.channel_name,
        backup.site_id,
        backup.site_name,
        backup.room_id,
        backup.room_name,
        backup.original_osd_content,
        backup.backup_strategy,
        backup.retention_days,
        backup.status
      ]);
    }

    console.log('✅ 测试备份数据创建完成');

    // 验证备份数据
    const verifySQL = `
      SELECT plan_id, COUNT(*) as backup_count
      FROM batch_osd_backup_data
      WHERE plan_id IN (10, 11)
      GROUP BY plan_id
    `;
    const verifyResult = await query(verifySQL);
    console.log('备份数据验证结果:', verifyResult);

  } catch (error) {
    console.error('❌ 创建备份数据失败:', error);
  }
}

createTestBackupData().then(() => {
  console.log('🎉 备份数据创建脚本执行完成');
  process.exit(0);
}).catch(error => {
  console.error('💥 备份数据创建脚本执行失败:', error);
  process.exit(1);
}); 