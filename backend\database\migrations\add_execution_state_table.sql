-- 创建批量OSD执行状态快照表
CREATE TABLE IF NOT EXISTS batch_osd_execution_states (
  id INT AUTO_INCREMENT PRIMARY KEY,
  plan_id INT NOT NULL COMMENT '计划ID',
  state_type ENUM('initial', 'progress', 'completed') DEFAULT 'progress' COMMENT '状态类型',
  execution_phase ENUM('osd_setting', 'osd_restore') DEFAULT 'osd_setting' COMMENT '执行阶段',
  
  -- 执行统计信息
  total_count INT DEFAULT 0 COMMENT '总数',
  completed_count INT DEFAULT 0 COMMENT '已完成数',
  success_count INT DEFAULT 0 COMMENT '成功数',
  failed_count INT DEFAULT 0 COMMENT '失败数',
  current_count INT DEFAULT 0 COMMENT '当前处理数',
  
  -- 执行状态
  is_executing BOOLEAN DEFAULT FALSE COMMENT '是否正在执行',
  is_completed BOOLEAN DEFAULT FALSE COMMENT '是否已完成',
  is_starting BOOLEAN DEFAULT FALSE COMMENT '是否正在启动',
  
  -- 机构考点树状态
  sites_tree_data JSON COMMENT '机构考点树数据',
  current_selected_site JSON COMMENT '当前选中的考点',
  
  -- OSD执行列表状态
  osd_execution_list JSON COMMENT 'OSD执行列表数据',
  
  -- 并发配置
  concurrency_config INT DEFAULT 3 COMMENT '并发数配置',
  
  -- 时间戳
  snapshot_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '快照时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- 索引
  INDEX idx_plan_id (plan_id),
  INDEX idx_state_type (state_type),
  INDEX idx_execution_phase (execution_phase),
  INDEX idx_snapshot_time (snapshot_time),
  
  -- 外键约束
  FOREIGN KEY (plan_id) REFERENCES batch_osd_plans(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量OSD执行状态快照表';

-- 创建索引以提高查询性能
CREATE INDEX idx_plan_execution_phase ON batch_osd_execution_states(plan_id, execution_phase);
CREATE INDEX idx_plan_state_type ON batch_osd_execution_states(plan_id, state_type);
