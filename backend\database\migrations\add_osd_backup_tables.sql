-- 创建OSD备份数据表
CREATE TABLE IF NOT EXISTS batch_osd_backup_data (
  id INT AUTO_INCREMENT PRIMARY KEY,
  plan_id INT NOT NULL,
  channel_id VARCHAR(50) NOT NULL,
  channel_name VARCHAR(100),
  site_id VARCHAR(50),
  site_name VA<PERSON>HAR(100),
  room_id VARCHAR(50),
  room_name VARCHAR(100),
  original_osd_content TEXT,
  backup_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  backup_strategy ENUM('auto', 'manual') DEFAULT 'auto',
  retention_days INT DEFAULT 30,
  status ENUM('backed_up', 'restored', 'restore_failed', 'expired') DEFAULT 'backed_up',
  restore_time TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_plan_id (plan_id),
  INDEX idx_channel_id (channel_id),
  INDEX idx_backup_time (backup_time),
  INDEX idx_status (status),
  FOREIGN KEY (plan_id) REFERENCES batch_osd_plans(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OSD备份数据表';

-- 更新计划配置表，添加备份配置支持
ALTER TABLE batch_osd_plan_configs 
MODIFY COLUMN config_type ENUM('osd_config', 'room_config', 'schedule_config', 'backup_config') NOT NULL;

-- 添加备份相关的执行日志类型
ALTER TABLE batch_osd_execution_logs 
MODIFY COLUMN execution_type ENUM('site', 'room', 'channel', 'restore') DEFAULT 'site';

-- 创建备份数据清理的定时任务表（可选）
CREATE TABLE IF NOT EXISTS batch_osd_backup_cleanup_jobs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  plan_id INT,
  cleanup_type ENUM('expired', 'manual', 'plan_deleted') DEFAULT 'expired',
  cleanup_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  deleted_count INT DEFAULT 0,
  status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending',
  error_message TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_plan_id (plan_id),
  INDEX idx_cleanup_time (cleanup_time),
  INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OSD备份清理任务表';
