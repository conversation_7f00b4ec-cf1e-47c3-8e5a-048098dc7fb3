const { query } = require('./src/config/database');

async function debugBackupConfig() {
  try {
    console.log('🔍 调试备份配置...');

    // 检查计划11的配置
    const configs = await query(`
      SELECT config_type, config_data
      FROM batch_osd_plan_configs
      WHERE plan_id = 11
    `);

    console.log('计划11的配置数据:');
    for (const config of configs) {
      console.log(`${config.config_type}: ${config.config_data}`);
      
      if (config.config_type === 'backup_config') {
        try {
          const parsed = JSON.parse(config.config_data);
          console.log('解析后的备份配置:', parsed);
          console.log('enableBackup:', parsed.enableBackup);
          
          // 测试JSON_EXTRACT
          const extractResult = await query(`
            SELECT JSON_EXTRACT(?, '$.enableBackup') as extracted
          `, [config.config_data]);
          console.log('JSON_EXTRACT结果:', extractResult[0].extracted);
          console.log('JSON_EXTRACT结果类型:', typeof extractResult[0].extracted);
          console.log('JSON_EXTRACT结果 === true:', extractResult[0].extracted === true);
          console.log('JSON_EXTRACT结果 === "true":', extractResult[0].extracted === "true");
          
        } catch (error) {
          console.error('解析失败:', error);
        }
      }
    }

    // 检查所有备份配置
    const allBackupConfigs = await query(`
      SELECT plan_id, config_data, LENGTH(config_data) as data_length
      FROM batch_osd_plan_configs
      WHERE config_type = 'backup_config'
    `);
    console.log('\n所有备份配置:');
    for (const config of allBackupConfigs) {
      console.log(`计划ID: ${config.plan_id}, 配置: ${config.config_data}, 长度: ${config.data_length}`);
      console.log(`数据类型: ${typeof config.config_data}`);
      
      if (typeof config.config_data === 'string') {
        try {
          const parsed = JSON.parse(config.config_data);
          console.log(`  解析后: ${JSON.stringify(parsed)}`);
        } catch (error) {
          console.log(`  解析失败: ${error.message}`);
        }
      }
    }

    // 测试SQL查询
    const sqlTest = await query(`
      SELECT 
        CASE WHEN EXISTS(
          SELECT 1 FROM batch_osd_plan_configs pc
          WHERE pc.plan_id = 11 AND pc.config_type = 'backup_config'
          AND JSON_EXTRACT(pc.config_data, '$.enableBackup') = true
        ) THEN 1 ELSE 0 END as has_backup_enabled
    `);
    console.log('SQL查询结果:', sqlTest[0]);

    // 直接测试EXISTS查询
    const existsTest = await query(`
      SELECT EXISTS(
        SELECT 1 FROM batch_osd_plan_configs pc
        WHERE pc.plan_id = 11 AND pc.config_type = 'backup_config'
        AND JSON_EXTRACT(pc.config_data, '$.enableBackup') = true
      ) as exists_result
    `);
    console.log('EXISTS查询结果:', existsTest[0]);

    // 测试子查询
    const subQueryTest = await query(`
      SELECT 1 as result
      FROM batch_osd_plan_configs pc
      WHERE pc.plan_id = 11 AND pc.config_type = 'backup_config'
      AND JSON_EXTRACT(pc.config_data, '$.enableBackup') = true
    `);
    console.log('子查询结果:', subQueryTest);

  } catch (error) {
    console.error('调试失败:', error);
  }
}

debugBackupConfig().then(() => {
  console.log('✅ 调试完成');
  process.exit(0);
}).catch(error => {
  console.error('❌ 调试失败:', error);
  process.exit(1);
}); 