const { query } = require('./src/config/database');

async function debugDatabase() {
  try {
    console.log('=== 检查批量OSD计划数据 ===');
    
    // 1. 检查计划表
    const plans = await query('SELECT * FROM batch_osd_plans ORDER BY id DESC LIMIT 5');
    console.log('计划数据:', plans);
    
    if (plans.length > 0) {
      const planId = plans[0].id;
      console.log(`\n=== 检查计划 ${planId} 的相关数据 ===`);
      
      // 2. 检查计划考点数据
      const planSites = await query('SELECT * FROM batch_osd_plan_sites WHERE plan_id = ?', [planId]);
      console.log('计划考点数据:', planSites);
      
      // 3. 检查房间通道绑定数据
      const bindings = await query('SELECT * FROM batch_osd_room_channel_bindings WHERE plan_id = ? LIMIT 10', [planId]);
      console.log('房间通道绑定数据:', bindings);
      
      // 4. 检查生成的考场数据
      const rooms = await query('SELECT * FROM batch_osd_generated_rooms WHERE plan_id = ? LIMIT 10', [planId]);
      console.log('生成的考场数据:', rooms);
    }
    
  } catch (error) {
    console.error('数据库查询失败:', error);
  }
  
  process.exit(0);
}

debugDatabase();
