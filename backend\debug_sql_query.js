const { query } = require('./src/config/database');

async function debugSqlQuery() {
  try {
    console.log('🔍 调试SQL查询...');

    // 测试计划13的配置
    const configs = await query(`
      SELECT config_type, config_data
      FROM batch_osd_plan_configs
      WHERE plan_id = 13
    `);

    console.log('计划13的配置数据:');
    for (const config of configs) {
      console.log(`${config.config_type}: ${config.config_data}`);
      
      if (config.config_type === 'backup_config') {
        try {
          const parsed = JSON.parse(config.config_data);
          console.log('解析后的备份配置:', parsed);
          console.log('enableBackup:', parsed.enableBackup);
          
          // 测试JSON_EXTRACT
          const extractResult = await query(`
            SELECT JSON_EXTRACT(?, '$.enableBackup') as extracted
          `, [config.config_data]);
          console.log('JSON_EXTRACT结果:', extractResult[0].extracted);
          
        } catch (error) {
          console.error('解析失败:', error);
        }
      }
    }

    // 测试完整的SQL查询
    const sqlTest = await query(`
      SELECT 
        CASE WHEN EXISTS(
          SELECT 1 FROM batch_osd_plan_configs pc
          WHERE pc.plan_id = 13 AND pc.config_type = 'backup_config'
          AND JSON_EXTRACT(CAST(pc.config_data AS JSON), '$.enableBackup') = true
        ) THEN 1 ELSE 0 END as has_backup_enabled
    `);
    console.log('完整SQL查询结果:', sqlTest[0]);

    // 测试子查询
    const subQueryTest = await query(`
      SELECT 1 as result
      FROM batch_osd_plan_configs pc
      WHERE pc.plan_id = 13 AND pc.config_type = 'backup_config'
      AND JSON_EXTRACT(CAST(pc.config_data AS JSON), '$.enableBackup') = true
    `);
    console.log('子查询结果:', subQueryTest);

    // 测试EXISTS查询
    const existsTest = await query(`
      SELECT EXISTS(
        SELECT 1 FROM batch_osd_plan_configs pc
        WHERE pc.plan_id = 13 AND pc.config_type = 'backup_config'
        AND JSON_EXTRACT(CAST(pc.config_data AS JSON), '$.enableBackup') = true
      ) as exists_result
    `);
    console.log('EXISTS查询结果:', existsTest[0]);

    // 测试备份数据查询
    const backupTest = await query(`
      SELECT 
        CASE WHEN EXISTS(
          SELECT 1 FROM batch_osd_backup_data bd
          WHERE bd.plan_id = 13 AND bd.status IN ('backed_up', 'restore_failed')
        ) THEN 1 ELSE 0 END as has_backup_data
    `);
    console.log('备份数据查询结果:', backupTest[0]);

    // 测试完整的hasBackup查询
    const hasBackupTest = await query(`
      SELECT 
        CASE WHEN EXISTS(
          SELECT 1 FROM batch_osd_plan_configs pc
          WHERE pc.plan_id = 13 AND pc.config_type = 'backup_config'
          AND JSON_EXTRACT(CAST(pc.config_data AS JSON), '$.enableBackup') = true
        ) AND EXISTS(
          SELECT 1 FROM batch_osd_backup_data bd
          WHERE bd.plan_id = 13 AND bd.status IN ('backed_up', 'restore_failed')
        ) THEN 1 ELSE 0 END as hasBackup
    `);
    console.log('完整hasBackup查询结果:', hasBackupTest[0]);

    // 测试数据存在性
    const dataExistsTest = await query(`
      SELECT plan_id, config_type, config_data
      FROM batch_osd_plan_configs
      WHERE plan_id = 13 AND config_type = 'backup_config'
    `);
    console.log('数据存在性检查:', dataExistsTest);

    // 测试JSON_EXTRACT在子查询中
    const jsonExtractInSubquery = await query(`
      SELECT plan_id, config_type, JSON_EXTRACT(config_data, '$.enableBackup') as extracted_value
      FROM batch_osd_plan_configs
      WHERE plan_id = 13 AND config_type = 'backup_config'
    `);
    console.log('子查询中的JSON_EXTRACT:', jsonExtractInSubquery);

    // 测试数据类型
    const dataTypeTest = await query(`
      SELECT plan_id, config_type, 
             config_data,
             JSON_TYPE(config_data) as json_type,
             JSON_VALID(config_data) as is_valid_json
      FROM batch_osd_plan_configs
      WHERE plan_id = 13 AND config_type = 'backup_config'
    `);
    console.log('数据类型检查:', dataTypeTest);

    // 测试直接比较
    const directComparison = await query(`
      SELECT plan_id, config_type, 
             JSON_EXTRACT(config_data, '$.enableBackup') as extracted,
             JSON_EXTRACT(config_data, '$.enableBackup') = true as is_true,
             JSON_EXTRACT(config_data, '$.enableBackup') = 'true' as is_string_true
      FROM batch_osd_plan_configs
      WHERE plan_id = 13 AND config_type = 'backup_config'
    `);
    console.log('直接比较测试:', directComparison);

    // 测试CAST语法
    const castTest = await query(`
      SELECT 
        CAST(config_data AS JSON) as casted_json,
        JSON_EXTRACT(CAST(config_data AS JSON), '$.enableBackup') as extracted_from_cast
      FROM batch_osd_plan_configs
      WHERE plan_id = 13 AND config_type = 'backup_config'
    `);
    console.log('CAST语法测试:', castTest);

  } catch (error) {
    console.error('调试失败:', error);
  }
}

debugSqlQuery().then(() => {
  console.log('✅ 调试完成');
  process.exit(0);
}).catch(error => {
  console.error('❌ 调试失败:', error);
  process.exit(1);
}); 