const mysql = require('mysql2/promise');

async function diagnoseMySQLIssue() {
  console.log('=== MySQL连接问题诊断 ===\n');
  
  // 测试不同的连接方式
  const testConfigs = [
    {
      name: '标准连接 - 指定数据库',
      config: {
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: 'Aiwa75210！',
        database: 'patrol_system'
      }
    },
    {
      name: '标准连接 - 不指定数据库',
      config: {
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: 'Aiwa75210！'
      }
    },
    {
      name: '127.0.0.1连接 - 不指定数据库',
      config: {
        host: '127.0.0.1',
        port: 3306,
        user: 'root',
        password: 'Aiwa75210！'
      }
    },
    {
      name: '禁用SSL连接',
      config: {
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: 'Aiwa75210！',
        ssl: false
      }
    },
    {
      name: '使用连接超时设置',
      config: {
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: 'Aiwa75210！',
        connectTimeout: 10000,
        acquireTimeout: 10000
      }
    }
  ];
  
  let successfulConnection = null;
  
  for (const { name, config } of testConfigs) {
    console.log(`🔍 测试: ${name}`);
    console.log(`   配置: ${JSON.stringify(config, null, 2)}`);
    
    try {
      const connection = await mysql.createConnection(config);
      console.log('   ✅ 连接成功！');
      
      // 获取连接信息
      const [connectionInfo] = await connection.execute('SELECT CONNECTION_ID() as id, USER() as user, @@hostname as hostname');
      console.log(`   📊 连接ID: ${connectionInfo[0].id}`);
      console.log(`   👤 当前用户: ${connectionInfo[0].user}`);
      console.log(`   🖥️  主机名: ${connectionInfo[0].hostname}`);
      
      // 检查用户权限
      const [userInfo] = await connection.execute('SELECT USER(), CURRENT_USER()');
      console.log(`   🔐 用户信息: ${JSON.stringify(userInfo[0])}`);
      
      // 检查数据库列表
      const [databases] = await connection.execute('SHOW DATABASES');
      const dbNames = databases.map(db => Object.values(db)[0]);
      console.log(`   📁 可访问数据库: ${dbNames.join(', ')}`);
      
      // 检查patrol_system数据库
      if (dbNames.includes('patrol_system')) {
        console.log('   ✅ patrol_system数据库存在');
        
        try {
          await connection.execute('USE patrol_system');
          const [tables] = await connection.execute('SHOW TABLES');
          const tableNames = tables.map(t => Object.values(t)[0]);
          console.log(`   📋 patrol_system中的表: ${tableNames.join(', ')}`);
          
          // 检查关键表的数据
          const keyTables = ['administrative_divisions', 'exam_sites', 'osd_rules', 'batch_osd_plans'];
          for (const tableName of keyTables) {
            if (tableNames.includes(tableName)) {
              try {
                const [countResult] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName}`);
                console.log(`   📊 ${tableName}: ${countResult[0].count} 条记录`);
              } catch (error) {
                console.log(`   ❌ ${tableName}: 查询失败 - ${error.message}`);
              }
            } else {
              console.log(`   ⚠️  缺少表: ${tableName}`);
            }
          }
        } catch (error) {
          console.log(`   ❌ 无法使用patrol_system数据库: ${error.message}`);
        }
      } else {
        console.log('   ⚠️  patrol_system数据库不存在');
      }
      
      await connection.end();
      successfulConnection = { name, config };
      console.log('   🎉 测试完成\n');
      break;
      
    } catch (error) {
      console.log(`   ❌ 连接失败: ${error.message}`);
      console.log(`   🔍 错误代码: ${error.code || 'N/A'}`);
      console.log(`   🔍 错误号: ${error.errno || 'N/A'}`);
      console.log('');
    }
  }
  
  if (successfulConnection) {
    console.log('🎯 找到可用的连接配置:');
    console.log(JSON.stringify(successfulConnection, null, 2));
    return successfulConnection;
  } else {
    console.log('❌ 所有连接配置都失败了');
    
    console.log('\n🔍 可能的原因分析:');
    console.log('1. MySQL服务重启后用户权限发生变化');
    console.log('2. MySQL配置文件被修改，影响了root用户权限');
    console.log('3. MySQL安全更新导致localhost连接被限制');
    console.log('4. 防火墙或安全软件阻止了连接');
    console.log('5. MySQL用户表损坏或被修改');
    console.log('6. 密码中的特殊字符在某些情况下被转义');
    
    console.log('\n💡 建议的解决步骤:');
    console.log('1. 检查MySQL错误日志');
    console.log('2. 尝试使用MySQL命令行工具直接连接');
    console.log('3. 检查MySQL用户表中的root用户配置');
    console.log('4. 考虑重置MySQL root密码');
    console.log('5. 检查MySQL配置文件中的bind-address设置');
    
    return null;
  }
}

// 额外的系统诊断
async function systemDiagnosis() {
  console.log('\n=== 系统环境诊断 ===');
  
  try {
    // 检查MySQL进程
    console.log('🔍 检查MySQL进程...');
    // 这里我们无法直接执行系统命令，但可以尝试连接来验证服务状态
    
    // 检查端口是否开放
    console.log('🔍 检查3306端口...');
    try {
      const testSocket = await mysql.createConnection({
        host: 'localhost',
        port: 3306,
        user: 'nonexistent',
        password: 'test',
        connectTimeout: 3000
      });
    } catch (error) {
      if (error.code === 'ER_ACCESS_DENIED_ERROR') {
        console.log('   ✅ 端口3306可访问（收到访问拒绝错误，说明MySQL在监听）');
      } else if (error.code === 'ECONNREFUSED') {
        console.log('   ❌ 端口3306无法连接（MySQL可能未启动）');
      } else {
        console.log(`   ⚠️  端口测试结果: ${error.message}`);
      }
    }
    
  } catch (error) {
    console.log(`系统诊断失败: ${error.message}`);
  }
}

// 运行诊断
async function runFullDiagnosis() {
  await diagnoseMySQLIssue();
  await systemDiagnosis();
}

runFullDiagnosis().catch(console.error);
