// 紧急服务器 - 解决前端API调用问题
const http = require('http');
const url = require('url');

const PORT = 8081;

// Mock数据
const mockOrganizationTree = [
  {
    id: '51',
    name: '四川省',
    fullName: '四川省',
    shortName: '川',
    singleName: '四川',
    level: 1,
    sort: 1,
    status: 1,
    createTime: '2024-01-01 00:00:00',
    updateTime: '2024-01-01 00:00:00',
    children: [
      {
        id: '5101',
        name: '成都市',
        fullName: '成都市',
        shortName: '成都',
        singleName: '成都',
        level: 2,
        sort: 1,
        status: 1,
        createTime: '2024-01-01 00:00:00',
        updateTime: '2024-01-01 00:00:00',
        children: [
          {
            id: '510104',
            name: '锦江区',
            fullName: '锦江区',
            shortName: '锦江',
            singleName: '锦江',
            level: 3,
            sort: 1,
            status: 1,
            createTime: '2024-01-01 00:00:00',
            updateTime: '2024-01-01 00:00:00',
            examSites: [
              {
                id: '510104001',
                site_id: '510104001',
                site_name: '成都市第七中学',
                site_code: 'CD001',
                province_id: '51',
                province_name: '四川省',
                city_id: '5101',
                city_name: '成都市',
                district_id: '510104',
                district_name: '锦江区',
                address: '成都市锦江区林荫街1号',
                contact_person: '张老师',
                contact_phone: '028-12345678',
                room_count: 25,
                status: 1,
                create_time: '2024-01-01 00:00:00',
                update_time: '2024-01-01 00:00:00'
              }
            ]
          }
        ]
      }
    ]
  }
];

const mockOsdRules = [
  {
    id: 1,
    name: '标准考试OSD模板',
    description: '适用于标准化考试的OSD显示模板',
    template_content: '考点：{site_name} 考场：{room_number} 时间：{current_time}',
    font_size: 24,
    font_color: '#FFFFFF',
    background_color: '#000000',
    position_x: 10,
    position_y: 10,
    display_duration: 0,
    is_active: 1,
    create_time: '2024-01-01 00:00:00',
    update_time: '2024-01-01 00:00:00'
  }
];

const mockPlans = [
  {
    id: 1,
    name: '2024年研究生考试OSD设置',
    description: '用于2024年研究生考试的OSD批量设置',
    rule_id: 1,
    rule_name: '标准考试OSD模板',
    target_type: 'custom',
    target_count: 55,
    execute_mode: 'scheduled',
    scheduled_time: '2024-12-20 08:00:00',
    concurrency: 5,
    status: 'completed',
    progress: 100,
    success_count: 55,
    failed_count: 0,
    create_time: '2024-01-15 10:00:00',
    update_time: '2024-01-15 12:00:00',
    creator: 'admin'
  }
];

// 设置CORS头
function setCorsHeaders(res) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
}

// 发送JSON响应
function sendJsonResponse(res, statusCode, data) {
  setCorsHeaders(res);
  res.writeHead(statusCode, { 'Content-Type': 'application/json; charset=utf-8' });
  res.end(JSON.stringify(data, null, 2));
}

// 创建服务器
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;
  const method = req.method;

  console.log(`${new Date().toISOString()} ${method} ${pathname}`);

  // 处理OPTIONS请求
  if (method === 'OPTIONS') {
    setCorsHeaders(res);
    res.writeHead(200);
    res.end();
    return;
  }

  // 健康检查
  if (pathname === '/health') {
    sendJsonResponse(res, 200, {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'patrol-system-emergency-api'
    });
    return;
  }

  // 机构树API
  if (pathname === '/api/organization/tree') {
    console.log('返回Mock机构树数据');
    sendJsonResponse(res, 200, {
      code: 200,
      message: '获取成功',
      data: mockOrganizationTree
    });
    return;
  }

  // OSD规则API
  if (pathname === '/api/osd-rules') {
    console.log('返回Mock OSD规则数据');
    sendJsonResponse(res, 200, {
      code: 200,
      message: '获取成功',
      data: mockOsdRules
    });
    return;
  }

  // 批量OSD计划API
  if (pathname === '/api/batch-osd/plans') {
    console.log('返回Mock批量OSD计划数据');
    sendJsonResponse(res, 200, {
      code: 200,
      message: '获取成功',
      data: {
        records: mockPlans,
        total: mockPlans.length,
        current: 1,
        size: 20
      }
    });
    return;
  }

  // 404处理
  sendJsonResponse(res, 404, {
    code: 404,
    message: '接口不存在',
    path: pathname
  });
});

// 启动服务器
server.listen(PORT, () => {
  console.log('🚀 紧急服务器启动成功!');
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`🏥 健康检查: http://localhost:${PORT}/health`);
  console.log('📊 数据库状态: ❌ Mock模式（无数据库连接）');
  console.log('');
  console.log('📋 可用的Mock API接口:');
  console.log('  - GET  /api/organization/tree          机构树数据');
  console.log('  - GET  /api/osd-rules                  OSD规则列表');
  console.log('  - GET  /api/batch-osd/plans            批量OSD计划列表');
  console.log('');
});

// 错误处理
server.on('error', (err) => {
  console.error('服务器错误:', err);
});

process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...');
  server.close(() => {
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭服务器...');
  server.close(() => {
    process.exit(0);
  });
});

module.exports = server;
