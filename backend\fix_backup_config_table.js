// 修复批量OSD计划配置表，添加backup_config支持
const mysql = require('mysql2/promise');

async function fixBackupConfigTable() {
  let connection;
  
  try {
    console.log('🔧 开始修复批量OSD计划配置表...');
    
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'Aiwa75210!',
      database: 'patrol_system'
    });
    
    console.log('✅ 数据库连接成功');
    
    // 1. 检查当前表结构
    console.log('\n📋 检查当前表结构...');
    try {
      const [columns] = await connection.execute('DESCRIBE batch_osd_plan_configs');
      console.log('当前表结构:');
      columns.forEach(col => {
        console.log(`  - ${col.Field}: ${col.Type} ${col.Null} ${col.Key} ${col.Default || ''}`);
      });
      
      // 检查config_type字段的枚举值
      const configTypeColumn = columns.find(col => col.Field === 'config_type');
      if (configTypeColumn) {
        console.log('当前config_type枚举值:', configTypeColumn.Type);
        
        // 检查是否已经包含backup_config
        if (configTypeColumn.Type.includes('backup_config')) {
          console.log('✅ backup_config类型已存在，无需修复');
          return;
        }
      }
    } catch (error) {
      console.log('❌ 表不存在，需要创建:', error.message);
      
      // 如果表不存在，创建完整的表
      console.log('\n🏗️ 创建批量OSD计划配置表...');
      const createTableSQL = `
        CREATE TABLE batch_osd_plan_configs (
          id INT AUTO_INCREMENT PRIMARY KEY,
          plan_id INT NOT NULL COMMENT '计划ID',
          config_type ENUM('osd_config', 'room_config', 'schedule_config', 'backup_config') NOT NULL COMMENT '配置类型',
          config_data JSON COMMENT '配置数据',
          create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          INDEX idx_plan_id (plan_id),
          INDEX idx_config_type (config_type),
          UNIQUE KEY uk_plan_config_type (plan_id, config_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量OSD计划配置信息表'
      `;
      
      await connection.execute(createTableSQL);
      console.log('✅ 表创建成功');
      return;
    }
    
    // 2. 修改config_type字段，添加backup_config类型
    console.log('\n🔧 修改config_type字段，添加backup_config类型...');
    const alterTableSQL = `
      ALTER TABLE batch_osd_plan_configs 
      MODIFY COLUMN config_type ENUM('osd_config', 'room_config', 'schedule_config', 'backup_config') NOT NULL COMMENT '配置类型'
    `;
    
    await connection.execute(alterTableSQL);
    console.log('✅ config_type字段修改成功');
    
    // 3. 验证修改结果
    console.log('\n🔍 验证修改结果...');
    const [newColumns] = await connection.execute('DESCRIBE batch_osd_plan_configs');
    const newConfigTypeColumn = newColumns.find(col => col.Field === 'config_type');
    if (newConfigTypeColumn) {
      console.log('修改后的config_type枚举值:', newConfigTypeColumn.Type);
      
      if (newConfigTypeColumn.Type.includes('backup_config')) {
        console.log('✅ backup_config类型添加成功');
      } else {
        console.log('❌ backup_config类型添加失败');
      }
    }
    
    // 4. 检查现有数据
    console.log('\n📊 检查现有配置数据...');
    const [configs] = await connection.execute('SELECT plan_id, config_type FROM batch_osd_plan_configs');
    console.log(`现有配置记录数: ${configs.length}`);
    
    const configTypes = {};
    configs.forEach(config => {
      configTypes[config.config_type] = (configTypes[config.config_type] || 0) + 1;
    });
    
    console.log('配置类型统计:');
    Object.entries(configTypes).forEach(([type, count]) => {
      console.log(`  - ${type}: ${count}条`);
    });
    
    console.log('\n🎉 批量OSD计划配置表修复完成！');
    
  } catch (error) {
    console.error('❌ 修复失败:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔗 数据库连接已关闭');
    }
  }
}

// 执行修复
if (require.main === module) {
  fixBackupConfigTable()
    .then(() => {
      console.log('\n✅ 修复脚本执行完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ 修复脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { fixBackupConfigTable };
