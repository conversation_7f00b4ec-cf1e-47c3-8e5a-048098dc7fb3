// 修复缺失的数据库表
const mysql = require('mysql2/promise');

async function fixMissingTables() {
  let connection;
  
  try {
    console.log('🔧 开始修复缺失的数据库表...');
    
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'Aiwa75210!',
      database: 'patrol_system'
    });
    
    console.log('✅ 数据库连接成功');
    
    // 1. 检查现有表
    console.log('\n📋 检查现有表...');
    const [tables] = await connection.execute('SHOW TABLES');
    const tableNames = tables.map(t => Object.values(t)[0]);
    console.log('现有表:', tableNames);
    
    // 2. 修复batch_osd_plan_configs表的config_type字段
    console.log('\n🔧 修复batch_osd_plan_configs表...');
    if (tableNames.includes('batch_osd_plan_configs')) {
      try {
        const alterConfigTableSQL = `
          ALTER TABLE batch_osd_plan_configs 
          MODIFY COLUMN config_type ENUM('osd_config', 'room_config', 'schedule_config', 'backup_config') NOT NULL COMMENT '配置类型'
        `;
        await connection.execute(alterConfigTableSQL);
        console.log('✅ batch_osd_plan_configs表config_type字段修复成功');
      } catch (error) {
        console.log('⚠️ batch_osd_plan_configs表修复失败:', error.message);
      }
    } else {
      // 创建batch_osd_plan_configs表
      console.log('🏗️ 创建batch_osd_plan_configs表...');
      const createConfigTableSQL = `
        CREATE TABLE batch_osd_plan_configs (
          id INT AUTO_INCREMENT PRIMARY KEY,
          plan_id INT NOT NULL COMMENT '计划ID',
          config_type ENUM('osd_config', 'room_config', 'schedule_config', 'backup_config') NOT NULL COMMENT '配置类型',
          config_data JSON COMMENT '配置数据',
          create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          INDEX idx_plan_id (plan_id),
          INDEX idx_config_type (config_type),
          UNIQUE KEY uk_plan_config_type (plan_id, config_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量OSD计划配置信息表'
      `;
      await connection.execute(createConfigTableSQL);
      console.log('✅ batch_osd_plan_configs表创建成功');
    }
    
    // 3. 创建batch_osd_plan_site_room_bindings表
    console.log('\n🏗️ 创建batch_osd_plan_site_room_bindings表...');
    if (!tableNames.includes('batch_osd_plan_site_room_bindings')) {
      const createBindingsTableSQL = `
        CREATE TABLE batch_osd_plan_site_room_bindings (
          id INT AUTO_INCREMENT PRIMARY KEY,
          plan_id INT NOT NULL COMMENT '计划ID',
          site_id VARCHAR(50) NOT NULL COMMENT '考点ID',
          site_name VARCHAR(200) NOT NULL COMMENT '考点名称',
          room_id VARCHAR(50) NOT NULL COMMENT '考场ID',
          room_number VARCHAR(20) NOT NULL COMMENT '考场编号',
          channel_id VARCHAR(50) COMMENT '通道ID',
          channel_name VARCHAR(200) COMMENT '通道名称',
          osd_content TEXT COMMENT 'OSD内容',
          binding_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
          status ENUM('pending', 'bound', 'failed') DEFAULT 'pending' COMMENT '绑定状态',
          create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          INDEX idx_plan_id (plan_id),
          INDEX idx_site_id (site_id),
          INDEX idx_room_id (room_id),
          INDEX idx_channel_id (channel_id),
          INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量OSD计划考点考场绑定关系表'
      `;
      await connection.execute(createBindingsTableSQL);
      console.log('✅ batch_osd_plan_site_room_bindings表创建成功');
    } else {
      console.log('✅ batch_osd_plan_site_room_bindings表已存在');
    }
    
    // 4. 创建batch_osd_backup_data表
    console.log('\n🏗️ 创建batch_osd_backup_data表...');
    if (!tableNames.includes('batch_osd_backup_data')) {
      const createBackupTableSQL = `
        CREATE TABLE batch_osd_backup_data (
          id INT AUTO_INCREMENT PRIMARY KEY,
          plan_id INT NOT NULL COMMENT '计划ID',
          channel_id VARCHAR(50) NOT NULL COMMENT '通道ID',
          channel_name VARCHAR(200) COMMENT '通道名称',
          site_id VARCHAR(50) COMMENT '考点ID',
          site_name VARCHAR(200) COMMENT '考点名称',
          room_id VARCHAR(50) COMMENT '考场ID',
          room_name VARCHAR(100) COMMENT '考场名称',
          original_osd_content TEXT COMMENT '原始OSD内容',
          backup_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '备份时间',
          backup_strategy ENUM('auto', 'manual') DEFAULT 'auto' COMMENT '备份策略',
          retention_days INT DEFAULT 30 COMMENT '保留天数',
          status ENUM('backed_up', 'restored', 'restore_failed', 'expired') DEFAULT 'backed_up' COMMENT '状态',
          restore_time TIMESTAMP NULL COMMENT '恢复时间',
          create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          INDEX idx_plan_id (plan_id),
          INDEX idx_channel_id (channel_id),
          INDEX idx_site_id (site_id),
          INDEX idx_room_id (room_id),
          INDEX idx_status (status),
          INDEX idx_backup_time (backup_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量OSD备份数据表'
      `;
      await connection.execute(createBackupTableSQL);
      console.log('✅ batch_osd_backup_data表创建成功');
    } else {
      console.log('✅ batch_osd_backup_data表已存在');
    }
    
    // 5. 验证修复结果
    console.log('\n🔍 验证修复结果...');
    const [newTables] = await connection.execute('SHOW TABLES');
    const newTableNames = newTables.map(t => Object.values(t)[0]);
    
    const requiredTables = [
      'batch_osd_plan_configs',
      'batch_osd_plan_site_room_bindings',
      'batch_osd_backup_data'
    ];
    
    let allTablesExist = true;
    for (const tableName of requiredTables) {
      if (newTableNames.includes(tableName)) {
        console.log(`✅ ${tableName} 表存在`);
      } else {
        console.log(`❌ ${tableName} 表缺失`);
        allTablesExist = false;
      }
    }
    
    if (allTablesExist) {
      console.log('\n🎉 所有必需的表都已创建成功！');
    } else {
      console.log('\n⚠️ 部分表仍然缺失，请检查错误信息');
    }
    
  } catch (error) {
    console.error('❌ 修复失败:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔗 数据库连接已关闭');
    }
  }
}

// 执行修复
if (require.main === module) {
  fixMissingTables()
    .then(() => {
      console.log('\n✅ 修复脚本执行完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ 修复脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { fixMissingTables };
