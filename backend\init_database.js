const mysql = require('mysql2/promise');
const fs = require('fs');

async function initDatabase() {
  let connection;
  try {
    // 尝试多种连接方式
    const connectionConfigs = [
      {
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: 'Aiwa75210！'
      },
      {
        host: '127.0.0.1',
        port: 3306,
        user: 'root',
        password: 'Aiwa75210！'
      },
      {
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: 'root'
      },
      {
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: ''
      }
    ];
    
    let connected = false;
    for (const config of connectionConfigs) {
      try {
        console.log(`尝试连接: ${config.user}@${config.host}:${config.port}`);
        connection = await mysql.createConnection(config);
        connected = true;
        console.log('✅ MySQL连接成功');
        break;
      } catch (error) {
        console.log(`❌ 连接失败: ${error.message}`);
      }
    }
    
    if (!connected) {
      throw new Error('无法连接到MySQL服务器，请检查MySQL服务是否启动和密码是否正确');
    }
    
    // 读取并执行SQL脚本
    console.log('\n=== 开始执行数据库初始化脚本 ===');
    const sqlScript = fs.readFileSync('./create_database.sql', 'utf8');
    
    // 分割SQL语句
    const statements = sqlScript
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`共有 ${statements.length} 条SQL语句需要执行`);
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.trim()) {
        try {
          await connection.execute(statement);
          console.log(`✅ 执行成功 (${i + 1}/${statements.length}): ${statement.substring(0, 50)}...`);
        } catch (error) {
          // 忽略一些常见的错误（如表已存在、数据已存在等）
          if (error.code === 'ER_TABLE_EXISTS_ERROR' || 
              error.code === 'ER_DB_CREATE_EXISTS' ||
              error.code === 'ER_DUP_ENTRY') {
            console.log(`⚠️  跳过 (${i + 1}/${statements.length}): ${error.message}`);
          } else {
            console.error(`❌ 执行失败 (${i + 1}/${statements.length}): ${error.message}`);
            console.error(`SQL: ${statement}`);
          }
        }
      }
    }
    
    // 验证数据库初始化结果
    console.log('\n=== 验证数据库初始化结果 ===');
    await connection.execute('USE patrol_system');
    
    const [tables] = await connection.execute('SHOW TABLES');
    const tableNames = tables.map(t => Object.values(t)[0]);
    console.log('已创建的表:', tableNames);
    
    // 检查关键表的记录数
    const keyTables = ['administrative_divisions', 'exam_sites', 'osd_rules', 'batch_osd_plans'];
    for (const tableName of keyTables) {
      if (tableNames.includes(tableName)) {
        try {
          const [result] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName}`);
          console.log(`${tableName}: ${result[0].count} 条记录`);
        } catch (error) {
          console.log(`${tableName}: 查询失败 - ${error.message}`);
        }
      } else {
        console.log(`❌ 缺少关键表: ${tableName}`);
      }
    }
    
    console.log('\n✅ 数据库初始化完成！');
    
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 执行初始化
console.log('开始初始化patrol_system数据库...\n');
initDatabase();
