// 初始化测试数据的脚本
const mysql = require('mysql2/promise');

const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: 'Aiwa75210!',
  database: 'patrol_system',
  charset: 'utf8mb4'
};

async function initTestData() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功');

    // 删除并重新创建行政区划表（支持root级别）
    await connection.execute('DROP TABLE IF EXISTS administrative_divisions');
    await connection.execute(`
      CREATE TABLE administrative_divisions (
        id VARCHAR(20) PRIMARY KEY COMMENT '行政区划代码',
        parent_id VARCHAR(20) COMMENT '上级行政区划代码',
        name VARCHAR(200) NOT NULL COMMENT '行政区划名称',
        short_name VARCHAR(100) COMMENT '简称',
        single_name VARCHAR(20) COMMENT '单字简称（省份专用）',
        level ENUM('root', 'province', 'city', 'district') NOT NULL COMMENT '行政级别',
        sort_order INT DEFAULT 0 COMMENT '排序',
        status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='行政区划表'
    `);

    // 删除旧的考点表并创建新结构
    await connection.execute('DROP TABLE IF EXISTS exam_sites');
    await connection.execute(`
      CREATE TABLE exam_sites (
        id VARCHAR(20) PRIMARY KEY COMMENT '考点代码',
        parent_id VARCHAR(20) NOT NULL COMMENT '上级机构代码（可以是省、地市或区县）',
        parent_level ENUM('province', 'city', 'district') NOT NULL COMMENT '上级机构级别',
        name VARCHAR(100) NOT NULL COMMENT '考点名称',
        short_name VARCHAR(50) COMMENT '考点简称',
        uri VARCHAR(255) COMMENT '考点URI地址',
        address VARCHAR(255) COMMENT '考点地址',
        contact_person VARCHAR(50) COMMENT '联系人',
        contact_phone VARCHAR(20) COMMENT '联系电话',
        capacity INT DEFAULT 0 COMMENT '考场容量',
        sort_order INT DEFAULT 0 COMMENT '排序',
        status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考点表'
    `);

    // 清空现有数据
    await connection.execute('DELETE FROM exam_sites');
    await connection.execute('DELETE FROM administrative_divisions');

    // 插入根节点（国家考试院）
    await connection.execute(`
      INSERT INTO administrative_divisions (id, parent_id, name, short_name, single_name, level, sort_order) VALUES
      ('root', NULL, '国家考试院', '国家考试院', '国', 'root', 0)
    `);

    // 插入省份数据
    await connection.execute(`
      INSERT INTO administrative_divisions (id, parent_id, name, short_name, single_name, level, sort_order) VALUES
      ('51', 'root', '四川省', '四川', '川', 'province', 1),
      ('11', 'root', '北京市', '北京', '京', 'province', 2),
      ('31', 'root', '上海市', '上海', '沪', 'province', 3),
      ('44', 'root', '广东省', '广东', '粤', 'province', 4)
    `);

    // 插入地市数据
    await connection.execute(`
      INSERT INTO administrative_divisions (id, parent_id, name, short_name, level, sort_order) VALUES
      ('5101', '51', '成都市', '成都', 'city', 1),
      ('5107', '51', '绵阳市', '绵阳', 'city', 2),
      ('4401', '44', '广州市', '广州', 'city', 1),
      ('4403', '44', '深圳市', '深圳', 'city', 2)
    `);

    // 插入区县数据
    await connection.execute(`
      INSERT INTO administrative_divisions (id, parent_id, name, short_name, level, sort_order) VALUES
      ('510104', '5101', '锦江区', '锦江', 'district', 1),
      ('510105', '5101', '青羊区', '青羊', 'district', 2),
      ('440106', '4401', '天河区', '天河', 'district', 1),
      ('110108', '11', '海淀区', '海淀', 'district', 1)
    `);

    // 插入省直属考点（具体学校名称）
    await connection.execute(`
      INSERT INTO exam_sites (id, parent_id, parent_level, name, short_name, uri, address, contact_person, contact_phone, capacity, sort_order) VALUES
      ('51001', '51', 'province', '四川师范大学附属中学', '川师附中', 'http://exam.sdsdfz.edu.cn', '成都市锦江区静安路5号', '张校长', '028-84760193', 5000, 1),
      ('11001', '11', 'province', '北京师范大学附属实验中学', '北师大实验', 'http://exam.sdsz.edu.cn', '北京市西城区二龙路甲14号', '李校长', '010-66036431', 4500, 1),
      ('44001', '44', 'province', '华南师范大学附属中学', '华师附中', 'http://exam.hsdfz.com.cn', '广州市天河区中山大道西1号', '王校长', '020-38630561', 4800, 1)
    `);

    // 插入市直属考点（具体学校名称）
    await connection.execute(`
      INSERT INTO exam_sites (id, parent_id, parent_level, name, short_name, uri, address, contact_person, contact_phone, capacity, sort_order) VALUES
      ('5101001', '5101', 'city', '成都外国语学校', '成外', 'http://exam.cfls.net.cn', '成都市高新区百草路35号', '陈校长', '028-68501880', 3500, 1),
      ('4401001', '4401', 'city', '广东实验中学', '省实', 'http://exam.gdsyzx.edu.cn', '广州市荔湾区龙溪大道省实路1号', '刘校长', '020-81505520', 3800, 1),
      ('5107001', '5107', 'city', '绵阳中学实验学校', '绵中实验', 'http://exam.myzxsy.com', '绵阳市涪城区石塘路62号', '赵校长', '0816-2312069', 2500, 1)
    `);

    // 插入区县考点
    await connection.execute(`
      INSERT INTO exam_sites (id, parent_id, parent_level, name, short_name, uri, address, contact_person, contact_phone, capacity, sort_order) VALUES
      ('510104001', '510104', 'district', '成都市第七中学', '成都七中', 'http://exam.cd7z.net', '成都市锦江区林荫中街1号', '张主任', '028-85442231', 2000, 1),
      ('510105001', '510105', 'district', '成都市石室中学', '石室中学', 'http://exam.cdssz.net', '成都市青羊区文庙前街93号', '赵主任', '028-86111271', 2200, 1),
      ('440106001', '440106', 'district', '华南师范大学附属中学', '华师附中', 'http://exam.hsdfz.com.cn', '广州市天河区中山大道西1号', '马校长', '020-38630561', 3200, 1),
      ('110108001', '110108', 'district', '中国人民大学附属中学', '人大附中', 'http://exam.rdfz.cn', '北京市海淀区中关村大街37号', '张校长', '010-62512094', 3000, 1)
    `);

    console.log('✓ 测试数据初始化完成');
    
    // 验证数据
    const [result] = await connection.execute(`
      SELECT 
        parent_level,
        COUNT(*) as count
      FROM exam_sites 
      GROUP BY parent_level 
      ORDER BY parent_level
    `);
    
    console.log('\n考点统计:');
    result.forEach(row => {
      console.log(`${row.parent_level}: ${row.count}个`);
    });

  } catch (error) {
    console.error('初始化失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行初始化
initTestData();
