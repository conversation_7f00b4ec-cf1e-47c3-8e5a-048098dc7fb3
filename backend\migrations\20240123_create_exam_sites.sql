-- 创建考点表（支持多级上级机构）
CREATE TABLE IF NOT EXISTS exam_sites (
    id VARCHAR(20) PRIMARY KEY COMMENT '考点代码',
    parent_id VARCHAR(20) NOT NULL COMMENT '上级机构代码（可以是省、地市或区县）',
    parent_level ENUM('province', 'city', 'district') NOT NULL COMMENT '上级机构级别',
    name VARCHAR(100) NOT NULL COMMENT '考点名称',
    short_name VARCHAR(50) COMMENT '考点简称',
    uri VARCHAR(255) COMMENT '考点URI地址',
    address VARCHAR(255) COMMENT '考点地址',
    contact_person VARCHAR(50) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    capacity INT DEFAULT 0 COMMENT '考场容量',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_parent_id (parent_id),
    INDEX idx_parent_level (parent_level),
    INDEX idx_status (status),
    INDEX idx_name (name),
    FOREIGN KEY (parent_id) REFERENCES administrative_divisions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考点表';

-- 插入成都市锦江区示例考点数据（区县级考点）
INSERT INTO exam_sites (id, parent_id, parent_level, name, short_name, uri, address, contact_person, contact_phone, capacity, sort_order) VALUES
('510104001', '510104', 'district', '成都市第七中学', '成都七中', 'http://exam.cd7z.net', '成都市锦江区林荫中街1号', '张主任', '028-85442231', 2000, 1),
('510104002', '510104', 'district', '成都市盐道街中学', '盐道街中学', 'http://exam.ydjzx.net', '成都市锦江区盐道街中学', '李主任', '028-86691516', 1800, 2),
('510104003', '510104', 'district', '成都市第十七中学', '成都十七中', 'http://exam.cd17z.net', '成都市锦江区较场坝东街48号', '王主任', '028-84442764', 1500, 3),
('510104004', '510104', 'district', '成都市田家炳中学', '田家炳中学', 'http://exam.tjbzx.net', '成都市锦江区顺江路369号', '陈主任', '028-84551611', 1200, 4),
('510104005', '510104', 'district', '成都市第三中学', '成都三中', 'http://exam.cd3z.net', '成都市锦江区红星路二段83号', '刘主任', '028-86932875', 1000, 5);

-- 插入成都市青羊区示例考点数据（区县级考点）
INSERT INTO exam_sites (id, parent_id, parent_level, name, short_name, uri, address, contact_person, contact_phone, capacity, sort_order) VALUES
('510105001', '510105', 'district', '成都市石室中学', '石室中学', 'http://exam.cdssz.net', '成都市青羊区文庙前街93号', '赵主任', '028-86111271', 2200, 1),
('510105002', '510105', 'district', '成都市第四中学', '成都四中', 'http://exam.cd4z.net', '成都市青羊区西胜街6号', '孙主任', '028-87766215', 1600, 2),
('510105003', '510105', 'district', '成都市第十一中学', '成都十一中', 'http://exam.cd11z.net', '成都市青羊区育婴堂街33号', '周主任', '028-87335696', 1400, 3),
('510105004', '510105', 'district', '成都市青羊实验中学', '青羊实验', 'http://exam.qysyzx.net', '成都市青羊区优品街50号', '吴主任', '028-87012056', 1300, 4),
('510105005', '510105', 'district', '成都市第三十七中学', '成都三十七中', 'http://exam.cd37z.net', '成都市青羊区培风东街388号', '郑主任', '028-87339399', 1100, 5);

-- 插入成都市金牛区示例考点数据（区县级考点）
INSERT INTO exam_sites (id, parent_id, parent_level, name, short_name, uri, address, contact_person, contact_phone, capacity, sort_order) VALUES
('510106001', '510106', 'district', '成都市第二十中学', '成都二十中', 'http://exam.cd20z.net', '成都市金牛区茶店子横街6号', '马主任', '028-87506805', 1900, 1),
('510106002', '510106', 'district', '成都市第十八中学', '成都十八中', 'http://exam.cd18z.net', '成都市金牛区白果林小区', '胡主任', '028-87786234', 1700, 2),
('510106003', '510106', 'district', '成都市金牛中学', '金牛中学', 'http://exam.jnzx.net', '成都市金牛区解放路一段86号', '徐主任', '028-83333205', 1500, 3),
('510106004', '510106', 'district', '成都市第四十四中学', '成都四十四中', 'http://exam.cd44z.net', '成都市金牛区金周路999号', '高主任', '028-87798655', 1300, 4),
('510106005', '510106', 'district', '成都市铁路中学', '铁路中学', 'http://exam.tlzx.net', '成都市金牛区一环路北二段34号', '林主任', '028-83180373', 1200, 5);

-- 插入成都市武侯区示例考点数据（区县级考点）
INSERT INTO exam_sites (id, parent_id, parent_level, name, short_name, uri, address, contact_person, contact_phone, capacity, sort_order) VALUES
('510107001', '510107', 'district', '成都市第十二中学', '成都十二中', 'http://exam.cd12z.net', '成都市武侯区太平南新街68号', '何主任', '028-85570586', 1800, 1),
('510107002', '510107', 'district', '成都市武侯高级中学', '武侯高中', 'http://exam.whgz.net', '成都市武侯区簇桥文盛路1号', '梁主任', '028-85017056', 1600, 2),
('510107003', '510107', 'district', '成都市第四十三中学', '成都四十三中', 'http://exam.cd43z.net', '成都市武侯区龙江路小学', '唐主任', '028-85583373', 1400, 3),
('510107004', '510107', 'district', '成都市棕北中学', '棕北中学', 'http://exam.zbzx.net', '成都市武侯区棕树南街12号', '韩主任', '028-85227876', 1300, 4),
('510107005', '510107', 'district', '成都市西北中学', '西北中学', 'http://exam.xbzx.net', '成都市武侯区化成路9号', '冯主任', '028-85012896', 1100, 5);

-- 插入成都市成华区示例考点数据（区县级考点）
INSERT INTO exam_sites (id, parent_id, parent_level, name, short_name, uri, address, contact_person, contact_phone, capacity, sort_order) VALUES
('510108001', '510108', 'district', '成都市第四十九中学', '成都四十九中', 'http://exam.cd49z.net', '成都市成华区建设路54号', '曾主任', '028-84334574', 1700, 1),
('510108002', '510108', 'district', '成都市华西中学', '华西中学', 'http://exam.hxzx.net', '成都市成华区青龙路781号', '邓主任', '028-84321456', 1500, 2),
('510108003', '510108', 'district', '成都市第三十八中学', '成都三十八中', 'http://exam.cd38z.net', '成都市成华区龙潭路10号', '彭主任', '028-84310145', 1400, 3),
('510108004', '510108', 'district', '成都市成华实验中学', '成华实验', 'http://exam.chsyzx.net', '成都市成华区双林路189号', '袁主任', '028-84321789', 1200, 4),
('510108005', '510108', 'district', '成都市第三十中学', '成都三十中', 'http://exam.cd30z.net', '成都市成华区双桥路248号', '谢主任', '028-84567890', 1000, 5);

-- 插入北京市海淀区示例考点数据（区县级考点）
INSERT INTO exam_sites (id, parent_id, parent_level, name, short_name, uri, address, contact_person, contact_phone, capacity, sort_order) VALUES
('110108001', '110108', 'district', '中国人民大学附属中学', '人大附中', 'http://exam.rdfz.cn', '北京市海淀区中关村大街37号', '张校长', '010-62512094', 3000, 1),
('110108002', '110108', 'district', '北京市第一零一中学', '北京101中学', 'http://exam.beijing101.com', '北京市海淀区颐和园路11号', '李校长', '010-51633351', 2800, 2),
('110108003', '110108', 'district', '清华大学附属中学', '清华附中', 'http://exam.qhfz.edu.cn', '北京市海淀区中关村北大街清华大学', '王校长', '010-62789541', 2600, 3),
('110108004', '110108', 'district', '北京大学附属中学', '北大附中', 'http://exam.pkuschool.edu.cn', '北京市海淀区大泥湾路甲82号', '刘校长', '010-58751234', 2400, 4),
('110108005', '110108', 'district', '北京市十一学校', '十一学校', 'http://exam.bnds.cn', '北京市海淀区玉泉路66号', '陈校长', '010-88625562', 2200, 5);

-- 插入上海市浦东新区示例考点数据（区县级考点）
INSERT INTO exam_sites (id, parent_id, parent_level, name, short_name, uri, address, contact_person, contact_phone, capacity, sort_order) VALUES
('310115001', '310115', 'district', '华东师范大学第二附属中学', '华师大二附中', 'http://exam.hsefz.cn', '上海市浦东新区晨晖路555号', '赵校长', '021-50802612', 2800, 1),
('310115002', '310115', 'district', '上海市建平中学', '建平中学', 'http://exam.jianping.com.cn', '上海市浦东新区崮山路517号', '孙校长', '021-58851542', 2600, 2),
('310115003', '310115', 'district', '上海市进才中学', '进才中学', 'http://exam.jincai.sh.cn', '上海市浦东新区杨高中路2788号', '周校长', '021-58851234', 2400, 3),
('310115004', '310115', 'district', '上海市洋泾中学', '洋泾中学', 'http://exam.yangjing.pudong-edu.sh.cn', '上海市浦东新区潍坊西路570号', '吴校长', '021-58751234', 2200, 4),
('310115005', '310115', 'district', '上海市川沙中学', '川沙中学', 'http://exam.chuansha.pudong-edu.sh.cn', '上海市浦东新区川沙镇川环南路260号', '郑校长', '021-58961234', 2000, 5);

-- 插入广州市天河区示例考点数据（区县级考点）
INSERT INTO exam_sites (id, parent_id, parent_level, name, short_name, uri, address, contact_person, contact_phone, capacity, sort_order) VALUES
('440106001', '440106', 'district', '华南师范大学附属中学', '华师附中', 'http://exam.hsdfz.com.cn', '广州市天河区中山大道西1号', '马校长', '020-38630561', 3200, 1),
('440106002', '440106', 'district', '广东实验中学', '省实', 'http://exam.gdsyzx.edu.cn', '广州市天河区天河北路176号', '胡校长', '020-83526562', 3000, 2),
('440106003', '440106', 'district', '广州市第六中学', '广州六中', 'http://exam.gz6z.cn', '广州市天河区黄埔大道309号', '徐校长', '020-83503561', 2800, 3),
('440106004', '440106', 'district', '广州市天河中学', '天河中学', 'http://exam.thzx.net', '广州市天河区珠江新城华成路7号', '高校长', '020-38456789', 2600, 4),
('440106005', '440106', 'district', '广州市第八十九中学', '广州八十九中', 'http://exam.gz89z.cn', '广州市天河区天源路1219号', '林校长', '020-37123456', 2400, 5);

-- 添加省直属考点示例数据
INSERT INTO exam_sites (id, parent_id, parent_level, name, short_name, uri, address, contact_person, contact_phone, capacity, sort_order) VALUES
('51001', '51', 'province', '四川省教育考试院直属考点', '省考试院考点', 'http://exam.sceea.cn', '成都市芳草街26号', '省考试院', '028-85156581', 5000, 1),
('11001', '11', 'province', '北京市教育考试院直属考点', '北京考试院考点', 'http://exam.bjeea.cn', '北京市海淀区志新东路9号', '市考试院', '010-82837100', 4500, 1),
('31001', '31', 'province', '上海市教育考试院直属考点', '上海考试院考点', 'http://exam.shmeea.edu.cn', '上海市杨浦区民星路465号', '市考试院', '021-25652963', 4000, 1),
('44001', '44', 'province', '广东省教育考试院直属考点', '粤考试院考点', 'http://exam.eeagd.edu.cn', '广州市天河区中山大道69号', '省考试院', '020-38627813', 4800, 1);

-- 添加地市直属考点示例数据
INSERT INTO exam_sites (id, parent_id, parent_level, name, short_name, uri, address, contact_person, contact_phone, capacity, sort_order) VALUES
('5101001', '5101', 'city', '成都市教育考试院直属考点', '成都考试院考点', 'http://exam.cdzk.org', '成都市桂花巷34号', '成都考试院', '028-86691516', 3500, 1),
('4401001', '4401', 'city', '广州市招生考试委员会办公室直属考点', '广州招考办考点', 'http://exam.gzzk.cn', '广州市建设六马路16号', '广州招考办', '020-83861999', 3800, 1),
('3201001', '3201', 'city', '南京市招生委员会办公室直属考点', '南京招考办考点', 'http://exam.njzb.net', '南京市中山路179号', '南京招考办', '025-52310667', 3200, 1),
('3701001', '3701', 'city', '济南市教育招生考试院直属考点', '济南考试院考点', 'http://exam.jnzk.net', '济南市历下区龙鼎大道1号', '济南考试院', '0531-86111580', 3000, 1);
