-- 更新考点表结构以支持多级上级机构
-- 执行时间: 2024-07-24

USE `patrol_system`;

-- 备份现有考点数据
CREATE TABLE IF NOT EXISTS exam_sites_backup AS SELECT * FROM exam_sites;

-- 删除现有考点表
DROP TABLE IF EXISTS exam_sites;

-- 重新创建考点表（支持多级上级机构）
CREATE TABLE IF NOT EXISTS exam_sites (
    id VARCHAR(20) PRIMARY KEY COMMENT '考点代码',
    parent_id VARCHAR(20) NOT NULL COMMENT '上级机构代码（可以是省、地市或区县）',
    parent_level ENUM('province', 'city', 'district') NOT NULL COMMENT '上级机构级别',
    name VARCHAR(100) NOT NULL COMMENT '考点名称',
    short_name VARCHAR(50) COMMENT '考点简称',
    uri VARCHAR(255) COMMENT '考点URI地址',
    address VARCHAR(255) COMMENT '考点地址',
    contact_person VARCHAR(50) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    capacity INT DEFAULT 0 COMMENT '考场容量',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_parent_id (parent_id),
    INDEX idx_parent_level (parent_level),
    INDEX idx_status (status),
    INDEX idx_name (name),
    FOREIGN KEY (parent_id) REFERENCES administrative_divisions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考点表';

-- 从备份表恢复数据（将district_id转换为parent_id，并设置parent_level为district）
INSERT INTO exam_sites (id, parent_id, parent_level, name, short_name, uri, address, contact_person, contact_phone, capacity, sort_order, status, created_at, updated_at)
SELECT 
    id, 
    district_id as parent_id, 
    'district' as parent_level, 
    name, 
    short_name, 
    uri, 
    address, 
    contact_person, 
    contact_phone, 
    capacity, 
    sort_order, 
    status, 
    created_at, 
    updated_at
FROM exam_sites_backup;

-- 添加省直属考点示例数据
INSERT INTO exam_sites (id, parent_id, parent_level, name, short_name, uri, address, contact_person, contact_phone, capacity, sort_order) VALUES
('51001', '51', 'province', '四川省教育考试院直属考点', '省考试院考点', 'http://exam.sceea.cn', '成都市芳草街26号', '省考试院', '028-85156581', 5000, 1),
('11001', '11', 'province', '北京市教育考试院直属考点', '北京考试院考点', 'http://exam.bjeea.cn', '北京市海淀区志新东路9号', '市考试院', '010-82837100', 4500, 1),
('31001', '31', 'province', '上海市教育考试院直属考点', '上海考试院考点', 'http://exam.shmeea.edu.cn', '上海市杨浦区民星路465号', '市考试院', '021-25652963', 4000, 1),
('44001', '44', 'province', '广东省教育考试院直属考点', '粤考试院考点', 'http://exam.eeagd.edu.cn', '广州市天河区中山大道69号', '省考试院', '020-38627813', 4800, 1),
('32001', '32', 'province', '江苏省教育考试院直属考点', '苏考试院考点', 'http://exam.jseea.cn', '南京市北京西路15-2号', '省考试院', '025-83235898', 4200, 1),
('37001', '37', 'province', '山东省教育招生考试院直属考点', '鲁考试院考点', 'http://exam.sdzk.cn', '济南市文化西路29号', '省考试院', '0531-86162757', 4600, 1);

-- 添加地市直属考点示例数据
INSERT INTO exam_sites (id, parent_id, parent_level, name, short_name, uri, address, contact_person, contact_phone, capacity, sort_order) VALUES
('5101001', '5101', 'city', '成都市教育考试院直属考点', '成都考试院考点', 'http://exam.cdzk.org', '成都市桂花巷34号', '成都考试院', '028-86691516', 3500, 1),
('4401001', '4401', 'city', '广州市招生考试委员会办公室直属考点', '广州招考办考点', 'http://exam.gzzk.cn', '广州市建设六马路16号', '广州招考办', '020-83861999', 3800, 1),
('3201001', '3201', 'city', '南京市招生委员会办公室直属考点', '南京招考办考点', 'http://exam.njzb.net', '南京市中山路179号', '南京招考办', '025-52310667', 3200, 1),
('3701001', '3701', 'city', '济南市教育招生考试院直属考点', '济南考试院考点', 'http://exam.jnzk.net', '济南市历下区龙鼎大道1号', '济南考试院', '0531-86111580', 3000, 1),
('5103001', '5103', 'city', '自贡市教育和体育局直属考点', '自贡教体局考点', 'http://exam.zgedu.cn', '自贡市自流井区丹桂大街368号', '自贡教体局', '0813-8121649', 2800, 1),
('5106001', '5106', 'city', '德阳市教育局直属考点', '德阳教育局考点', 'http://exam.dyjy.gov.cn', '德阳市长江西路一段12号', '德阳教育局', '0838-2515070', 2600, 1),
('3202001', '3202', 'city', '无锡市教育考试院直属考点', '无锡考试院考点', 'http://exam.wxeea.cn', '无锡市解放南路681号', '无锡考试院', '0510-85017711', 2900, 1),
('3205001', '3205', 'city', '苏州市教育考试院直属考点', '苏州考试院考点', 'http://exam.szjyksy.com', '苏州市劳动路359号', '苏州考试院', '0512-68661151', 3300, 1);

-- 删除备份表（可选，生产环境建议保留一段时间）
-- DROP TABLE exam_sites_backup;

-- 验证数据迁移结果
SELECT 
    parent_level,
    COUNT(*) as count,
    GROUP_CONCAT(DISTINCT SUBSTRING(parent_id, 1, 2)) as provinces
FROM exam_sites 
GROUP BY parent_level 
ORDER BY parent_level;
