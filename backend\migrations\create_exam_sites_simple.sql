-- 创建考点表
CREATE TABLE exam_sites (
    id VARCHAR(20) PRIMARY KEY,
    district_id VARCHAR(20),
    name TEXT,
    short_name TEXT,
    uri VARCHAR(500),
    address TEXT,
    contact_person VARCHAR(100),
    contact_phone VARCHAR(50),
    capacity INT DEFAULT 0,
    sort_order INT DEFAULT 0,
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
