-- 创建行政区划表（简化版）
CREATE TABLE administrative_divisions (
    id VARCHAR(20) PRIMARY KEY,
    parent_id VARCHAR(20),
    name TEXT,
    short_name TEXT,
    single_name VARCHAR(10),
    level VARCHAR(20),
    sort_order INT DEFAULT 0,
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
