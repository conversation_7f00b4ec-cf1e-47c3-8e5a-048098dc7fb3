-- 创建行政区划表
CREATE TABLE IF NOT EXISTS administrative_divisions (
    id VARCHAR(20) PRIMARY KEY COMMENT '行政区划代码',
    parent_id VARCHAR(20) COMMENT '上级行政区划代码',
    name VARCHAR(200) NOT NULL COMMENT '行政区划名称',
    short_name VARCHAR(100) COMMENT '简称',
    single_name VARCHAR(20) COMMENT '单字简称（省份专用）',
    level ENUM('province', 'city', 'district') NOT NULL COMMENT '行政级别',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_parent_id (parent_id),
    INDEX idx_level (level),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='行政区划表';
