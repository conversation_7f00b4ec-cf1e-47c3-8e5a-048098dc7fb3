// 最简单的HTTP服务器测试
const http = require('http');

console.log('🔍 开始创建最简单的HTTP服务器...');

const server = http.createServer((req, res) => {
  console.log(`收到请求: ${req.method} ${req.url}`);
  
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  // 简单的路由
  if (req.url === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      status: 'ok',
      message: '最简单的服务器正在运行',
      timestamp: new Date().toISOString()
    }));
  } else if (req.url === '/api/organization/tree') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      code: 200,
      message: '获取成功',
      data: [
        {
          id: '51',
          name: '四川省',
          children: [
            {
              id: '5101',
              name: '成都市',
              children: []
            }
          ]
        }
      ]
    }));
  } else if (req.url.startsWith('/api/osd-rules')) {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      code: 200,
      message: '获取成功',
      data: [
        {
          id: 1,
          name: '标准考试OSD模板',
          description: '测试模板'
        }
      ]
    }));
  } else if (req.url.startsWith('/api/batch-osd/plans')) {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      code: 200,
      message: '获取成功',
      data: {
        records: [
          {
            id: 1,
            name: '测试计划',
            status: 'completed'
          }
        ],
        total: 1,
        current: 1,
        size: 20
      }
    }));
  } else {
    res.writeHead(404, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      code: 404,
      message: '接口不存在',
      path: req.url
    }));
  }
});

const PORT = 8081;

server.on('error', (err) => {
  console.error('❌ 服务器错误:', err);
  if (err.code === 'EADDRINUSE') {
    console.error(`❌ 端口 ${PORT} 已被占用`);
    console.log('尝试查找占用端口的进程...');
    
    // 尝试使用不同的端口
    const alternativePort = 8082;
    console.log(`🔄 尝试使用端口 ${alternativePort}...`);
    
    server.listen(alternativePort, () => {
      console.log('✅ 最简单的HTTP服务器启动成功!');
      console.log(`📍 服务地址: http://localhost:${alternativePort}`);
      console.log(`🏥 健康检查: http://localhost:${alternativePort}/health`);
      console.log('');
      console.log('⚠️  注意：服务器运行在端口', alternativePort, '而不是', PORT);
      console.log('   请修改前端代理配置或停止占用8081端口的进程');
    });
  } else {
    process.exit(1);
  }
});

server.listen(PORT, () => {
  console.log('✅ 最简单的HTTP服务器启动成功!');
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`🏥 健康检查: http://localhost:${PORT}/health`);
  console.log('');
  console.log('📋 测试API接口:');
  console.log('  - GET  /health                         健康检查');
  console.log('  - GET  /api/organization/tree          机构树数据');
  console.log('  - GET  /api/osd-rules                  OSD规则列表');
  console.log('  - GET  /api/batch-osd/plans            批量OSD计划列表');
  console.log('');
  console.log('🔍 等待前端请求...');
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...');
  server.close(() => {
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭服务器...');
  server.close(() => {
    process.exit(0);
  });
});

console.log('🚀 正在启动服务器...');
