const express = require('express');
const cors = require('cors');
const mysql = require('mysql2/promise');
const app = express();
const port = 8080;

// 中间件
app.use(cors());
app.use(express.json());

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: 'Aiwa75210!',
  database: 'patrol_system',
  charset: 'utf8mb4'
};

// 时间格式化函数
function formatDateTime(date = new Date()) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 获取当前时间
const currentTime = formatDateTime();

// 数据库查询函数
async function getOrganizationTreeFromDB() {
  try {
    const connection = await mysql.createConnection(dbConfig);

    // 查询行政区划数据
    const [adminRows] = await connection.execute(`
      SELECT id, parent_id, name as fullName, short_name as shortName,
             single_name as singleName, level, sort_order as sort,
             status, created_at as createTime, updated_at as updateTime
      FROM administrative_divisions
      ORDER BY sort_order
    `);

    // 查询考点数据
    const [examRows] = await connection.execute(`
      SELECT id, parent_id, parent_level, name as fullName, short_name as shortName,
             uri, address, contact_person, contact_phone, capacity,
             sort_order as sort, status, created_at as createTime, updated_at as updateTime
      FROM exam_sites
      ORDER BY sort_order
    `);

    await connection.end();

    // 合并数据并添加必要字段
    const allNodes = [];

    // 处理行政区划数据
    adminRows.forEach(row => {
      allNodes.push({
        ...row,
        code: row.id,
        level: row.level,
        createTime: formatDateTime(new Date(row.createTime)),
        updateTime: formatDateTime(new Date(row.updateTime)),
        childrenCount: 0,
        examSiteCount: 0,
        children: []
      });
    });

    // 处理考点数据
    examRows.forEach(row => {
      allNodes.push({
        ...row,
        code: row.id,
        level: 'examSite',
        parentId: row.parent_id,
        parentLevel: row.parent_level,
        createTime: formatDateTime(new Date(row.createTime)),
        updateTime: formatDateTime(new Date(row.updateTime)),
        childrenCount: 0,
        examSiteCount: 1,
        children: []
      });
    });

    // 构建树形结构
    const tree = buildTree(allNodes);

    return tree;
  } catch (error) {
    console.error('数据库查询错误:', error);
    return mockData; // 如果数据库查询失败，返回模拟数据
  }
}

// 构建树形结构
function buildTree(nodes) {
  const nodeMap = {};
  const rootNodes = [];

  // 创建节点映射
  nodes.forEach(node => {
    nodeMap[node.id] = { ...node, children: [] };
  });

  // 构建父子关系
  nodes.forEach(node => {
    if (node.parent_id || node.parentId) {
      const parentId = node.parent_id || node.parentId;
      const parent = nodeMap[parentId];
      if (parent) {
        parent.children.push(nodeMap[node.id]);
      }
    } else {
      rootNodes.push(nodeMap[node.id]);
    }
  });

  // 计算统计信息
  function calculateStats(node) {
    node.childrenCount = node.children.length;
    node.examSiteCount = 0;

    if (node.level === 'examSite') {
      node.examSiteCount = 1;
    }

    node.children.forEach(child => {
      calculateStats(child);
      node.examSiteCount += child.examSiteCount;
    });
  }

  rootNodes.forEach(calculateStats);

  return rootNodes;
}

// 递归添加机构到树形结构中
function addOrganizationToTree(nodes, newOrg) {
  for (let node of nodes) {
    if (node.id === newOrg.parentId) {
      // 找到父节点，添加到children中
      node.children.push(newOrg);
      return true;
    }
    if (node.children && node.children.length > 0) {
      if (addOrganizationToTree(node.children, newOrg)) {
        return true;
      }
    }
  }
  return false;
}

// 更新父级节点的统计信息
function updateParentStats(nodes, parentId) {
  for (let node of nodes) {
    if (node.id === parentId) {
      // 重新计算子节点数量
      node.childrenCount = node.children.length;
      // 重新计算考点数量
      node.examSiteCount = calculateExamSiteCount(node);
      return true;
    }
    if (node.children && node.children.length > 0) {
      if (updateParentStats(node.children, parentId)) {
        // 递归更新上级节点的统计
        node.childrenCount = node.children.length;
        node.examSiteCount = calculateExamSiteCount(node);
        return true;
      }
    }
  }
  return false;
}

// 计算考点数量
function calculateExamSiteCount(node) {
  let count = 0;
  if (node.level === 'examSite') {
    count = 1;
  }
  if (node.children && node.children.length > 0) {
    for (let child of node.children) {
      count += calculateExamSiteCount(child);
    }
  }
  return count;
}

// 递归查找节点
function findNodeById(nodes, id) {
  for (let node of nodes) {
    if (node.id === id) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const found = findNodeById(node.children, id);
      if (found) return found;
    }
  }
  return null;
}

// 递归删除节点
function removeNodeById(nodes, id) {
  for (let i = 0; i < nodes.length; i++) {
    if (nodes[i].id === id) {
      nodes.splice(i, 1);
      return true;
    }
    if (nodes[i].children && nodes[i].children.length > 0) {
      if (removeNodeById(nodes[i].children, id)) {
        // 更新父节点统计
        nodes[i].childrenCount = nodes[i].children.length;
        nodes[i].examSiteCount = calculateExamSiteCount(nodes[i]);
        return true;
      }
    }
  }
  return false;
}

// 模拟数据
const mockData = [
  {
    id: '00',
    code: '00',
    parentId: null,
    fullName: '国家考试院',
    shortName: '国家考试院',
    level: 'root',
    sort: 0,
    status: 1,
    createTime: '2024-01-01 00:00:00',
    updateTime: '2024-01-01 00:00:00',
    childrenCount: 2,
    examSiteCount: 4,
    children: [
      {
        id: '51',
        code: '51',
        parentId: '00',
        fullName: '四川省',
        shortName: '四川',
        singleName: '川',
        level: 'province',
        sort: 23,
        status: 1,
        createTime: '2024-01-15 09:30:00',
        updateTime: '2024-01-15 09:30:00',
        childrenCount: 2,
        examSiteCount: 3,
    children: [
      {
        id: '5101',
        code: '5101',
        parentId: '51',
        fullName: '成都市',
        shortName: '成都',
        level: 'city',
        sort: 1,
        status: 1,
        createTime: '2024-01-16 10:15:00',
        updateTime: '2024-01-16 10:15:00',
        childrenCount: 2,
        examSiteCount: 3,
        children: [
          {
            id: '510104',
            code: '510104',
            parentId: '5101',
            fullName: '锦江区',
            shortName: '锦江',
            level: 'district',
            sort: 1,
            status: 1,
            createTime: '2024-01-17 14:20:00',
            updateTime: '2024-01-17 14:20:00',
            childrenCount: 2,
            examSiteCount: 2,
            children: [
              {
                id: '510104001',
                code: '510104001',
                parentId: '510104',
                fullName: '成都市第七中学',
                shortName: '成都七中',
                level: 'examSite',
                uri: 'cdqz.jjjy.cdjy.scjy.cnjy',
                sort: 1,
                status: 1,
                createTime: '2024-01-18 08:45:00',
                updateTime: '2024-01-20 16:30:00',
                childrenCount: 0,
                examSiteCount: 1,
                children: []
              },
              {
                id: '510104002',
                code: '510104002',
                parentId: '510104',
                fullName: '成都市盐道街中学',
                shortName: '盐道街中学',
                level: 'examSite',
                uri: 'ydjzx.jjjy.cdjy.scjy.cnjy',
                sort: 2,
                status: 1,
                createTime: '2024-01-18 09:15:00',
                updateTime: '2024-01-19 11:20:00',
                childrenCount: 0,
                examSiteCount: 1,
                children: []
              }
            ]
          },
          {
            id: '510105',
            code: '510105',
            parentId: '5101',
            fullName: '青羊区',
            shortName: '青羊',
            level: 'district',
            sort: 2,
            status: 1,
            createTime: '2024-01-17 15:30:00',
            updateTime: '2024-01-17 15:30:00',
            childrenCount: 0,
            examSiteCount: 0,
            children: []
          }
        ]
      },
      {
        id: '5102',
        code: '5102',
        parentId: '51',
        fullName: '绵阳市',
        shortName: '绵阳',
        level: 'city',
        sort: 2,
        status: 1,
        createTime: '2024-01-16 11:45:00',
        updateTime: '2024-01-16 11:45:00',
        childrenCount: 0,
        examSiteCount: 0,
        children: []
      }
    ]
  },
      {
        id: '44',
        code: '44',
        parentId: '00',
        fullName: '广东省',
        shortName: '广东',
        singleName: '粤',
        level: 'province',
        sort: 19,
        status: 1,
        createTime: '2024-01-10 13:20:00',
        updateTime: '2024-01-10 13:20:00',
    childrenCount: 2,
    examSiteCount: 1,
    children: [
      {
        id: '4401',
        code: '4401',
        parentId: '44',
        fullName: '广州市',
        shortName: '广州',
        level: 'city',
        sort: 1,
        status: 1,
        createTime: '2024-01-12 10:30:00',
        updateTime: '2024-01-12 10:30:00',
        childrenCount: 1,
        examSiteCount: 1,
        children: [
          {
            id: '440106',
            code: '440106',
            parentId: '4401',
            fullName: '天河区',
            shortName: '天河',
            level: 'district',
            sort: 1,
            status: 1,
            createTime: '2024-01-13 14:15:00',
            updateTime: '2024-01-13 14:15:00',
            childrenCount: 1,
            examSiteCount: 1,
            children: [
              {
                id: '440106001',
                code: '440106001',
                parentId: '440106',
                fullName: '华南师范大学附属中学',
                shortName: '华师附中',
                level: 'examSite',
                uri: 'hsfz.thjy.gzjy.gdjy.cnjy',
                sort: 1,
                status: 1,
                createTime: '2024-01-14 09:00:00',
                updateTime: '2024-01-22 15:45:00',
                childrenCount: 0,
                examSiteCount: 1,
                children: []
              }
            ]
          }
        ]
      },
      {
        id: '4403',
        code: '4403',
        parentId: '44',
        fullName: '深圳市',
        shortName: '深圳',
        level: 'city',
        sort: 2,
        status: 1,
        createTime: '2024-01-12 11:20:00',
        updateTime: '2024-01-12 11:20:00',
        childrenCount: 0,
        examSiteCount: 0,
        children: []
      }
    ]
      }
    ]
  }
];

// API路由
app.get('/api/organization/tree', async (req, res) => {
  console.log('GET /api/organization/tree');
  try {
    const treeData = await getOrganizationTreeFromDB();
    res.json({
      code: 200,
      message: '获取成功',
      data: treeData
    });
  } catch (error) {
    console.error('获取机构树失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取机构树失败',
      error: error.message
    });
  }
});

app.get('/api/organization/:id', (req, res) => {
  const { id } = req.params;
  console.log(`GET /api/organization/${id}`);

  const node = findNodeById(mockData, id);
  if (node) {
    res.json({
      code: 200,
      message: '获取成功',
      data: node
    });
  } else {
    res.status(404).json({
      code: 404,
      message: '机构不存在'
    });
  }
});

app.post('/api/organization', async (req, res) => {
  console.log('POST /api/organization', req.body);

  try {
    const connection = await mysql.createConnection(dbConfig);
    const { parentId, level, code, fullName, shortName, singleName, uri, sort, status } = req.body;

    if (level === 'examSite') {
      // 插入考点数据，需要确定parent_level
      const { parentLevel } = req.body;
      let actualParentLevel = parentLevel;

      if (!actualParentLevel) {
        // 如果没有指定parent_level，根据parentId查询
        const [parentInfo] = await connection.execute(
          'SELECT level FROM administrative_divisions WHERE id = ?',
          [parentId]
        );
        actualParentLevel = parentInfo.length > 0 ? parentInfo[0].level : 'district';
      }

      await connection.execute(`
        INSERT INTO exam_sites (id, parent_id, parent_level, name, short_name, uri, sort_order, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [code, parentId, actualParentLevel, fullName, shortName, uri || '', sort || 0, status || 1]);
    } else {
      // 插入行政区划数据
      await connection.execute(`
        INSERT INTO administrative_divisions (id, parent_id, name, short_name, single_name, level, sort_order, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [code, parentId, fullName, shortName, singleName || null, level, sort || 0, status || 1]);
    }

    await connection.end();

    res.json({
      code: 200,
      message: '创建成功',
      data: {
        id: code,
        code: code,
        parentId: parentId,
        fullName: fullName,
        shortName: shortName,
        singleName: singleName,
        level: level,
        uri: uri,
        sort: sort || 0,
        status: status || 1,
        createTime: formatDateTime(),
        updateTime: formatDateTime(),
        childrenCount: 0,
        examSiteCount: level === 'examSite' ? 1 : 0,
        children: []
      }
    });
  } catch (error) {
    console.error('创建机构失败:', error);
    res.status(500).json({
      code: 500,
      message: '创建机构失败',
      error: error.message
    });
  }
});

app.put('/api/organization/:id', (req, res) => {
  const { id } = req.params;
  console.log(`PUT /api/organization/${id}`, req.body);
  const now = formatDateTime();

  // 查找要更新的节点
  const nodeToUpdate = findNodeById(mockData, id);
  if (!nodeToUpdate) {
    return res.status(404).json({
      code: 404,
      message: '机构不存在'
    });
  }

  // 更新节点数据
  Object.assign(nodeToUpdate, {
    ...req.body,
    id: id,
    updateTime: now
  });

  res.json({
    code: 200,
    message: '更新成功',
    data: nodeToUpdate
  });
});

app.delete('/api/organization/:id', (req, res) => {
  const { id } = req.params;
  console.log(`DELETE /api/organization/${id}`);

  // 查找要删除的节点，获取其父节点ID
  const nodeToDelete = findNodeById(mockData, id);
  if (!nodeToDelete) {
    return res.status(404).json({
      code: 404,
      message: '机构不存在'
    });
  }

  // 删除节点
  if (removeNodeById(mockData, id)) {
    // 如果有父节点，更新父节点统计
    if (nodeToDelete.parentId) {
      updateParentStats(mockData, nodeToDelete.parentId);
    }

    res.json({
      code: 200,
      message: '删除成功'
    });
  } else {
    res.status(400).json({
      code: 400,
      message: '删除失败'
    });
  }
});

app.get('/api/organization/stats', (req, res) => {
  console.log('GET /api/organization/stats');
  res.json({
    code: 200,
    message: '获取成功',
    data: {
      provinceCount: 2,
      cityCount: 4,
      districtCount: 3,
      examSiteCount: 3,
      totalCount: 12
    }
  });
});

app.post('/api/organization/validate/code', (req, res) => {
  console.log('POST /api/organization/validate/code', req.body);
  res.json({
    code: 200,
    message: '验证成功',
    data: true
  });
});

// OSD规则管理相关接口
let osdRules = [
  {
    id: 1,
    name: '完整OSD格式',
    type: 'osd',
    format: '[省份][地市][区县][考点名称][考点编号] 考场[考场号]',
    description: '包含完整地理信息的OSD格式',
    preview: '浙江省杭州西湖第一考点KD001 考场01',
    createTime: '2024-03-21 10:30:00',
    updateTime: '2024-03-21 10:30:00'
  },
  {
    id: 2,
    name: '简化OSD格式',
    type: 'osd',
    format: '[省份简称][地市][考点名称] 考场[考场号]',
    description: '简化的OSD格式，减少显示内容',
    preview: '浙江杭州第一考点 考场01',
    createTime: '2024-03-21 11:00:00',
    updateTime: '2024-03-21 11:00:00'
  },
  {
    id: 3,
    name: '代码格式OSD',
    type: 'osd',
    format: '[省份代码][地市][区县][考点编号] 考场[考场号]',
    description: '使用省份代码的OSD格式',
    preview: '33杭州西湖KD001 考场01',
    createTime: '2024-03-21 12:00:00',
    updateTime: '2024-03-21 12:00:00'
  },
  {
    id: 4,
    name: '单字简称格式',
    type: 'osd',
    format: '[省份单字简称][地市][考点名称] 考场[考场号]',
    description: '使用省份单字简称的OSD格式',
    preview: '浙杭州第一考点 考场01',
    createTime: '2024-03-21 13:00:00',
    updateTime: '2024-03-21 13:00:00'
  }
];

// 生成预览文本的辅助函数
function generatePreview(format, type) {
  // 只处理OSD格式
  const replacements = {
    '[省份]': '浙江省',
    '[省份简称]': '浙江',
    '[省份单字简称]': '浙',
    '[省份代码]': '33',
    '[地市]': '杭州',
    '[区县]': '西湖',
    '[考点名称]': '第一考点',
    '[考点编号]': 'KD001',
    '考场[考场号]': '考场01'
  };

  let preview = format;
  Object.entries(replacements).forEach(([key, value]) => {
    preview = preview.replace(new RegExp(key.replace(/[[\]]/g, '\\$&'), 'g'), value);
  });

  return preview;
}

// 获取OSD规则列表
app.get('/api/osd-rules', (req, res) => {
  const { page = 1, size = 20, keyword, type, status } = req.query;
  console.log('GET /api/osd-rules', req.query);

  let filteredRules = [...osdRules];

  // 关键词搜索
  if (keyword) {
    filteredRules = filteredRules.filter(rule =>
      rule.name.includes(keyword) ||
      rule.description.includes(keyword) ||
      rule.format.includes(keyword)
    );
  }

  // 类型筛选
  if (type) {
    filteredRules = filteredRules.filter(rule => rule.type === type);
  }

  // 状态筛选
  if (status !== undefined) {
    filteredRules = filteredRules.filter(rule => rule.status == status);
  }

  // 分页
  const total = filteredRules.length;
  const start = (page - 1) * size;
  const end = start + parseInt(size);
  const records = filteredRules.slice(start, end);

  res.json({
    code: 200,
    message: '获取成功',
    data: {
      records,
      total,
      size: parseInt(size),
      current: parseInt(page),
      pages: Math.ceil(total / size)
    }
  });
});

// 获取格式选项 - 必须在 :id 路由之前
app.get('/api/osd-rules/format-options', (req, res) => {
  console.log('GET /api/osd-rules/format-options');

  const osdOptions = [
    { label: '[省份]', value: '[省份]' },
    { label: '[省份简称]', value: '[省份简称]' },
    { label: '[省份单字简称]', value: '[省份单字简称]' },
    { label: '[省份代码]', value: '[省份代码]' },
    { label: '[地市]', value: '[地市]' },
    { label: '[区县]', value: '[区县]' },
    { label: '[考点名称]', value: '[考点名称]' },
    { label: '[考点编号]', value: '[考点编号]' },
    { label: '考场[考场号]', value: '考场[考场号]' },
    { label: '空格', value: ' ' },
    { label: '-', value: '-' },
    { label: '_', value: '_' }
  ];

  res.json({
    code: 200,
    message: '获取成功',
    data: {
      osdOptions
    }
  });
});

// 预览OSD格式 - 必须在 :id 路由之前
app.post('/api/osd-rules/preview', (req, res) => {
  const { format, type } = req.body;
  console.log('POST /api/osd-rules/preview', req.body);
  const preview = generatePreview(format, type);

  res.json({
    code: 200,
    message: '预览生成成功',
    data: { preview }
  });
});

// 验证格式模板 - 必须在 :id 路由之前
app.post('/api/osd-rules/validate', (req, res) => {
  const { format, type } = req.body;
  console.log('POST /api/osd-rules/validate', req.body);

  let valid = true;
  let message = '';

  if (!format || format.trim() === '') {
    valid = false;
    message = '格式模板不能为空';
  } else {
    // OSD格式验证
    const validTokens = ['[省份]', '[省份简称]', '[省份单字简称]', '[省份代码]', '[地市]', '[区县]', '[考点名称]', '[考点编号]', '考场[考场号]'];
    const hasValidToken = validTokens.some(token => format.includes(token));

    if (!hasValidToken) {
      valid = false;
      message = '格式模板必须包含至少一个有效的标签';
    }
  }

  res.json({
    code: 200,
    message: '验证完成',
    data: { valid, message }
  });
});

// 获取OSD规则详情
app.get('/api/osd-rules/:id', (req, res) => {
  const { id } = req.params;
  console.log(`GET /api/osd-rules/${id}`);
  const rule = osdRules.find(r => r.id == id);

  if (rule) {
    res.json({
      code: 200,
      message: '获取成功',
      data: rule
    });
  } else {
    res.status(404).json({
      code: 404,
      message: '规则不存在'
    });
  }
});

// 新增OSD规则
app.post('/api/osd-rules', (req, res) => {
  const { name, type, format, description, status = 1 } = req.body;
  console.log('POST /api/osd-rules', req.body);

  // 生成预览
  const preview = generatePreview(format, type);

  const newRule = {
    id: Math.max(...osdRules.map(r => r.id)) + 1,
    name,
    type,
    format,
    description,
    preview,
    status,
    createTime: formatDateTime(),
    updateTime: formatDateTime()
  };

  osdRules.unshift(newRule);

  res.json({
    code: 200,
    message: '新增成功',
    data: newRule
  });
});

// 更新OSD规则
app.put('/api/osd-rules/:id', (req, res) => {
  const { id } = req.params;
  const { name, type, format, description, status } = req.body;
  console.log(`PUT /api/osd-rules/${id}`, req.body);

  const index = osdRules.findIndex(r => r.id == id);
  if (index > -1) {
    const preview = generatePreview(format, type);

    osdRules[index] = {
      ...osdRules[index],
      name,
      type,
      format,
      description,
      preview,
      status,
      updateTime: formatDateTime()
    };

    res.json({
      code: 200,
      message: '更新成功',
      data: osdRules[index]
    });
  } else {
    res.status(404).json({
      code: 404,
      message: '规则不存在'
    });
  }
});

// 删除OSD规则
app.delete('/api/osd-rules/:id', (req, res) => {
  const { id } = req.params;
  console.log(`DELETE /api/osd-rules/${id}`);
  const index = osdRules.findIndex(r => r.id == id);

  if (index > -1) {
    osdRules.splice(index, 1);
    res.json({
      code: 200,
      message: '删除成功'
    });
  } else {
    res.status(404).json({
      code: 404,
      message: '规则不存在'
    });
  }
});

// 批量删除OSD规则
app.delete('/api/osd-rules/batch', (req, res) => {
  const { ids } = req.body;
  console.log('DELETE /api/osd-rules/batch', req.body);

  osdRules = osdRules.filter(rule => !ids.includes(rule.id));

  res.json({
    code: 200,
    message: '批量删除成功'
  });
});

// 更新OSD规则状态
app.patch('/api/osd-rules/:id/status', (req, res) => {
  const { id } = req.params;
  const { status } = req.body;
  console.log(`PATCH /api/osd-rules/${id}/status`, req.body);

  const index = osdRules.findIndex(r => r.id == id);
  if (index > -1) {
    osdRules[index].status = status;
    osdRules[index].updateTime = formatDateTime();

    res.json({
      code: 200,
      message: '状态更新成功',
      data: osdRules[index]
    });
  } else {
    res.status(404).json({
      code: 404,
      message: '规则不存在'
    });
  }
});

// 复制OSD规则
app.post('/api/osd-rules/:id/copy', (req, res) => {
  const { id } = req.params;
  const { name } = req.body;
  console.log(`POST /api/osd-rules/${id}/copy`, req.body);

  const originalRule = osdRules.find(r => r.id == id);
  if (originalRule) {
    const newRule = {
      ...originalRule,
      id: Math.max(...osdRules.map(r => r.id)) + 1,
      name,
      createTime: formatDateTime(),
      updateTime: formatDateTime()
    };

    osdRules.unshift(newRule);

    res.json({
      code: 200,
      message: '复制成功',
      data: newRule
    });
  } else {
    res.status(404).json({
      code: 404,
      message: '原规则不存在'
    });
  }
});



// 批量OSD设置相关接口
let batchOsdPlans = [
  {
    id: 1,
    name: '全省考点OSD统一设置',
    description: '为全省所有考点统一设置OSD显示格式',
    ruleId: 2,
    targetType: 'all',
    targetIds: [],
    executeMode: 'immediate',
    scheduleTime: null,
    concurrency: 5,
    targetCount: 150,
    completedCount: 150,
    successCount: 148,
    failedCount: 2,
    status: 'completed',
    createTime: '2024-03-21 09:00:00',
    startTime: '2024-03-21 09:30:00',
    endTime: '2024-03-21 10:15:00'
  },
  {
    id: 2,
    name: '成都市考点OSD更新',
    description: '更新成都市所有考点的OSD格式为新标准',
    ruleId: 2,
    targetType: 'city',
    targetIds: ['5101'],
    executeMode: 'immediate',
    scheduleTime: null,
    concurrency: 3,
    targetCount: 45,
    completedCount: 32,
    successCount: 30,
    failedCount: 2,
    status: 'running',
    createTime: '2024-03-21 14:00:00',
    startTime: '2024-03-21 14:30:00',
    endTime: null
  },
  {
    id: 3,
    name: '重点考点OSD配置',
    description: '为重点考点配置特殊的OSD显示格式',
    ruleId: 5,
    targetType: 'custom',
    targetIds: ['510104001', '510105002', '440106001'],
    executeMode: 'scheduled',
    scheduleTime: '2024-03-22 10:00:00',
    concurrency: 2,
    targetCount: 3,
    completedCount: 0,
    successCount: 0,
    failedCount: 0,
    status: 'pending',
    createTime: '2024-03-21 16:00:00',
    startTime: null,
    endTime: null
  }
];

let executionLogs = [
  {
    id: 1,
    planId: 2,
    examSiteId: '510104001',
    examSiteName: '成都七中',
    status: 'success',
    message: 'OSD设置成功',
    executeTime: '2024-03-21 14:35:00'
  },
  {
    id: 2,
    planId: 2,
    examSiteId: '510105002',
    examSiteName: '树德光华',
    status: 'success',
    message: 'OSD设置成功',
    executeTime: '2024-03-21 14:36:00'
  },
  {
    id: 3,
    planId: 2,
    examSiteId: '510104002',
    examSiteName: '石室中学',
    status: 'failed',
    message: '网络连接超时',
    executeTime: '2024-03-21 14:37:00'
  }
];

// 获取批量OSD计划列表
app.get('/api/batch-osd/plans', (req, res) => {
  const { page = 1, size = 20, keyword, status } = req.query;
  console.log('GET /api/batch-osd/plans', req.query);

  let filteredPlans = [...batchOsdPlans];

  // 关键词搜索
  if (keyword) {
    filteredPlans = filteredPlans.filter(plan =>
      plan.name.includes(keyword) ||
      plan.description.includes(keyword)
    );
  }

  // 状态筛选
  if (status) {
    filteredPlans = filteredPlans.filter(plan => plan.status === status);
  }

  // 分页
  const total = filteredPlans.length;
  const start = (page - 1) * size;
  const end = start + parseInt(size);
  const records = filteredPlans.slice(start, end);

  res.json({
    code: 200,
    message: '获取成功',
    data: {
      records,
      total,
      size: parseInt(size),
      current: parseInt(page),
      pages: Math.ceil(total / size)
    }
  });
});

// 获取批量OSD计划详情
app.get('/api/batch-osd/plans/:id', (req, res) => {
  const { id } = req.params;
  console.log(`GET /api/batch-osd/plans/${id}`);
  const plan = batchOsdPlans.find(p => p.id == id);

  if (plan) {
    res.json({
      code: 200,
      message: '获取成功',
      data: plan
    });
  } else {
    res.status(404).json({
      code: 404,
      message: '计划不存在'
    });
  }
});

// 创建批量OSD计划
app.post('/api/batch-osd/plans', (req, res) => {
  const { name, description, ruleId, targetType, targetIds, executeMode, scheduleTime, concurrency } = req.body;
  console.log('POST /api/batch-osd/plans', req.body);

  const newPlan = {
    id: Math.max(...batchOsdPlans.map(p => p.id)) + 1,
    name,
    description,
    ruleId,
    targetType,
    targetIds: targetIds || [],
    executeMode,
    scheduleTime,
    concurrency: concurrency || 3,
    targetCount: 0,
    completedCount: 0,
    successCount: 0,
    failedCount: 0,
    status: 'pending',
    createTime: formatDateTime(),
    startTime: null,
    endTime: null
  };

  batchOsdPlans.unshift(newPlan);

  res.json({
    code: 200,
    message: '创建成功',
    data: newPlan
  });
});

// 更新批量OSD计划
app.put('/api/batch-osd/plans/:id', (req, res) => {
  const { id } = req.params;
  const { name, description, ruleId, targetType, targetIds, executeMode, scheduleTime, concurrency } = req.body;
  console.log(`PUT /api/batch-osd/plans/${id}`, req.body);

  const index = batchOsdPlans.findIndex(p => p.id == id);
  if (index > -1) {
    batchOsdPlans[index] = {
      ...batchOsdPlans[index],
      name,
      description,
      ruleId,
      targetType,
      targetIds: targetIds || [],
      executeMode,
      scheduleTime,
      concurrency: concurrency || 3,
      updateTime: formatDateTime()
    };

    res.json({
      code: 200,
      message: '更新成功',
      data: batchOsdPlans[index]
    });
  } else {
    res.status(404).json({
      code: 404,
      message: '计划不存在'
    });
  }
});

// 删除批量OSD计划
app.delete('/api/batch-osd/plans/:id', (req, res) => {
  const { id } = req.params;
  console.log(`DELETE /api/batch-osd/plans/${id}`);
  const index = batchOsdPlans.findIndex(p => p.id == id);

  if (index > -1) {
    batchOsdPlans.splice(index, 1);
    res.json({
      code: 200,
      message: '删除成功'
    });
  } else {
    res.status(404).json({
      code: 404,
      message: '计划不存在'
    });
  }
});

// 执行批量OSD计划
app.post('/api/batch-osd/plans/:id/execute', (req, res) => {
  const { id } = req.params;
  console.log(`POST /api/batch-osd/plans/${id}/execute`);

  const index = batchOsdPlans.findIndex(p => p.id == id);
  if (index > -1) {
    batchOsdPlans[index].status = 'running';
    batchOsdPlans[index].startTime = formatDateTime();

    res.json({
      code: 200,
      message: '计划已开始执行',
      data: batchOsdPlans[index]
    });
  } else {
    res.status(404).json({
      code: 404,
      message: '计划不存在'
    });
  }
});

// 暂停批量OSD计划
app.post('/api/batch-osd/plans/:id/pause', (req, res) => {
  const { id } = req.params;
  console.log(`POST /api/batch-osd/plans/${id}/pause`);

  const index = batchOsdPlans.findIndex(p => p.id == id);
  if (index > -1) {
    batchOsdPlans[index].status = 'pending';

    res.json({
      code: 200,
      message: '计划已暂停',
      data: batchOsdPlans[index]
    });
  } else {
    res.status(404).json({
      code: 404,
      message: '计划不存在'
    });
  }
});

// 获取计划执行日志
app.get('/api/batch-osd/plans/:id/logs', (req, res) => {
  const { id } = req.params;
  const { page = 1, size = 20 } = req.query;
  console.log(`GET /api/batch-osd/plans/${id}/logs`);

  const planLogs = executionLogs.filter(log => log.planId == id);

  // 分页
  const total = planLogs.length;
  const start = (page - 1) * size;
  const end = start + parseInt(size);
  const records = planLogs.slice(start, end);

  res.json({
    code: 200,
    message: '获取成功',
    data: {
      records,
      total,
      size: parseInt(size),
      current: parseInt(page),
      pages: Math.ceil(total / size)
    }
  });
});

// 获取可用的OSD规则列表
app.get('/api/batch-osd/available-rules', (req, res) => {
  console.log('GET /api/batch-osd/available-rules');

  const availableRules = osdRules
    .filter(rule => rule.status === 1)
    .map(rule => ({
      id: rule.id,
      name: rule.name,
      type: rule.type,
      format: rule.format
    }));

  res.json({
    code: 200,
    message: '获取成功',
    data: availableRules
  });
});

// 预览计划目标
app.post('/api/batch-osd/preview-targets', (req, res) => {
  const { targetType, targetIds } = req.body;
  console.log('POST /api/batch-osd/preview-targets', req.body);

  let targets = [];
  let totalCount = 0;

  if (targetType === 'all') {
    targets = [
      { id: 'all', name: '全部考点', type: 'all' }
    ];
    totalCount = 150; // 模拟总数
  } else if (targetType === 'city' && targetIds.includes('5101')) {
    targets = [
      { id: '5101', name: '成都市', type: 'city' }
    ];
    totalCount = 45;
  } else if (targetType === 'custom') {
    targets = targetIds.map(id => ({
      id,
      name: `考点${id}`,
      type: 'examSite'
    }));
    totalCount = targetIds.length;
  }

  res.json({
    code: 200,
    message: '预览成功',
    data: { targets, totalCount }
  });
});

// 步骤式创建计划相关接口

// 验证步骤数据
app.post('/api/batch-osd/validate-step', (req, res) => {
  const { step, data } = req.body;
  console.log(`POST /api/batch-osd/validate-step - Step ${step}`, req.body);

  let valid = true;
  let message = '';

  switch (step) {
    case 0: // 基本信息
      if (!data.name) {
        valid = false;
        message = '计划名称不能为空';
      }
      break;
    case 1: // 考点选择
      if (!data.selectedExamSites || data.selectedExamSites.length === 0) {
        valid = false;
        message = '请至少选择一个考点';
      }
      break;
    case 2: // OSD标签生成
      if (!data.ruleId) {
        valid = false;
        message = '请选择OSD规则';
      }
      break;
    // 其他步骤验证...
  }

  res.json({
    code: 200,
    message: '验证完成',
    data: { valid, message }
  });
});

// 保存步骤数据
app.post('/api/batch-osd/save-step', (req, res) => {
  const { planId, step, data } = req.body;
  console.log(`POST /api/batch-osd/save-step - Plan ${planId}, Step ${step}`, req.body);

  // 这里可以保存步骤数据到数据库
  res.json({
    code: 200,
    message: '步骤数据保存成功',
    data: { planId, step }
  });
});

// 获取步骤数据
app.get('/api/batch-osd/step-data/:planId/:step', (req, res) => {
  const { planId, step } = req.params;
  console.log(`GET /api/batch-osd/step-data/${planId}/${step}`);

  // 模拟返回步骤数据
  const stepData = {
    0: { name: '示例计划', code: 'OSD_001', description: '示例描述' },
    1: { selectedExamSites: [], importedSites: [] },
    2: { ruleId: 1, customFormat: '', applyScope: 'all' },
    3: { associationType: 'auto', autoRule: 'siteCode' },
    4: { executeMode: 'immediate', concurrency: 3 }
  };

  res.json({
    code: 200,
    message: '获取成功',
    data: stepData[step] || {}
  });
});

// 计划监控相关接口

// 获取计划执行统计
app.get('/api/batch-osd/plans/:id/stats', (req, res) => {
  const { id } = req.params;
  console.log(`GET /api/batch-osd/plans/${id}/stats`);

  const stats = {
    totalCount: 150,
    successCount: 120,
    failedCount: 5,
    pendingCount: 25,
    runningCount: 0
  };

  res.json({
    code: 200,
    message: '获取成功',
    data: stats
  });
});

// 获取实时执行日志
app.get('/api/batch-osd/plans/:id/realtime-logs', (req, res) => {
  const { id } = req.params;
  const { lastTimestamp } = req.query;
  console.log(`GET /api/batch-osd/plans/${id}/realtime-logs`);

  const logs = [
    { time: '14:35:00', level: 'INFO', message: '开始执行计划：全省考点OSD统一设置', timestamp: Date.now() },
    { time: '14:35:30', level: 'SUCCESS', message: '成都七中 OSD设置成功', timestamp: Date.now() + 1000 },
    { time: '14:36:30', level: 'ERROR', message: '树德光华 OSD设置失败：网络连接超时', timestamp: Date.now() + 2000 }
  ];

  res.json({
    code: 200,
    message: '获取成功',
    data: logs
  });
});

// 重试失败的任务
app.post('/api/batch-osd/plans/:id/retry-failed', (req, res) => {
  const { id } = req.params;
  const { taskIds } = req.body;
  console.log(`POST /api/batch-osd/plans/${id}/retry-failed`, req.body);

  res.json({
    code: 200,
    message: '重试任务已启动',
    data: { planId: id, retryCount: taskIds?.length || 0 }
  });
});

// 暂停/恢复计划
app.post('/api/batch-osd/plans/:id/pause', (req, res) => {
  const { id } = req.params;
  console.log(`POST /api/batch-osd/plans/${id}/pause`);

  const index = batchOsdPlans.findIndex(p => p.id == id);
  if (index > -1) {
    batchOsdPlans[index].status = 'paused';

    res.json({
      code: 200,
      message: '计划已暂停',
      data: batchOsdPlans[index]
    });
  } else {
    res.status(404).json({
      code: 404,
      message: '计划不存在'
    });
  }
});

app.post('/api/batch-osd/plans/:id/resume', (req, res) => {
  const { id } = req.params;
  console.log(`POST /api/batch-osd/plans/${id}/resume`);

  const index = batchOsdPlans.findIndex(p => p.id == id);
  if (index > -1) {
    batchOsdPlans[index].status = 'running';

    res.json({
      code: 200,
      message: '计划已恢复执行',
      data: batchOsdPlans[index]
    });
  } else {
    res.status(404).json({
      code: 404,
      message: '计划不存在'
    });
  }
});

// 导出执行报告
app.get('/api/batch-osd/plans/:id/export-report', (req, res) => {
  const { id } = req.params;
  console.log(`GET /api/batch-osd/plans/${id}/export-report`);

  // 模拟生成报告
  const report = {
    planId: id,
    planName: '全省考点OSD统一设置',
    generateTime: new Date().toISOString(),
    summary: {
      totalCount: 150,
      successCount: 120,
      failedCount: 5,
      successRate: '96.0%'
    },
    details: [
      { siteName: '成都七中', status: 'success', message: 'OSD设置成功' },
      { siteName: '树德光华', status: 'failed', message: '网络连接超时' }
    ]
  };

  res.json({
    code: 200,
    message: '报告生成成功',
    data: report
  });
});

// 启动服务器
app.listen(port, () => {
  console.log(`Mock API server running at http://localhost:${port}`);
  console.log('Available endpoints:');
  console.log('');
  console.log('=== 机构数据管理 ===');
  console.log('  GET  /api/organization/tree');
  console.log('  GET  /api/organization/:id');
  console.log('  POST /api/organization');
  console.log('  PUT  /api/organization/:id');
  console.log('  DELETE /api/organization/:id');
  console.log('  GET  /api/organization/stats');
  console.log('  POST /api/organization/validate/code');
  console.log('');
  console.log('=== OSD规则管理 ===');
  console.log('  GET  /api/osd-rules');
  console.log('  GET  /api/osd-rules/:id');
  console.log('  POST /api/osd-rules');
  console.log('  PUT  /api/osd-rules/:id');
  console.log('  DELETE /api/osd-rules/:id');
  console.log('  PATCH /api/osd-rules/:id/status');
  console.log('  POST /api/osd-rules/:id/copy');
  console.log('  POST /api/osd-rules/preview');
  console.log('  GET  /api/osd-rules/format-options');
  console.log('  POST /api/osd-rules/validate');
  console.log('');
  console.log('=== 批量OSD设置 ===');
  console.log('  GET  /api/batch-osd/plans');
  console.log('  GET  /api/batch-osd/plans/:id');
  console.log('  POST /api/batch-osd/plans');
  console.log('  PUT  /api/batch-osd/plans/:id');
  console.log('  DELETE /api/batch-osd/plans/:id');
  console.log('  POST /api/batch-osd/plans/:id/execute');
  console.log('  POST /api/batch-osd/plans/:id/pause');
  console.log('  POST /api/batch-osd/plans/:id/resume');
  console.log('  GET  /api/batch-osd/plans/:id/logs');
  console.log('  GET  /api/batch-osd/plans/:id/stats');
  console.log('  GET  /api/batch-osd/plans/:id/realtime-logs');
  console.log('  POST /api/batch-osd/plans/:id/retry-failed');
  console.log('  GET  /api/batch-osd/plans/:id/export-report');
  console.log('  GET  /api/batch-osd/available-rules');
  console.log('  POST /api/batch-osd/preview-targets');
  console.log('  POST /api/batch-osd/validate-step');
  console.log('  POST /api/batch-osd/save-step');
  console.log('  GET  /api/batch-osd/step-data/:planId/:step');
});
