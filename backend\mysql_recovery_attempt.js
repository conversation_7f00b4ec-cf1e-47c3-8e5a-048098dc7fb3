const mysql = require('mysql2/promise');

async function attemptMySQLRecovery() {
  console.log('=== MySQL连接恢复尝试 ===\n');
  
  // 尝试不同的密码变体（可能的编码问题）
  const passwordVariants = [
    'Aiwa75210！',  // 原密码（中文感叹号）
    'Aiwa75210!',   // 英文感叹号
    'Aiwa75210',    // 无感叹号
    'root',         // 默认密码
    '',             // 空密码
  ];
  
  const hostVariants = ['localhost', '127.0.0.1', '::1'];
  
  console.log('🔍 尝试不同的密码和主机组合...\n');
  
  for (const host of hostVariants) {
    for (const password of passwordVariants) {
      const config = {
        host: host,
        port: 3306,
        user: 'root',
        password: password,
        connectTimeout: 5000
      };
      
      console.log(`测试: root@${host} 密码: "${password}"`);
      
      try {
        const connection = await mysql.createConnection(config);
        console.log('   ✅ 连接成功！');
        
        // 获取基本信息
        const [versionResult] = await connection.execute('SELECT VERSION() as version');
        console.log(`   📊 MySQL版本: ${versionResult[0].version}`);
        
        // 检查用户信息
        const [userResult] = await connection.execute('SELECT USER(), CURRENT_USER()');
        console.log(`   👤 用户信息: ${JSON.stringify(userResult[0])}`);
        
        // 检查数据库
        const [databases] = await connection.execute('SHOW DATABASES');
        const dbNames = databases.map(db => Object.values(db)[0]);
        console.log(`   📁 数据库列表: ${dbNames.join(', ')}`);
        
        // 特别检查patrol_system
        if (dbNames.includes('patrol_system')) {
          console.log('   🎯 patrol_system数据库存在！');
          
          await connection.execute('USE patrol_system');
          const [tables] = await connection.execute('SHOW TABLES');
          const tableNames = tables.map(t => Object.values(t)[0]);
          console.log(`   📋 表列表: ${tableNames.join(', ')}`);
          
          // 检查关键表
          const keyTables = ['osd_rules', 'batch_osd_plans'];
          for (const table of keyTables) {
            if (tableNames.includes(table)) {
              const [count] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`);
              console.log(`   📊 ${table}: ${count[0].count} 条记录`);
            }
          }
        } else {
          console.log('   ⚠️  patrol_system数据库不存在');
        }
        
        await connection.end();
        
        console.log('\n🎉 找到可用的连接配置！');
        console.log('✅ 成功配置:');
        console.log(`   主机: ${host}`);
        console.log(`   用户: root`);
        console.log(`   密码: "${password}"`);
        
        // 更新配置文件
        await updateConfigFiles(host, password);
        
        return { host, password };
        
      } catch (error) {
        console.log(`   ❌ 失败: ${error.message}`);
      }
    }
  }
  
  console.log('\n❌ 所有尝试都失败了');
  return null;
}

async function updateConfigFiles(host, password) {
  console.log('\n🔧 更新配置文件...');
  
  try {
    const fs = require('fs');
    
    // 更新database.js
    const databaseConfigPath = './src/config/database.js';
    if (fs.existsSync(databaseConfigPath)) {
      let content = fs.readFileSync(databaseConfigPath, 'utf8');
      content = content.replace(
        /password: process\.env\.DB_PASSWORD \|\| '[^']*'/,
        `password: process.env.DB_PASSWORD || '${password}'`
      );
      fs.writeFileSync(databaseConfigPath, content);
      console.log('   ✅ 已更新 database.js');
    }
    
    // 更新mock-server.js
    const mockServerPath = './mock-server.js';
    if (fs.existsSync(mockServerPath)) {
      let content = fs.readFileSync(mockServerPath, 'utf8');
      content = content.replace(
        /password: '[^']*'/,
        `password: '${password}'`
      );
      fs.writeFileSync(mockServerPath, content);
      console.log('   ✅ 已更新 mock-server.js');
    }
    
  } catch (error) {
    console.log(`   ❌ 更新配置文件失败: ${error.message}`);
  }
}

// 检查MySQL服务状态
async function checkMySQLService() {
  console.log('=== MySQL服务状态检查 ===\n');
  
  try {
    // 尝试连接到MySQL端口
    const net = require('net');
    const socket = new net.Socket();
    
    return new Promise((resolve) => {
      socket.setTimeout(3000);
      
      socket.on('connect', () => {
        console.log('✅ MySQL服务正在运行（端口3306可连接）');
        socket.destroy();
        resolve(true);
      });
      
      socket.on('timeout', () => {
        console.log('❌ 连接超时 - MySQL可能未启动');
        socket.destroy();
        resolve(false);
      });
      
      socket.on('error', (error) => {
        if (error.code === 'ECONNREFUSED') {
          console.log('❌ 连接被拒绝 - MySQL服务未启动');
        } else {
          console.log(`❌ 连接错误: ${error.message}`);
        }
        resolve(false);
      });
      
      socket.connect(3306, 'localhost');
    });
  } catch (error) {
    console.log(`服务检查失败: ${error.message}`);
    return false;
  }
}

// 主函数
async function main() {
  console.log('🚀 开始MySQL连接问题诊断和恢复...\n');
  
  // 1. 检查服务状态
  const serviceRunning = await checkMySQLService();
  if (!serviceRunning) {
    console.log('\n💡 请先启动MySQL服务');
    return;
  }
  
  // 2. 尝试恢复连接
  const result = await attemptMySQLRecovery();
  
  if (result) {
    console.log('\n🎯 连接恢复成功！现在可以重新启动应用程序。');
  } else {
    console.log('\n💡 建议的下一步操作:');
    console.log('1. 使用MySQL Workbench或命令行工具直接连接');
    console.log('2. 检查MySQL错误日志文件');
    console.log('3. 考虑重置MySQL root密码');
    console.log('4. 检查MySQL配置文件中的用户权限设置');
    console.log('5. 暂时使用Mock数据模式继续开发');
  }
}

main().catch(console.error);
