{"name": "patrol-system-backend-mock", "version": "1.0.0", "description": "Mock backend server for patrol system", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "mock": "node mock-server.js"}, "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.2"}, "keywords": ["mock", "api", "server"], "author": "AI Design", "license": "MIT"}