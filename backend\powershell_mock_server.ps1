# PowerShell HTTP服务器 - 解决前端API调用问题
param(
    [int]$Port = 8081
)

# 检查端口是否被占用
$portInUse = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
if ($portInUse) {
    Write-Host "❌ 端口 $Port 已被占用，尝试使用端口 8082" -ForegroundColor Red
    $Port = 8082
}

Write-Host "🚀 PowerShell Mock服务器启动中..." -ForegroundColor Green
Write-Host "📍 服务地址: http://localhost:$Port" -ForegroundColor Cyan
Write-Host "🏥 健康检查: http://localhost:$Port/health" -ForegroundColor Cyan
Write-Host "📊 数据库状态: ❌ Mock模式（无数据库连接）" -ForegroundColor Yellow
Write-Host ""
Write-Host "📋 可用的Mock API接口:" -ForegroundColor White
Write-Host "  - GET  /api/organization/tree          机构树数据" -ForegroundColor Gray
Write-Host "  - GET  /api/osd-rules                  OSD规则列表" -ForegroundColor Gray
Write-Host "  - GET  /api/batch-osd/plans            批量OSD计划列表" -ForegroundColor Gray
Write-Host ""
Write-Host "按 Ctrl+C 停止服务器" -ForegroundColor Yellow

# Mock数据
$mockOrganizationTree = @(
    @{
        id = "51"
        name = "四川省"
        fullName = "四川省"
        shortName = "川"
        singleName = "四川"
        level = 1
        sort = 1
        status = 1
        createTime = "2024-01-01 00:00:00"
        updateTime = "2024-01-01 00:00:00"
        children = @(
            @{
                id = "5101"
                name = "成都市"
                fullName = "成都市"
                shortName = "成都"
                singleName = "成都"
                level = 2
                sort = 1
                status = 1
                createTime = "2024-01-01 00:00:00"
                updateTime = "2024-01-01 00:00:00"
                children = @(
                    @{
                        id = "510104"
                        name = "锦江区"
                        fullName = "锦江区"
                        shortName = "锦江"
                        singleName = "锦江"
                        level = 3
                        sort = 1
                        status = 1
                        createTime = "2024-01-01 00:00:00"
                        updateTime = "2024-01-01 00:00:00"
                        examSites = @(
                            @{
                                id = "510104001"
                                site_id = "510104001"
                                site_name = "成都市第七中学"
                                site_code = "CD001"
                                province_id = "51"
                                province_name = "四川省"
                                city_id = "5101"
                                city_name = "成都市"
                                district_id = "510104"
                                district_name = "锦江区"
                                address = "成都市锦江区林荫街1号"
                                contact_person = "张老师"
                                contact_phone = "028-12345678"
                                room_count = 25
                                status = 1
                                create_time = "2024-01-01 00:00:00"
                                update_time = "2024-01-01 00:00:00"
                            }
                        )
                    },
                    @{
                        id = "510105"
                        name = "青羊区"
                        fullName = "青羊区"
                        shortName = "青羊"
                        singleName = "青羊"
                        level = 3
                        sort = 2
                        status = 1
                        createTime = "2024-01-01 00:00:00"
                        updateTime = "2024-01-01 00:00:00"
                        examSites = @(
                            @{
                                id = "510105002"
                                site_id = "510105002"
                                site_name = "成都市石室中学"
                                site_code = "CD002"
                                province_id = "51"
                                province_name = "四川省"
                                city_id = "5101"
                                city_name = "成都市"
                                district_id = "510105"
                                district_name = "青羊区"
                                address = "成都市青羊区光华大道一段1386号"
                                contact_person = "李老师"
                                contact_phone = "028-87654321"
                                room_count = 30
                                status = 1
                                create_time = "2024-01-01 00:00:00"
                                update_time = "2024-01-01 00:00:00"
                            }
                        )
                    }
                )
            }
        )
    }
)

$mockOsdRules = @(
    @{
        id = 1
        name = "标准考试OSD模板"
        description = "适用于标准化考试的OSD显示模板"
        template_content = "考点：{site_name} 考场：{room_number} 时间：{current_time}"
        font_size = 24
        font_color = "#FFFFFF"
        background_color = "#000000"
        position_x = 10
        position_y = 10
        display_duration = 0
        is_active = 1
        create_time = "2024-01-01 00:00:00"
        update_time = "2024-01-01 00:00:00"
    },
    @{
        id = 2
        name = "研究生考试OSD模板"
        description = "专用于研究生考试的OSD显示模板"
        template_content = "研究生考试 考点：{site_name} 考场：{room_number}"
        font_size = 20
        font_color = "#FFFF00"
        background_color = "#000080"
        position_x = 15
        position_y = 15
        display_duration = 0
        is_active = 1
        create_time = "2024-01-01 00:00:00"
        update_time = "2024-01-01 00:00:00"
    }
)

$mockPlans = @(
    @{
        id = 1
        name = "2024年研究生考试OSD设置"
        description = "用于2024年研究生考试的OSD批量设置"
        rule_id = 1
        rule_name = "标准考试OSD模板"
        target_type = "custom"
        target_count = 55
        execute_mode = "scheduled"
        scheduled_time = "2024-12-20 08:00:00"
        concurrency = 5
        status = "completed"
        progress = 100
        success_count = 55
        failed_count = 0
        create_time = "2024-01-15 10:00:00"
        update_time = "2024-01-15 12:00:00"
        creator = "admin"
    },
    @{
        id = 2
        name = "期末考试OSD批量设置"
        description = "用于期末考试的OSD批量设置"
        rule_id = 2
        rule_name = "研究生考试OSD模板"
        target_type = "all"
        target_count = 120
        execute_mode = "immediate"
        scheduled_time = $null
        concurrency = 3
        status = "running"
        progress = 65
        success_count = 78
        failed_count = 2
        create_time = "2024-01-20 14:30:00"
        update_time = "2024-01-20 15:45:00"
        creator = "admin"
    }
)

# 创建HTTP监听器
$listener = New-Object System.Net.HttpListener
$listener.Prefixes.Add("http://localhost:$Port/")
$listener.Start()

Write-Host "✅ 服务器已启动，等待请求..." -ForegroundColor Green

try {
    while ($listener.IsListening) {
        $context = $listener.GetContext()
        $request = $context.Request
        $response = $context.Response

        $timestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss.fffZ"
        $method = $request.HttpMethod
        $url = $request.Url.AbsolutePath

        Write-Host "$timestamp $method $url" -ForegroundColor White

        # 设置CORS头
        $response.Headers.Add("Access-Control-Allow-Origin", "*")
        $response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
        $response.Headers.Add("Access-Control-Allow-Headers", "Content-Type, Authorization")
        $response.ContentType = "application/json; charset=utf-8"

        # 处理OPTIONS请求
        if ($method -eq "OPTIONS") {
            $response.StatusCode = 200
            $response.Close()
            continue
        }

        # 路由处理
        $responseData = $null

        switch ($url) {
            "/health" {
                $responseData = @{
                    status = "ok"
                    timestamp = $timestamp
                    service = "patrol-system-powershell-mock-api"
                } | ConvertTo-Json -Depth 10
            }
            "/api/organization/tree" {
                Write-Host "返回Mock机构树数据" -ForegroundColor Cyan
                $responseData = @{
                    code = 200
                    message = "获取成功"
                    data = $mockOrganizationTree
                } | ConvertTo-Json -Depth 10
            }
            "/api/osd-rules" {
                Write-Host "返回Mock OSD规则数据" -ForegroundColor Cyan
                $responseData = @{
                    code = 200
                    message = "获取成功"
                    data = $mockOsdRules
                } | ConvertTo-Json -Depth 10
            }
            "/api/batch-osd/plans" {
                Write-Host "返回Mock批量OSD计划数据" -ForegroundColor Cyan
                $responseData = @{
                    code = 200
                    message = "获取成功"
                    data = @{
                        records = $mockPlans
                        total = $mockPlans.Count
                        current = 1
                        size = 20
                    }
                } | ConvertTo-Json -Depth 10
            }
            default {
                $response.StatusCode = 404
                $responseData = @{
                    code = 404
                    message = "接口不存在"
                    path = $url
                } | ConvertTo-Json -Depth 10
            }
        }

        # 发送响应
        $buffer = [System.Text.Encoding]::UTF8.GetBytes($responseData)
        $response.ContentLength64 = $buffer.Length
        $response.OutputStream.Write($buffer, 0, $buffer.Length)
        $response.Close()
    }
}
catch {
    Write-Host "服务器错误: $_" -ForegroundColor Red
}
finally {
    $listener.Stop()
    Write-Host "服务器已停止" -ForegroundColor Yellow
}
