#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
临时Python Mock服务器 - 解决前端API调用问题
"""

import json
import http.server
import socketserver
from urllib.parse import urlparse, parse_qs
from datetime import datetime

PORT = 8081

# Mock数据
mock_organization_tree = [
    {
        "id": "51",
        "name": "四川省",
        "fullName": "四川省",
        "shortName": "川",
        "singleName": "四川",
        "level": 1,
        "sort": 1,
        "status": 1,
        "createTime": "2024-01-01 00:00:00",
        "updateTime": "2024-01-01 00:00:00",
        "children": [
            {
                "id": "5101",
                "name": "成都市",
                "fullName": "成都市",
                "shortName": "成都",
                "singleName": "成都",
                "level": 2,
                "sort": 1,
                "status": 1,
                "createTime": "2024-01-01 00:00:00",
                "updateTime": "2024-01-01 00:00:00",
                "children": [
                    {
                        "id": "510104",
                        "name": "锦江区",
                        "fullName": "锦江区",
                        "shortName": "锦江",
                        "singleName": "锦江",
                        "level": 3,
                        "sort": 1,
                        "status": 1,
                        "createTime": "2024-01-01 00:00:00",
                        "updateTime": "2024-01-01 00:00:00",
                        "examSites": [
                            {
                                "id": "510104001",
                                "site_id": "510104001",
                                "site_name": "成都市第七中学",
                                "site_code": "CD001",
                                "province_id": "51",
                                "province_name": "四川省",
                                "city_id": "5101",
                                "city_name": "成都市",
                                "district_id": "510104",
                                "district_name": "锦江区",
                                "address": "成都市锦江区林荫街1号",
                                "contact_person": "张老师",
                                "contact_phone": "028-12345678",
                                "room_count": 25,
                                "status": 1,
                                "create_time": "2024-01-01 00:00:00",
                                "update_time": "2024-01-01 00:00:00"
                            }
                        ]
                    },
                    {
                        "id": "510105",
                        "name": "青羊区",
                        "fullName": "青羊区",
                        "shortName": "青羊",
                        "singleName": "青羊",
                        "level": 3,
                        "sort": 2,
                        "status": 1,
                        "createTime": "2024-01-01 00:00:00",
                        "updateTime": "2024-01-01 00:00:00",
                        "examSites": [
                            {
                                "id": "510105002",
                                "site_id": "510105002",
                                "site_name": "成都市石室中学",
                                "site_code": "CD002",
                                "province_id": "51",
                                "province_name": "四川省",
                                "city_id": "5101",
                                "city_name": "成都市",
                                "district_id": "510105",
                                "district_name": "青羊区",
                                "address": "成都市青羊区光华大道一段1386号",
                                "contact_person": "李老师",
                                "contact_phone": "028-87654321",
                                "room_count": 30,
                                "status": 1,
                                "create_time": "2024-01-01 00:00:00",
                                "update_time": "2024-01-01 00:00:00"
                            }
                        ]
                    }
                ]
            }
        ]
    }
]

mock_osd_rules = [
    {
        "id": 1,
        "name": "标准考试OSD模板",
        "description": "适用于标准化考试的OSD显示模板",
        "template_content": "考点：{site_name} 考场：{room_number} 时间：{current_time}",
        "font_size": 24,
        "font_color": "#FFFFFF",
        "background_color": "#000000",
        "position_x": 10,
        "position_y": 10,
        "display_duration": 0,
        "is_active": 1,
        "create_time": "2024-01-01 00:00:00",
        "update_time": "2024-01-01 00:00:00"
    },
    {
        "id": 2,
        "name": "研究生考试OSD模板",
        "description": "专用于研究生考试的OSD显示模板",
        "template_content": "研究生考试 考点：{site_name} 考场：{room_number}",
        "font_size": 20,
        "font_color": "#FFFF00",
        "background_color": "#000080",
        "position_x": 15,
        "position_y": 15,
        "display_duration": 0,
        "is_active": 1,
        "create_time": "2024-01-01 00:00:00",
        "update_time": "2024-01-01 00:00:00"
    }
]

mock_plans = [
    {
        "id": 1,
        "name": "2024年研究生考试OSD设置",
        "description": "用于2024年研究生考试的OSD批量设置",
        "rule_id": 1,
        "rule_name": "标准考试OSD模板",
        "target_type": "custom",
        "target_count": 55,
        "execute_mode": "scheduled",
        "scheduled_time": "2024-12-20 08:00:00",
        "concurrency": 5,
        "status": "completed",
        "progress": 100,
        "success_count": 55,
        "failed_count": 0,
        "create_time": "2024-01-15 10:00:00",
        "update_time": "2024-01-15 12:00:00",
        "creator": "admin"
    },
    {
        "id": 2,
        "name": "期末考试OSD批量设置",
        "description": "用于期末考试的OSD批量设置",
        "rule_id": 2,
        "rule_name": "研究生考试OSD模板",
        "target_type": "all",
        "target_count": 120,
        "execute_mode": "immediate",
        "scheduled_time": None,
        "concurrency": 3,
        "status": "running",
        "progress": 65,
        "success_count": 78,
        "failed_count": 2,
        "create_time": "2024-01-20 14:30:00",
        "update_time": "2024-01-20 15:45:00",
        "creator": "admin"
    }
]


class MockAPIHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        query_params = parse_qs(parsed_path.query)
        
        print(f"{datetime.now().isoformat()} GET {path}")
        
        # 设置CORS头
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.end_headers()
        
        # 路由处理
        if path == '/health':
            response = {
                "status": "ok",
                "timestamp": datetime.now().isoformat(),
                "service": "patrol-system-python-mock-api"
            }
        elif path == '/api/organization/tree':
            print('返回Mock机构树数据')
            response = {
                "code": 200,
                "message": "获取成功",
                "data": mock_organization_tree
            }
        elif path == '/api/osd-rules':
            print('返回Mock OSD规则数据')
            response = {
                "code": 200,
                "message": "获取成功",
                "data": mock_osd_rules
            }
        elif path == '/api/batch-osd/plans':
            print('返回Mock批量OSD计划数据')
            response = {
                "code": 200,
                "message": "获取成功",
                "data": {
                    "records": mock_plans,
                    "total": len(mock_plans),
                    "current": 1,
                    "size": 20
                }
            }
        else:
            response = {
                "code": 404,
                "message": "接口不存在",
                "path": path
            }
        
        # 发送响应
        response_json = json.dumps(response, ensure_ascii=False, indent=2)
        self.wfile.write(response_json.encode('utf-8'))
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()
    
    def log_message(self, format, *args):
        # 禁用默认日志，使用自定义日志
        pass


if __name__ == "__main__":
    try:
        with socketserver.TCPServer(("", PORT), MockAPIHandler) as httpd:
            print("🚀 Python Mock服务器启动成功!")
            print(f"📍 服务地址: http://localhost:{PORT}")
            print(f"🏥 健康检查: http://localhost:{PORT}/health")
            print("📊 数据库状态: ❌ Mock模式（无数据库连接）")
            print("")
            print("📋 可用的Mock API接口:")
            print("  - GET  /api/organization/tree          机构树数据")
            print("  - GET  /api/osd-rules                  OSD规则列表")
            print("  - GET  /api/batch-osd/plans            批量OSD计划列表")
            print("")
            print("按 Ctrl+C 停止服务器")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n收到中断信号，正在关闭服务器...")
    except Exception as e:
        print(f"服务器启动失败: {e}")
