// 调试脚本：检查计划数据
const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'Aiwa75210!',
  database: 'patrol_system',
  charset: 'utf8mb4'
};

async function debugPlanData() {
  let connection;

  try {
    console.log('🔍 开始检查计划数据...');

    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 1. 检查计划表数据
    console.log('\n📋 检查计划表数据:');
    const [plans] = await connection.execute(`
      SELECT id, name, description, status, rule_id, target_type, target_ids,
             execute_mode, schedule_time, concurrency, target_count, completed_count,
             create_time, update_time
      FROM batch_osd_plans
      ORDER BY create_time DESC
      LIMIT 5
    `);

    console.log('计划数量:', plans.length);
    plans.forEach(plan => {
      console.log(`  - ID: ${plan.id}, 名称: ${plan.name}, 状态: ${plan.status}`);
      console.log(`    目标类型: ${plan.target_type}, 目标数量: ${plan.target_count}`);
      console.log(`    创建时间: ${plan.create_time}`);
    });

    // 2. 检查计划配置数据
    console.log('\n⚙️ 检查计划配置数据:');
    const [configs] = await connection.execute(`
      SELECT plan_id, config_type, config_data
      FROM batch_osd_plan_configs
      ORDER BY plan_id DESC, config_type
      LIMIT 10
    `);

    console.log('配置数量:', configs.length);
    configs.forEach(config => {
      console.log(`  - 计划ID: ${config.plan_id}, 配置类型: ${config.config_type}`);
      try {
        const data = JSON.parse(config.config_data);
        if (config.config_type === 'backup_config') {
          console.log(`    备份配置: 启用=${data.enableBackup}, 策略=${data.backupStrategy}, 保留天数=${data.retentionDays}`);
        } else {
          console.log(`    配置数据: ${JSON.stringify(data).substring(0, 100)}...`);
        }
      } catch (e) {
        const dataStr = typeof config.config_data === 'string' ? config.config_data : JSON.stringify(config.config_data);
        console.log(`    配置数据: ${dataStr.substring(0, 100)}...`);
      }
    });

    // 3. 检查备份数据表
    console.log('\n💾 检查备份数据表:');
    const [backups] = await connection.execute(`
      SELECT plan_id, channel_id, channel_name, backup_time, status
      FROM batch_osd_backup_data
      ORDER BY backup_time DESC
      LIMIT 5
    `);

    console.log('备份数据数量:', backups.length);
    backups.forEach(backup => {
      console.log(`  - 计划ID: ${backup.plan_id}, 通道: ${backup.channel_name}, 状态: ${backup.status}`);
      console.log(`    备份时间: ${backup.backup_time}`);
    });

    // 4. 执行完整的查询（和前端API相同）
    console.log('\n🔍 执行完整查询（模拟前端API）:');
    const [fullQuery] = await connection.execute(`
      SELECT p.id, p.name, p.description, p.rule_id, p.target_type, p.target_ids,
             p.execute_mode, p.schedule_time, p.concurrency, p.target_count,
             p.completed_count, p.success_count, p.failed_count, p.status,
             p.start_time, p.end_time, p.created_by,
             p.create_time as created_at, p.update_time as updated_at,
             r.name as rule_name,
             CASE WHEN EXISTS(
               SELECT 1 FROM batch_osd_plan_configs pc
               WHERE pc.plan_id = p.id AND pc.config_type = 'backup_config'
               AND JSON_EXTRACT(pc.config_data, '$.enableBackup') = true
             ) THEN 1 ELSE 0 END as has_backup_enabled,
             CASE WHEN EXISTS(
               SELECT 1 FROM batch_osd_backup_data bd
               WHERE bd.plan_id = p.id AND bd.status IN ('backed_up', 'restore_failed')
             ) THEN 1 ELSE 0 END as has_backup_data
      FROM batch_osd_plans p
      LEFT JOIN osd_rules r ON p.rule_id = r.id
      ORDER BY p.create_time DESC
      LIMIT 3
    `);

    console.log('完整查询结果数量:', fullQuery.length);
    fullQuery.forEach(plan => {
      console.log(`\n  📄 计划: ${plan.name} (ID: ${plan.id})`);
      console.log(`     状态: ${plan.status}`);
      console.log(`     规则: ${plan.rule_name} (ID: ${plan.rule_id})`);
      console.log(`     目标类型: ${plan.target_type}`);
      console.log(`     目标数量: ${plan.target_count}, 完成数量: ${plan.completed_count}`);
      console.log(`     备份启用: ${plan.has_backup_enabled}, 备份数据: ${plan.has_backup_data}`);
      console.log(`     创建时间: ${plan.created_at}`);

      // 解析target_ids
      try {
        const targetIds = JSON.parse(plan.target_ids || '[]');
        console.log(`     目标IDs: [${targetIds.join(', ')}]`);
      } catch (e) {
        console.log(`     目标IDs: ${plan.target_ids}`);
      }
    });

    // 6. 检查特定计划的详细数据（计划51）
    const planId = 51;
    console.log(`\n🔍 检查计划 ${planId} 的详细数据:`);

    // 检查计划考点数据
    const [planSites] = await connection.execute(`
      SELECT * FROM batch_osd_plan_sites WHERE plan_id = ?
    `, [planId]);
    console.log(`计划考点数据: ${planSites.length} 条`);
    planSites.forEach(site => {
      console.log(`  - 考点: ${site.site_name} (${site.site_id})`);
    });

    // 检查房间通道绑定数据
    const [bindings] = await connection.execute(`
      SELECT * FROM batch_osd_room_channel_bindings WHERE plan_id = ? LIMIT 5
    `, [planId]);
    console.log(`\n房间通道绑定数据: ${bindings.length} 条`);
    bindings.forEach(binding => {
      console.log(`  - 房间: ${binding.room_id}, 通道: ${binding.channel_id}`);
      console.log(`    原始OSD: ${binding.original_osd}`);
      console.log(`    目标OSD: ${binding.target_osd}`);
    });

    // 检查生成的考场数据
    const [rooms] = await connection.execute(`
      SELECT * FROM batch_osd_generated_rooms WHERE plan_id = ? LIMIT 5
    `, [planId]);
    console.log(`\n生成的考场数据: ${rooms.length} 条`);
    rooms.forEach(room => {
      console.log(`  - 考场: ${room.room_number}, 考点: ${room.site_id}`);
    });

    console.log('\n🎉 数据检查完成！');

  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 运行调试
debugPlanData();
