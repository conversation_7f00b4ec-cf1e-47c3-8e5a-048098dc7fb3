// 运行OSD备份功能数据库迁移脚本
const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'Aiwa75210!',
  database: 'patrol_system',
  charset: 'utf8mb4'
};

async function runMigration() {
  let connection;

  try {
    console.log('🔄 开始执行OSD备份功能数据库迁移...');

    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 读取迁移SQL文件
    const migrationPath = path.join(__dirname, '../database/migrations/add_osd_backup_tables.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // 分割SQL语句（按分号分割）
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);

    console.log(`📝 找到 ${statements.length} 个SQL语句`);

    // 执行每个SQL语句
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`🔄 执行语句 ${i + 1}/${statements.length}...`);

      try {
        await connection.execute(statement);
        console.log(`✅ 语句 ${i + 1} 执行成功`);
      } catch (error) {
        // 如果是表已存在的错误，可以忽略
        if (error.code === 'ER_TABLE_EXISTS_ERROR') {
          console.log(`⚠️  语句 ${i + 1} 跳过（表已存在）`);
        } else {
          console.error(`❌ 语句 ${i + 1} 执行失败:`, error.message);
          throw error;
        }
      }
    }

    console.log('🎉 OSD备份功能数据库迁移完成！');

    // 验证表是否创建成功
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME IN ('batch_osd_backup_data', 'batch_osd_backup_cleanup_jobs')
    `, [dbConfig.database]);

    console.log('📋 创建的表：');
    tables.forEach(table => {
      console.log(`  ✅ ${table.TABLE_NAME}`);
    });

  } catch (error) {
    console.error('❌ 迁移失败:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 运行迁移
runMigration();
