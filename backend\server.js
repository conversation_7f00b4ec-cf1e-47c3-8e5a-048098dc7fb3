// 主服务器文件 - 整合MySQL数据库和Mock数据
const express = require('express');
const cors = require('cors');
const { testConnection } = require('./src/config/database');

// 导入路由
const organizationRoutes = require('./src/routes/organization');
const osdRulesRoutes = require('./src/routes/osdRules');
const batchOsdRoutes = require('./src/routes/batchOsd');

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 请求日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} ${req.method} ${req.url}`);
  next();
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'patrol-system-api'
  });
});

// API路由
app.use('/api/organization', organizationRoutes);
app.use('/api/osd-rules', osdRulesRoutes);
app.use('/api/batch-osd', batchOsdRoutes);

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    code: 404,
    message: '接口不存在',
    path: req.originalUrl
  });
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  res.status(500).json({
    code: 500,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'development' ? error.message : '服务器错误'
  });
});

// 创建必需的数据库表
async function createRequiredTables() {
  const { query } = require('./src/config/database');

  try {
    // 1. 创建batch_osd_plan_configs表（如果不存在）
    await query(`
      CREATE TABLE IF NOT EXISTS batch_osd_plan_configs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        plan_id INT NOT NULL COMMENT '计划ID',
        config_type ENUM('osd_config', 'room_config', 'schedule_config', 'backup_config') NOT NULL COMMENT '配置类型',
        config_data JSON COMMENT '配置数据',
        create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        INDEX idx_plan_id (plan_id),
        INDEX idx_config_type (config_type),
        UNIQUE KEY uk_plan_config_type (plan_id, config_type)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量OSD计划配置信息表'
    `);

    // 2. 创建batch_osd_plan_site_room_bindings表（如果不存在）
    await query(`
      CREATE TABLE IF NOT EXISTS batch_osd_plan_site_room_bindings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        plan_id INT NOT NULL COMMENT '计划ID',
        site_id VARCHAR(50) NOT NULL COMMENT '考点ID',
        site_name VARCHAR(200) NOT NULL COMMENT '考点名称',
        room_id VARCHAR(50) NOT NULL COMMENT '考场ID',
        room_number VARCHAR(20) NOT NULL COMMENT '考场编号',
        channel_id VARCHAR(50) COMMENT '通道ID',
        channel_name VARCHAR(200) COMMENT '通道名称',
        osd_content TEXT COMMENT 'OSD内容',
        binding_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
        status ENUM('pending', 'bound', 'failed') DEFAULT 'pending' COMMENT '绑定状态',
        create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        INDEX idx_plan_id (plan_id),
        INDEX idx_site_id (site_id),
        INDEX idx_room_id (room_id),
        INDEX idx_channel_id (channel_id),
        INDEX idx_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量OSD计划考点考场绑定关系表'
    `);

    // 3. 创建batch_osd_backup_data表（如果不存在）
    await query(`
      CREATE TABLE IF NOT EXISTS batch_osd_backup_data (
        id INT AUTO_INCREMENT PRIMARY KEY,
        plan_id INT NOT NULL COMMENT '计划ID',
        channel_id VARCHAR(50) NOT NULL COMMENT '通道ID',
        channel_name VARCHAR(200) COMMENT '通道名称',
        site_id VARCHAR(50) COMMENT '考点ID',
        site_name VARCHAR(200) COMMENT '考点名称',
        room_id VARCHAR(50) COMMENT '考场ID',
        room_name VARCHAR(100) COMMENT '考场名称',
        original_osd_content TEXT COMMENT '原始OSD内容',
        backup_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '备份时间',
        backup_strategy ENUM('auto', 'manual') DEFAULT 'auto' COMMENT '备份策略',
        retention_days INT DEFAULT 30 COMMENT '保留天数',
        status ENUM('backed_up', 'restored', 'restore_failed', 'expired') DEFAULT 'backed_up' COMMENT '状态',
        restore_time TIMESTAMP NULL COMMENT '恢复时间',
        create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        INDEX idx_plan_id (plan_id),
        INDEX idx_channel_id (channel_id),
        INDEX idx_site_id (site_id),
        INDEX idx_room_id (room_id),
        INDEX idx_status (status),
        INDEX idx_backup_time (backup_time)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量OSD备份数据表'
    `);

    console.log('✅ 必需的数据库表已创建或确认存在');
  } catch (error) {
    console.error('❌ 创建数据库表失败:', error);
    throw error;
  }
}

// 启动服务器
async function startServer() {
  try {
    // 测试数据库连接
    console.log('🔍 测试数据库连接...');
    const dbConnected = await testConnection();

    if (!dbConnected) {
      console.log('⚠️  数据库连接失败，将使用Mock数据模式');
      // 如果数据库连接失败，可以fallback到mock-server
      const mockServer = require('./mock-server');
      return;
    }

    // 创建必需的数据库表
    console.log('🔧 检查并创建必需的数据库表...');
    await createRequiredTables();
    console.log('✅ 数据库表检查完成');

    app.listen(PORT, () => {
      console.log('🚀 服务器启动成功!');
      console.log(`📍 服务地址: http://localhost:${PORT}`);
      console.log(`🏥 健康检查: http://localhost:${PORT}/health`);
      console.log(`📊 数据库状态: ${dbConnected ? '✅ 已连接' : '❌ 未连接'}`);
      console.log('');
      console.log('📋 可用的API接口:');
      console.log('  - GET  /api/organization/tree          机构树数据');
      console.log('  - GET  /api/osd-rules                  OSD规则列表');
      console.log('  - POST /api/osd-rules                  创建OSD规则');
      console.log('  - GET  /api/batch-osd/plans            批量OSD计划列表');
      console.log('  - POST /api/batch-osd/plans            创建批量OSD计划');
      console.log('  - GET  /api/batch-osd/available-rules  可用OSD规则');
      console.log('');
    });
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭服务器...');
  process.exit(0);
});

// 启动服务器
startServer();

module.exports = app;
