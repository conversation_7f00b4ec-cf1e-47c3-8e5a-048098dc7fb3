// 简单的Mock服务器 - 不依赖数据库
const express = require('express');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 8081;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 请求日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} ${req.method} ${req.url}`);
  next();
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'patrol-system-mock-api'
  });
});

// Mock机构树数据
app.get('/api/organization/tree', (req, res) => {
  console.log('返回Mock机构树数据');
  
  const mockOrganizationTree = [
    {
      id: '51',
      name: '四川省',
      fullName: '四川省',
      shortName: '川',
      singleName: '四川',
      level: 1,
      sort: 1,
      status: 1,
      createTime: '2024-01-01 00:00:00',
      updateTime: '2024-01-01 00:00:00',
      children: [
        {
          id: '5101',
          name: '成都市',
          fullName: '成都市',
          shortName: '成都',
          singleName: '成都',
          level: 2,
          sort: 1,
          status: 1,
          createTime: '2024-01-01 00:00:00',
          updateTime: '2024-01-01 00:00:00',
          children: [
            {
              id: '510104',
              name: '锦江区',
              fullName: '锦江区',
              shortName: '锦江',
              singleName: '锦江',
              level: 3,
              sort: 1,
              status: 1,
              createTime: '2024-01-01 00:00:00',
              updateTime: '2024-01-01 00:00:00',
              examSites: [
                {
                  id: '510104001',
                  site_id: '510104001',
                  site_name: '成都市第七中学',
                  site_code: 'CD001',
                  province_id: '51',
                  province_name: '四川省',
                  city_id: '5101',
                  city_name: '成都市',
                  district_id: '510104',
                  district_name: '锦江区',
                  address: '成都市锦江区林荫街1号',
                  contact_person: '张老师',
                  contact_phone: '028-12345678',
                  room_count: 25,
                  status: 1,
                  create_time: '2024-01-01 00:00:00',
                  update_time: '2024-01-01 00:00:00'
                }
              ]
            },
            {
              id: '510105',
              name: '青羊区',
              fullName: '青羊区',
              shortName: '青羊',
              singleName: '青羊',
              level: 3,
              sort: 2,
              status: 1,
              createTime: '2024-01-01 00:00:00',
              updateTime: '2024-01-01 00:00:00',
              examSites: [
                {
                  id: '510105002',
                  site_id: '510105002',
                  site_name: '成都市石室中学',
                  site_code: 'CD002',
                  province_id: '51',
                  province_name: '四川省',
                  city_id: '5101',
                  city_name: '成都市',
                  district_id: '510105',
                  district_name: '青羊区',
                  address: '成都市青羊区光华大道一段1386号',
                  contact_person: '李老师',
                  contact_phone: '028-87654321',
                  room_count: 30,
                  status: 1,
                  create_time: '2024-01-01 00:00:00',
                  update_time: '2024-01-01 00:00:00'
                }
              ]
            }
          ]
        }
      ]
    }
  ];

  res.json({
    code: 200,
    message: '获取成功',
    data: mockOrganizationTree
  });
});

// Mock OSD规则数据
app.get('/api/osd-rules', (req, res) => {
  console.log('返回Mock OSD规则数据');
  
  const mockOsdRules = [
    {
      id: 1,
      name: '标准考试OSD模板',
      description: '适用于标准化考试的OSD显示模板',
      template_content: '考点：{site_name} 考场：{room_number} 时间：{current_time}',
      font_size: 24,
      font_color: '#FFFFFF',
      background_color: '#000000',
      position_x: 10,
      position_y: 10,
      display_duration: 0,
      is_active: 1,
      create_time: '2024-01-01 00:00:00',
      update_time: '2024-01-01 00:00:00'
    },
    {
      id: 2,
      name: '研究生考试OSD模板',
      description: '专用于研究生考试的OSD显示模板',
      template_content: '研究生考试 考点：{site_name} 考场：{room_number}',
      font_size: 20,
      font_color: '#FFFF00',
      background_color: '#000080',
      position_x: 15,
      position_y: 15,
      display_duration: 0,
      is_active: 1,
      create_time: '2024-01-01 00:00:00',
      update_time: '2024-01-01 00:00:00'
    }
  ];

  res.json({
    code: 200,
    message: '获取成功',
    data: mockOsdRules
  });
});

// Mock批量OSD计划数据
app.get('/api/batch-osd/plans', (req, res) => {
  console.log('返回Mock批量OSD计划数据');
  
  const mockPlans = [
    {
      id: 1,
      name: '2024年研究生考试OSD设置',
      description: '用于2024年研究生考试的OSD批量设置',
      rule_id: 1,
      rule_name: '标准考试OSD模板',
      target_type: 'custom',
      target_count: 55,
      execute_mode: 'scheduled',
      scheduled_time: '2024-12-20 08:00:00',
      concurrency: 5,
      status: 'completed',
      progress: 100,
      success_count: 55,
      failed_count: 0,
      create_time: '2024-01-15 10:00:00',
      update_time: '2024-01-15 12:00:00',
      creator: 'admin'
    },
    {
      id: 2,
      name: '期末考试OSD批量设置',
      description: '用于期末考试的OSD批量设置',
      rule_id: 2,
      rule_name: '研究生考试OSD模板',
      target_type: 'all',
      target_count: 120,
      execute_mode: 'immediate',
      scheduled_time: null,
      concurrency: 3,
      status: 'running',
      progress: 65,
      success_count: 78,
      failed_count: 2,
      create_time: '2024-01-20 14:30:00',
      update_time: '2024-01-20 15:45:00',
      creator: 'admin'
    }
  ];

  const { page = 1, size = 20, keyword = '', status = '' } = req.query;
  
  let filteredPlans = mockPlans;
  
  // 关键词过滤
  if (keyword) {
    filteredPlans = filteredPlans.filter(plan => 
      plan.name.includes(keyword) || plan.description.includes(keyword)
    );
  }
  
  // 状态过滤
  if (status) {
    filteredPlans = filteredPlans.filter(plan => plan.status === status);
  }

  res.json({
    code: 200,
    message: '获取成功',
    data: {
      list: filteredPlans,
      total: filteredPlans.length,
      page: parseInt(page),
      size: parseInt(size)
    }
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    code: 404,
    message: '接口不存在',
    path: req.originalUrl
  });
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  res.status(500).json({
    code: 500,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'development' ? error.message : '服务器错误'
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log('🚀 Mock服务器启动成功!');
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`🏥 健康检查: http://localhost:${PORT}/health`);
  console.log('📊 数据库状态: ❌ Mock模式（无数据库连接）');
  console.log('');
  console.log('📋 可用的Mock API接口:');
  console.log('  - GET  /api/organization/tree          机构树数据');
  console.log('  - GET  /api/osd-rules                  OSD规则列表');
  console.log('  - GET  /api/batch-osd/plans            批量OSD计划列表');
  console.log('');
});

module.exports = app;
