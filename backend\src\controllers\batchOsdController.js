// 批量OSD设置控制器
const batchOsdService = require('../services/batchOsdService');
const osdRulesService = require('../services/osdRulesService');
const batchOsdPersistenceService = require('../services/batchOsdPersistenceService');

class BatchOsdController {

  // 获取批量OSD计划列表
  async getBatchOsdPlans(req, res) {
    try {
      const { page, size, keyword, status } = req.query;
      const result = await batchOsdService.getBatchOsdPlans({
        page: parseInt(page) || 1,
        size: parseInt(size) || 20,
        keyword,
        status
      });

      res.json({
        code: 200,
        message: '获取成功',
        data: result
      });
    } catch (error) {
      console.error('获取批量OSD计划列表失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取批量OSD计划列表失败',
        error: error.message
      });
    }
  }

  // 获取批量OSD计划详情
  async getBatchOsdPlanById(req, res) {
    try {
      const { id } = req.params;
      const plan = await batchOsdService.getBatchOsdPlanById(id);

      if (!plan) {
        return res.status(404).json({
          code: 404,
          message: '批量OSD计划不存在'
        });
      }

      res.json({
        code: 200,
        message: '获取成功',
        data: plan
      });
    } catch (error) {
      console.error('获取批量OSD计划详情失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取批量OSD计划详情失败',
        error: error.message
      });
    }
  }

  // 创建批量OSD计划
  async createBatchOsdPlan(req, res) {
    try {
      const {
        name, description, rule_id, target_type, target_ids,
        execute_mode, schedule_time, concurrency
      } = req.body;

      // 验证必填字段
      if (!name || !rule_id || !target_type || !execute_mode) {
        return res.status(400).json({
          code: 400,
          message: '计划名称、OSD规则、目标类型和执行方式不能为空'
        });
      }

      // 验证OSD规则是否存在
      const rule = await osdRulesService.getOsdRuleById(rule_id);
      if (!rule) {
        return res.status(400).json({
          code: 400,
          message: '指定的OSD规则不存在'
        });
      }

      const planId = await batchOsdService.createBatchOsdPlan({
        name,
        description,
        rule_id,
        target_type,
        target_ids: target_ids || [],
        execute_mode,
        schedule_time,
        concurrency: concurrency || 3,
        created_by: req.user?.username || 'system'
      });

      res.json({
        code: 200,
        message: '创建成功',
        data: { id: planId }
      });
    } catch (error) {
      console.error('创建批量OSD计划失败:', error);
      res.status(500).json({
        code: 500,
        message: '创建批量OSD计划失败',
        error: error.message
      });
    }
  }

  // 更新批量OSD计划
  async updateBatchOsdPlan(req, res) {
    try {
      const { id } = req.params;
      const {
        name, description, rule_id, target_type, target_ids,
        execute_mode, schedule_time, concurrency
      } = req.body;

      // 验证必填字段
      if (!name || !rule_id || !target_type || !execute_mode) {
        return res.status(400).json({
          code: 400,
          message: '计划名称、OSD规则、目标类型和执行方式不能为空'
        });
      }

      // 验证OSD规则是否存在
      const rule = await osdRulesService.getOsdRuleById(rule_id);
      if (!rule) {
        return res.status(400).json({
          code: 400,
          message: '指定的OSD规则不存在'
        });
      }

      const success = await batchOsdService.updateBatchOsdPlan(id, {
        name,
        description,
        rule_id,
        target_type,
        target_ids: target_ids || [],
        execute_mode,
        schedule_time,
        concurrency: concurrency || 3,
        updated_by: req.user?.username || 'system'
      });

      if (!success) {
        return res.status(404).json({
          code: 404,
          message: '批量OSD计划不存在'
        });
      }

      res.json({
        code: 200,
        message: '更新成功'
      });
    } catch (error) {
      console.error('更新批量OSD计划失败:', error);
      res.status(500).json({
        code: 500,
        message: '更新批量OSD计划失败',
        error: error.message
      });
    }
  }

  // 删除批量OSD计划
  async deleteBatchOsdPlan(req, res) {
    try {
      const { id } = req.params;

      const success = await batchOsdService.deleteBatchOsdPlan(id);
      if (!success) {
        return res.status(404).json({
          code: 404,
          message: '批量OSD计划不存在'
        });
      }

      res.json({
        code: 200,
        message: '删除成功'
      });
    } catch (error) {
      console.error('删除批量OSD计划失败:', error);
      res.status(500).json({
        code: 500,
        message: '删除批量OSD计划失败',
        error: error.message
      });
    }
  }

  // 批量删除计划
  async batchDeleteBatchOsdPlans(req, res) {
    try {
      const { ids } = req.body;

      if (!ids || !Array.isArray(ids) || ids.length === 0) {
        return res.status(400).json({
          code: 400,
          message: '请选择要删除的计划'
        });
      }

      const deletedCount = await batchOsdService.batchDeleteBatchOsdPlans(ids);

      res.json({
        code: 200,
        message: `成功删除 ${deletedCount} 个计划`
      });
    } catch (error) {
      console.error('批量删除计划失败:', error);
      res.status(500).json({
        code: 500,
        message: '批量删除计划失败',
        error: error.message
      });
    }
  }

  // 执行批量OSD计划
  async executeBatchOsdPlan(req, res) {
    try {
      const { id } = req.params;

      const success = await batchOsdService.executeBatchOsdPlan(id);
      if (!success) {
        return res.status(400).json({
          code: 400,
          message: '计划不存在或状态不允许执行'
        });
      }

      // 这里可以添加实际的执行逻辑
      // 比如发送到消息队列进行异步处理

      res.json({
        code: 200,
        message: '计划已开始执行'
      });
    } catch (error) {
      console.error('执行批量OSD计划失败:', error);
      res.status(500).json({
        code: 500,
        message: '执行批量OSD计划失败',
        error: error.message
      });
    }
  }

  // 暂停批量OSD计划
  async pauseBatchOsdPlan(req, res) {
    try {
      const { id } = req.params;

      const success = await batchOsdService.pauseBatchOsdPlan(id);
      if (!success) {
        return res.status(400).json({
          code: 400,
          message: '计划不存在或状态不允许暂停'
        });
      }

      res.json({
        code: 200,
        message: '计划已暂停'
      });
    } catch (error) {
      console.error('暂停批量OSD计划失败:', error);
      res.status(500).json({
        code: 500,
        message: '暂停批量OSD计划失败',
        error: error.message
      });
    }
  }

  // 取消批量OSD计划
  async cancelBatchOsdPlan(req, res) {
    try {
      const { id } = req.params;

      const success = await batchOsdService.cancelBatchOsdPlan(id);
      if (!success) {
        return res.status(400).json({
          code: 400,
          message: '计划不存在或状态不允许取消'
        });
      }

      res.json({
        code: 200,
        message: '计划已取消'
      });
    } catch (error) {
      console.error('取消批量OSD计划失败:', error);
      res.status(500).json({
        code: 500,
        message: '取消批量OSD计划失败',
        error: error.message
      });
    }
  }

  // 获取可用的OSD规则
  async getAvailableOsdRules(req, res) {
    try {
      const rules = await osdRulesService.getAvailableOsdRules();

      res.json({
        code: 200,
        message: '获取成功',
        data: rules
      });
    } catch (error) {
      console.error('获取可用OSD规则失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取可用OSD规则失败',
        error: error.message
      });
    }
  }

  // 获取计划执行统计
  async getPlanExecutionStats(req, res) {
    try {
      const { id } = req.params;
      const stats = await batchOsdService.getPlanExecutionStats(id);

      if (!stats) {
        return res.status(404).json({
          code: 404,
          message: '计划不存在'
        });
      }

      res.json({
        code: 200,
        message: '获取成功',
        data: stats
      });
    } catch (error) {
      console.error('获取计划执行统计失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取计划执行统计失败',
        error: error.message
      });
    }
  }

  // ==================== 数据持久化相关API ====================

  /**
   * 创建带数据持久化的批量OSD计划
   */
  async createBatchOsdPlanWithPersistence(req, res) {
    try {
      const {
        name, description, rule_id, target_type, target_ids,
        execute_mode, schedule_time, concurrency, selected_sites,
        generated_rooms, room_channel_bindings, osd_config, room_config, schedule_config, backup_config
      } = req.body;

      console.log('收到创建计划请求:', {
        name, description, rule_id, target_type,
        selected_sites_count: selected_sites?.length || 0,
        generated_rooms_count: generated_rooms?.length || 0,
        bindings_count: room_channel_bindings?.length || 0
      });

      // 验证必填字段
      if (!name || !rule_id || !target_type || !execute_mode) {
        return res.status(400).json({
          code: 400,
          message: '计划名称、OSD规则、目标类型和执行方式不能为空'
        });
      }

      // 验证OSD规则是否存在
      const rule = await osdRulesService.getOsdRuleById(rule_id);
      if (!rule) {
        return res.status(400).json({
          code: 400,
          message: '指定的OSD规则不存在'
        });
      }

      const planData = {
        name,
        description,
        rule_id,
        target_type,
        target_ids: target_ids || [],
        execute_mode,
        schedule_time,
        concurrency: concurrency || 3,
        created_by: req.user?.username || 'system'
      };

      // 创建完整的计划数据，包含所有相关信息
      const fullPlanData = {
        ...planData,
        selected_sites: selected_sites || [],
        generated_rooms: generated_rooms || [],
        room_channel_bindings: room_channel_bindings || [],
        osd_config: osd_config || {},
        room_config: room_config || {},
        schedule_config: schedule_config || {},
        backup_config: backup_config || { enableBackup: false }
      };

      const planId = await batchOsdService.createBatchOsdPlanWithFullData(fullPlanData);

      res.json({
        code: 200,
        message: '创建成功',
        data: { id: planId }
      });
    } catch (error) {
      console.error('创建批量OSD计划失败:', error);
      res.status(500).json({
        code: 500,
        message: '创建批量OSD计划失败',
        error: error.message
      });
    }
  }

  /**
   * 执行带数据持久化的批量OSD计划
   */
  async executeBatchOsdPlanWithPersistence(req, res) {
    try {
      const { id } = req.params;

      const success = await batchOsdService.executeBatchOsdPlanWithPersistence(id);
      if (!success) {
        return res.status(400).json({
          code: 400,
          message: '计划不存在或状态不允许执行'
        });
      }

      res.json({
        code: 200,
        message: '计划已开始执行'
      });
    } catch (error) {
      console.error('执行批量OSD计划失败:', error);
      res.status(500).json({
        code: 500,
        message: '执行批量OSD计划失败',
        error: error.message
      });
    }
  }

  /**
   * 暂停批量OSD计划
   */
  async pauseBatchOsdPlan(req, res) {
    try {
      const { id } = req.params;

      console.log(`暂停批量OSD计划: ${id}`);

      const result = await batchOsdService.pauseBatchOsdPlan(id);

      res.json({
        code: 200,
        message: '计划已暂停',
        data: result
      });
    } catch (error) {
      console.error('暂停批量OSD计划失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '暂停计划失败'
      });
    }
  }

  /**
   * 继续执行批量OSD计划
   */
  async resumeBatchOsdPlan(req, res) {
    try {
      const { id } = req.params;

      console.log(`继续执行批量OSD计划: ${id}`);

      const result = await batchOsdService.resumeBatchOsdPlan(id);

      res.json({
        code: 200,
        message: '计划已继续执行',
        data: result
      });
    } catch (error) {
      console.error('继续执行批量OSD计划失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '继续执行计划失败'
      });
    }
  }

  /**
   * 取消批量OSD计划
   */
  async cancelBatchOsdPlan(req, res) {
    try {
      const { id } = req.params;

      console.log(`取消批量OSD计划: ${id}`);

      const result = await batchOsdService.cancelBatchOsdPlan(id);

      res.json({
        code: 200,
        message: '计划已取消',
        data: result
      });
    } catch (error) {
      console.error('取消批量OSD计划失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '取消计划失败'
      });
    }
  }

  /**
   * 一键恢复OSD备份
   */
  async restoreOsdBackup(req, res) {
    try {
      const { id } = req.params;

      // 检查计划是否存在且支持备份恢复
      const plan = await batchOsdService.getBatchOsdPlanById(id);
      if (!plan) {
        return res.status(404).json({
          code: 404,
          message: '计划不存在'
        });
      }

      // 检查是否启用了备份功能
      const planDetail = await batchOsdService.getBatchOsdPlanDetailWithPersistence(id);
      if (!planDetail.backup_config?.enableBackup) {
        return res.status(400).json({
          code: 400,
          message: '该计划未启用OSD备份功能'
        });
      }

      // 执行恢复操作
      const success = await batchOsdService.restoreOsdBackup(id);
      if (!success) {
        return res.status(400).json({
          code: 400,
          message: '恢复失败，可能没有可用的备份数据'
        });
      }

      res.json({
        code: 200,
        message: 'OSD备份恢复已开始执行'
      });
    } catch (error) {
      console.error('恢复OSD备份失败:', error);
      res.status(500).json({
        code: 500,
        message: '恢复OSD备份失败',
        error: error.message
      });
    }
  }

  /**
   * 获取计划详细信息（包含持久化数据）
   */
  async getBatchOsdPlanDetailWithPersistence(req, res) {
    try {
      const { id } = req.params;

      const plan = await batchOsdService.getBatchOsdPlanDetailWithPersistence(id);
      if (!plan) {
        return res.status(404).json({
          code: 404,
          message: '批量OSD计划不存在'
        });
      }

      res.json({
        code: 200,
        message: '获取成功',
        data: plan
      });
    } catch (error) {
      console.error('获取批量OSD计划详情失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取批量OSD计划详情失败',
        error: error.message
      });
    }
  }

  /**
   * 获取计划选定的考点列表
   */
  async getPlanSites(req, res) {
    try {
      const { id } = req.params;

      const sites = await batchOsdPersistenceService.getPlanSites(id);

      res.json({
        code: 200,
        message: '获取成功',
        data: sites
      });
    } catch (error) {
      console.error('获取计划考点列表失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取计划考点列表失败',
        error: error.message
      });
    }
  }

  /**
   * 获取考点的考场列表
   */
  async getSiteExamRooms(req, res) {
    try {
      const { planId, siteId } = req.params;

      const rooms = await batchOsdPersistenceService.getSiteExamRooms(planId, siteId);

      res.json({
        code: 200,
        message: '获取成功',
        data: rooms
      });
    } catch (error) {
      console.error('获取考点考场列表失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取考点考场列表失败',
        error: error.message
      });
    }
  }

  /**
   * 获取计划执行日志（支持筛选）
   */
  async getPlanExecutionLogsWithFilter(req, res) {
    try {
      const { id } = req.params;
      const { site_id, status, execution_type, limit } = req.query;

      const options = {};
      if (site_id) options.siteId = site_id;
      if (status) options.status = status;
      if (execution_type) options.executionType = execution_type;
      if (limit) options.limit = parseInt(limit);

      const logs = await batchOsdPersistenceService.getPlanExecutionLogs(id, options);

      res.json({
        code: 200,
        message: '获取成功',
        data: logs
      });
    } catch (error) {
      console.error('获取计划执行日志失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取计划执行日志失败',
        error: error.message
      });
    }
  }

  /**
   * 获取OSD备份恢复结果
   */
  async getOsdBackupRestoreResults(req, res) {
    try {
      const { id } = req.params;

      // 检查计划是否存在
      const plan = await batchOsdService.getBatchOsdPlanById(id);
      if (!plan) {
        return res.status(404).json({
          code: 404,
          message: '计划不存在'
        });
      }

      // 获取备份恢复结果
      const batchOsdPersistenceService = require('../services/batchOsdPersistenceService');
      const backupResults = await batchOsdPersistenceService.getOsdBackupDataWithRestoreResults(id);

      // 统计恢复结果
      const stats = {
        total: backupResults.length,
        backed_up: backupResults.filter(item => item.status === 'backed_up').length,
        restored: backupResults.filter(item => item.status === 'restored').length,
        restore_failed: backupResults.filter(item => item.status === 'restore_failed').length
      };

      res.json({
        code: 200,
        message: '获取成功',
        data: {
          plan: {
            id: plan.id,
            name: plan.name,
            description: plan.description
          },
          stats: stats,
          results: backupResults
        }
      });
    } catch (error) {
      console.error('获取OSD备份恢复结果失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取OSD备份恢复结果失败',
        error: error.message
      });
    }
  }

  // 获取计划进度
  async getPlanProgress(req, res) {
    try {
      const { planId } = req.params;
      const stats = await batchOsdPersistenceService.getPlanProgress(planId);

      if (!stats) {
        return res.status(404).json({
          code: 404,
          message: '计划不存在'
        });
      }

      res.json({
        code: 200,
        message: '获取成功',
        data: stats
      });
    } catch (error) {
      console.error('获取计划进度失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取计划进度失败',
        error: error.message
      });
    }
  }

  // 获取考点下所有通道执行明细
  async getSiteChannels(req, res) {
    try {
      const { planId, siteId } = req.params;
      const channels = await batchOsdPersistenceService.getSiteChannels(planId, siteId);

      res.json({
        code: 200,
        message: '获取成功',
        data: channels
      });
    } catch (error) {
      console.error('获取考点通道列表失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取考点通道列表失败',
        error: error.message
      });
    }
  }

  // 单通道重试
  async retryChannel(req, res) {
    try {
      const { bindingId } = req.body;
      if (!bindingId) {
        return res.status(400).json({
          code: 400,
          message: 'bindingId 不能为空'
        });
      }

      const success = await batchOsdPersistenceService.retryChannel(bindingId);
      if (!success) {
        return res.status(400).json({
          code: 400,
          message: '通道不存在或状态不允许重试'
        });
      }

      res.json({
        code: 200,
        message: '通道重试已开始'
      });
    } catch (error) {
      console.error('单通道重试失败:', error);
      res.status(500).json({
        code: 500,
        message: '单通道重试失败',
        error: error.message
      });
    }
  }

  // 获取计划实时监控数据
  async getPlanMonitorData(req, res) {
    try {
      const { id } = req.params;

      // 获取计划基本信息
      const plan = await batchOsdService.getBatchOsdPlanById(id);
      if (!plan) {
        return res.status(404).json({
          code: 404,
          message: '批量OSD计划不存在'
        });
      }

      // 获取考点数据和通道绑定数据
      const sites = await batchOsdService.getPlanSites(id);
      const bindings = await batchOsdService.getPlanChannelBindings(id);

      // 按考点统计执行情况
      const siteStats = new Map();

      // 初始化考点统计
      sites.forEach(site => {
        siteStats.set(site.site_id, {
          site_id: site.site_id,
          site_name: site.site_name,
          total_channels: 0,
          completed_channels: 0,
          success_channels: 0,
          failed_channels: 0,
          status: 'pending' // pending, running, success, failed
        });
      });

      // 统计每个考点的通道执行情况
      bindings.forEach(binding => {
        const siteId = binding.site_id || 'unknown';
        if (siteStats.has(siteId)) {
          const stat = siteStats.get(siteId);
          stat.total_channels++;

          // 注意：这里的status是我们模拟的'completed'状态
          if (['completed', 'success', 'failed'].includes(binding.status)) {
            stat.completed_channels++;
          }
          if (binding.status === 'completed' || binding.status === 'success') {
            stat.success_channels++;
          }
          if (binding.status === 'failed') {
            stat.failed_channels++;
          }
        }
      });

      // 计算考点级别的统计
      let completedSites = 0;
      let successSites = 0;
      let failedSites = 0;

      siteStats.forEach(stat => {
        // 判断考点状态
        if (stat.completed_channels === stat.total_channels && stat.total_channels > 0) {
          // 考点所有通道都完成了
          completedSites++;
          if (stat.failed_channels === 0) {
            stat.status = 'success';
            successSites++;
          } else {
            stat.status = 'failed';
            failedSites++;
          }
        } else if (stat.completed_channels > 0) {
          stat.status = 'running';
        }
      });

      const totalSites = sites.length;
      const pendingSites = totalSites - completedSites;

      // 构造监控数据
      const monitorData = {
        plan: {
          id: plan.id,
          name: plan.name,
          status: plan.status,
          start_time: plan.start_time,
          end_time: plan.end_time,
          target_count: totalSites, // 考点数量
          completed_count: completedSites, // 已完成的考点数量
          success_count: successSites, // 成功的考点数量
          failed_count: failedSites, // 失败的考点数量
          progress: totalSites > 0 ? Math.round((completedSites / totalSites) * 100) : 0
        },
        stats: {
          total: totalSites,
          completed: completedSites,
          success: successSites,
          failed: failedSites,
          pending: pendingSites
        },
        sites: Array.from(siteStats.values()), // 考点详细信息
        logs: bindings.slice(0, 50), // 返回最近50条通道记录
        realtime: {
          timestamp: new Date().toISOString(),
          is_running: plan.status === 'running',
          estimated_completion: null
        }
      };

      res.json({
        code: 200,
        message: '获取成功',
        data: monitorData
      });
    } catch (error) {
      console.error('获取计划监控数据失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取计划监控数据失败',
        error: error.message
      });
    }
  }

  // 获取考点的通道执行详情
  async getSiteChannels(req, res) {
    try {
      const { planId, siteId } = req.params;
      console.log(`获取考点通道详情，计划ID: ${planId}, 考点ID: ${siteId}`);

      const result = await batchOsdService.getSiteChannelDetails(planId, siteId);

      res.json({
        code: 200,
        message: '获取成功',
        data: result
      });
    } catch (error) {
      console.error('获取考点通道详情失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取考点通道详情失败',
        error: error.message
      });
    }
  }

  // 获取计划的目标考点列表
  async getPlanTargetSites(req, res) {
    try {
      const { planId } = req.params;
      console.log(`获取计划目标考点列表，计划ID: ${planId}`);

      const result = await batchOsdService.getPlanTargetSites(planId);

      res.json({
        code: 200,
        message: '获取成功',
        data: result
      });
    } catch (error) {
      console.error('获取计划目标考点列表失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取计划目标考点列表失败',
        error: error.message
      });
    }
  }

  // 获取计划的房间通道绑定信息
  async getPlanRoomChannelBindings(req, res) {
    try {
      const { planId } = req.params;
      console.log(`获取计划房间通道绑定信息，计划ID: ${planId}`);

      const result = await batchOsdService.getPlanRoomChannelBindings(planId);

      res.json({
        code: 200,
        message: '获取成功',
        data: result
      });
    } catch (error) {
      console.error('获取计划房间通道绑定信息失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取计划房间通道绑定信息失败',
        error: error.message
      });
    }
  }

  // 获取计划的OSD设置数据
  async getPlanOsdData(req, res) {
    try {
      const { planId } = req.params;
      console.log(`获取计划OSD设置数据，计划ID: ${planId}`);

      const result = await batchOsdService.getPlanOsdData(planId);

      res.json({
        code: 200,
        message: '获取成功',
        data: result
      });
    } catch (error) {
      console.error('获取计划OSD设置数据失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取计划OSD设置数据失败',
        error: error.message
      });
    }
  }




}

module.exports = new BatchOsdController();
