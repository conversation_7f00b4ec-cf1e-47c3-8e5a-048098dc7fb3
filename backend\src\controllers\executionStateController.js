const executionStateService = require('../services/executionStateService');

/**
 * 批量OSD执行状态管理控制器
 */
class ExecutionStateController {
  // 保存执行状态
  async saveExecutionState(req, res) {
    try {
      const { planId } = req.params;
      const stateData = req.body;
      
      console.log('📥 接收到保存执行状态请求，计划ID:', planId);
      
      const result = await executionStateService.saveExecutionState(planId, stateData);
      
      res.json({
        code: 200,
        message: '执行状态保存成功',
        data: result
      });
    } catch (error) {
      console.error('保存执行状态失败:', error);
      res.status(500).json({
        code: 500,
        message: '保存执行状态失败',
        error: error.message
      });
    }
  }

  // 获取执行状态
  async getExecutionState(req, res) {
    try {
      const { planId } = req.params;
      const { executionPhase } = req.query;
      
      console.log('📥 接收到获取执行状态请求，计划ID:', planId, '执行阶段:', executionPhase);
      
      const state = await executionStateService.getExecutionState(planId, executionPhase);
      
      if (!state) {
        return res.json({
          code: 404,
          message: '未找到执行状态',
          data: null
        });
      }
      
      res.json({
        code: 200,
        message: '获取执行状态成功',
        data: state
      });
    } catch (error) {
      console.error('获取执行状态失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取执行状态失败',
        error: error.message
      });
    }
  }

  // 删除执行状态
  async deleteExecutionState(req, res) {
    try {
      const { planId } = req.params;
      
      console.log('📥 接收到删除执行状态请求，计划ID:', planId);
      
      const result = await executionStateService.deleteExecutionState(planId);
      
      res.json({
        code: 200,
        message: '执行状态删除成功',
        data: result
      });
    } catch (error) {
      console.error('删除执行状态失败:', error);
      res.status(500).json({
        code: 500,
        message: '删除执行状态失败',
        error: error.message
      });
    }
  }

  // 获取执行状态历史
  async getExecutionStateHistory(req, res) {
    try {
      const { planId } = req.params;
      
      console.log('📥 接收到获取执行状态历史请求，计划ID:', planId);
      
      const history = await executionStateService.getExecutionStateHistory(planId);
      
      res.json({
        code: 200,
        message: '获取执行状态历史成功',
        data: history
      });
    } catch (error) {
      console.error('获取执行状态历史失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取执行状态历史失败',
        error: error.message
      });
    }
  }

  // 清理过期状态
  async cleanupOldStates(req, res) {
    try {
      const { planId } = req.params;
      const { keepCount = 10 } = req.query;
      
      console.log('📥 接收到清理过期状态请求，计划ID:', planId, '保留数量:', keepCount);
      
      const result = await executionStateService.cleanupOldStates(planId, parseInt(keepCount));
      
      res.json({
        code: 200,
        message: '清理过期状态成功',
        data: result
      });
    } catch (error) {
      console.error('清理过期状态失败:', error);
      res.status(500).json({
        code: 500,
        message: '清理过期状态失败',
        error: error.message
      });
    }
  }
}

module.exports = new ExecutionStateController();
