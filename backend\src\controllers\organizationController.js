const { query } = require('../config/database');

/**
 * 获取机构树数据
 */
const getOrganizationTree = async (req, res) => {
  try {
    const { keyword, level, status, parentId } = req.query;

    // 构建查询条件
    let whereConditions = [];
    let params = [];

    if (keyword) {
      whereConditions.push('(ad.name LIKE ? OR ad.short_name LIKE ? OR ad.id LIKE ?)');
      params.push(`%${keyword}%`, `%${keyword}%`, `%${keyword}%`);
    }

    if (level) {
      whereConditions.push('ad.level = ?');
      params.push(level);
    }

    if (status !== undefined) {
      whereConditions.push('ad.status = ?');
      params.push(status);
    }

    if (parentId) {
      whereConditions.push('ad.parent_id = ?');
      params.push(parentId);
    }

    const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

    // 查询行政区划数据
    const adminQuery = `
      SELECT
        ad.id,
        ad.parent_id,
        ad.name as fullName,
        ad.short_name as shortName,
        ad.single_name as singleName,
        ad.level,
        ad.sort_order as sort,
        ad.status,
        ad.created_at as createTime,
        ad.updated_at as updateTime,
        COUNT(DISTINCT child.id) as childrenCount,
        COUNT(DISTINCT es.id) as examSiteCount
      FROM administrative_divisions ad
      LEFT JOIN administrative_divisions child ON child.parent_id = ad.id
      LEFT JOIN exam_sites es ON (
        (ad.level = 'district' AND es.parent_id = ad.id AND es.parent_level = 'district') OR
        (ad.level = 'city' AND (
          (es.parent_id = ad.id AND es.parent_level = 'city') OR
          (es.parent_id IN (SELECT id FROM administrative_divisions WHERE parent_id = ad.id) AND es.parent_level = 'district')
        )) OR
        (ad.level = 'province' AND (
          (es.parent_id = ad.id AND es.parent_level = 'province') OR
          (es.parent_id IN (SELECT id FROM administrative_divisions WHERE parent_id = ad.id) AND es.parent_level = 'city') OR
          (es.parent_id IN (
            SELECT d.id FROM administrative_divisions d
            JOIN administrative_divisions c ON d.parent_id = c.id
            WHERE c.parent_id = ad.id
          ) AND es.parent_level = 'district')
        ))
      )
      ${whereClause}
      GROUP BY ad.id, ad.parent_id, ad.name, ad.short_name, ad.single_name, ad.level, ad.sort_order, ad.status, ad.created_at, ad.updated_at
      ORDER BY ad.sort_order, ad.id
    `;

    const adminRows = await query(adminQuery, params);

    // 查询考点数据
    const examSiteQuery = `
      SELECT
        es.id,
        es.parent_id as parentId,
        es.parent_level,
        es.name as fullName,
        es.short_name as shortName,
        es.uri,
        'examSite' as level,
        es.sort_order as sort,
        es.status,
        es.created_at as createTime,
        es.updated_at as updateTime,
        0 as childrenCount,
        1 as examSiteCount
      FROM exam_sites es
      ${keyword ? 'WHERE (es.name LIKE ? OR es.short_name LIKE ? OR es.id LIKE ?)' : ''}
      ORDER BY es.sort_order, es.id
    `;

    const examParams = keyword ? [`%${keyword}%`, `%${keyword}%`, `%${keyword}%`] : [];
    const examRows = await query(examSiteQuery, examParams);

    // 合并数据
    const allNodes = [...adminRows, ...examRows];

    // 构建树形结构
    const buildTree = (nodes, parentId = null) => {
      return nodes
        .filter(node => node.parent_id === parentId || node.parentId === parentId)
        .map(node => ({
          ...node,
          children: buildTree(nodes, node.id)
        }));
    };

    const tree = buildTree(allNodes);

    res.json({
      code: 200,
      message: '获取成功',
      data: tree
    });
  } catch (error) {
    console.error('获取机构树失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取机构树失败',
      error: error.message
    });
  }
};

/**
 * 获取机构详情
 */
const getOrganizationDetail = async (req, res) => {
  try {
    const { id } = req.params;

    // 先查询行政区划
    const adminRows = await query(`
      SELECT
        id,
        parent_id,
        name as fullName,
        short_name as shortName,
        single_name as singleName,
        level,
        sort_order as sort,
        status,
        created_at as createTime,
        updated_at as updateTime
      FROM administrative_divisions
      WHERE id = ?
    `, [id]);

    if (adminRows.length > 0) {
      // 统计子级数量
      const childCount = await query(
        'SELECT COUNT(*) as count FROM administrative_divisions WHERE parent_id = ?',
        [id]
      );

      // 统计考点数量
      let examSiteCount = 0;
      if (adminRows[0].level === 'district') {
        const examCount = await query(
          'SELECT COUNT(*) as count FROM exam_sites WHERE parent_id = ? AND parent_level = ?',
          [id, 'district']
        );
        examSiteCount = examCount[0].count;
      } else if (adminRows[0].level === 'city') {
        const examCount = await query(`
          SELECT COUNT(*) as count FROM exam_sites
          WHERE (parent_id = ? AND parent_level = 'city')
             OR (parent_id IN (SELECT id FROM administrative_divisions WHERE parent_id = ?) AND parent_level = 'district')
        `, [id, id]);
        examSiteCount = examCount[0].count;
      } else if (adminRows[0].level === 'province') {
        const examCount = await query(`
          SELECT COUNT(*) as count FROM exam_sites
          WHERE (parent_id = ? AND parent_level = 'province')
             OR (parent_id IN (SELECT id FROM administrative_divisions WHERE parent_id = ?) AND parent_level = 'city')
             OR (parent_id IN (
               SELECT d.id FROM administrative_divisions d
               JOIN administrative_divisions c ON d.parent_id = c.id
               WHERE c.parent_id = ?
             ) AND parent_level = 'district')
        `, [id, id, id]);
        examSiteCount = examCount[0].count;
      }

      const result = {
        ...adminRows[0],
        childrenCount: childCount[0].count,
        examSiteCount
      };

      return res.json({
        code: 200,
        message: '获取成功',
        data: result
      });
    }

    // 查询考点
    const examRows = await query(`
      SELECT
        id,
        parent_id as parentId,
        parent_level,
        name as fullName,
        short_name as shortName,
        uri,
        'examSite' as level,
        sort_order as sort,
        status,
        created_at as createTime,
        updated_at as updateTime,
        0 as childrenCount,
        1 as examSiteCount
      FROM exam_sites
      WHERE id = ?
    `, [id]);

    if (examRows.length > 0) {
      return res.json({
        code: 200,
        message: '获取成功',
        data: examRows[0]
      });
    }

    res.status(404).json({
      code: 404,
      message: '机构不存在'
    });
  } catch (error) {
    console.error('获取机构详情失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取机构详情失败',
      error: error.message
    });
  }
};

/**
 * 创建机构
 */
const createOrganization = async (req, res) => {
  try {
    const { parentId, level, code, fullName, shortName, singleName, uri, sort, status, parentLevel } = req.body;

    // 验证机构码是否已存在
    const existing = await query(
      'SELECT id FROM administrative_divisions WHERE id = ? UNION SELECT id FROM exam_sites WHERE id = ?',
      [code, code]
    );

    if (existing.length > 0) {
      return res.status(400).json({
        code: 400,
        message: '机构码已存在'
      });
    }

    if (level === 'examSite') {
      // 创建考点，需要确定parent_level
      let actualParentLevel = parentLevel;
      if (!actualParentLevel) {
        // 如果没有指定parent_level，根据parentId查询
        const parentInfo = await query(
          'SELECT level FROM administrative_divisions WHERE id = ?',
          [parentId]
        );
        actualParentLevel = parentInfo.length > 0 ? parentInfo[0].level : 'district';
      }

      await query(`
        INSERT INTO exam_sites (id, parent_id, parent_level, name, short_name, uri, sort_order, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [code, parentId, actualParentLevel, fullName, shortName, uri, sort || 0, status || 1]);
    } else {
      // 创建行政区划
      await query(`
        INSERT INTO administrative_divisions (id, parent_id, name, short_name, single_name, level, sort_order, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [code, parentId, fullName, shortName, singleName, level, sort || 0, status || 1]);
    }

    res.json({
      code: 200,
      message: '创建成功'
    });
  } catch (error) {
    console.error('创建机构失败:', error);
    res.status(500).json({
      code: 500,
      message: '创建机构失败',
      error: error.message
    });
  }
};

/**
 * 更新机构
 */
const updateOrganization = async (req, res) => {
  try {
    const { id } = req.params;
    const { fullName, shortName, singleName, uri, sort, status } = req.body;

    // 先检查是行政区划还是考点
    const adminCheck = await query(
      'SELECT id FROM administrative_divisions WHERE id = ?',
      [id]
    );

    if (adminCheck.length > 0) {
      // 更新行政区划
      await query(`
        UPDATE administrative_divisions
        SET name = ?, short_name = ?, single_name = ?, sort_order = ?, status = ?
        WHERE id = ?
      `, [fullName, shortName, singleName, sort, status, id]);
    } else {
      // 更新考点
      await query(`
        UPDATE exam_sites
        SET name = ?, short_name = ?, uri = ?, sort_order = ?, status = ?
        WHERE id = ?
      `, [fullName, shortName, uri, sort, status, id]);
    }

    res.json({
      code: 200,
      message: '更新成功'
    });
  } catch (error) {
    console.error('更新机构失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新机构失败',
      error: error.message
    });
  }
};

/**
 * 删除机构
 */
const deleteOrganization = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查是否有子级机构
    const children = await query(
      'SELECT id FROM administrative_divisions WHERE parent_id = ? UNION SELECT id FROM exam_sites WHERE parent_id = ?',
      [id, id]
    );

    if (children.length > 0) {
      return res.status(400).json({
        code: 400,
        message: '存在下级机构，无法删除'
      });
    }

    // 先尝试删除行政区划
    const adminResult = await query(
      'DELETE FROM administrative_divisions WHERE id = ?',
      [id]
    );

    if (adminResult.affectedRows === 0) {
      // 删除考点
      await query('DELETE FROM exam_sites WHERE id = ?', [id]);
    }

    res.json({
      code: 200,
      message: '删除成功'
    });
  } catch (error) {
    console.error('删除机构失败:', error);
    res.status(500).json({
      code: 500,
      message: '删除机构失败',
      error: error.message
    });
  }
};

/**
 * 获取机构统计信息
 */
const getOrganizationStats = async (req, res) => {
  try {
    const provinceCount = await query(
      "SELECT COUNT(*) as count FROM administrative_divisions WHERE level = 'province'"
    );

    const cityCount = await query(
      "SELECT COUNT(*) as count FROM administrative_divisions WHERE level = 'city'"
    );

    const districtCount = await query(
      "SELECT COUNT(*) as count FROM administrative_divisions WHERE level = 'district'"
    );

    const examSiteCount = await query(
      "SELECT COUNT(*) as count FROM exam_sites"
    );

    res.json({
      code: 200,
      message: '获取成功',
      data: {
        provinceCount: provinceCount[0].count,
        cityCount: cityCount[0].count,
        districtCount: districtCount[0].count,
        examSiteCount: examSiteCount[0].count,
        totalCount: provinceCount[0].count + cityCount[0].count + districtCount[0].count + examSiteCount[0].count
      }
    });
  } catch (error) {
    console.error('获取统计信息失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取统计信息失败',
      error: error.message
    });
  }
};

module.exports = {
  getOrganizationTree,
  getOrganizationDetail,
  createOrganization,
  updateOrganization,
  deleteOrganization,
  getOrganizationStats
};
