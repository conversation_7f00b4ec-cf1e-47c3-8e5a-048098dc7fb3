// OSD规则管理控制器
const osdRulesService = require('../services/osdRulesService');

class OsdRulesController {
  
  // 获取OSD规则列表
  async getOsdRules(req, res) {
    try {
      const { page, size, keyword, status } = req.query;
      const result = await osdRulesService.getOsdRules({
        page: parseInt(page) || 1,
        size: parseInt(size) || 20,
        keyword,
        status: status !== undefined ? parseInt(status) : undefined
      });
      
      res.json({
        code: 200,
        message: '获取成功',
        data: result
      });
    } catch (error) {
      console.error('获取OSD规则列表失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取OSD规则列表失败',
        error: error.message
      });
    }
  }
  
  // 获取OSD规则详情
  async getOsdRuleById(req, res) {
    try {
      const { id } = req.params;
      const rule = await osdRulesService.getOsdRuleById(id);
      
      if (!rule) {
        return res.status(404).json({
          code: 404,
          message: 'OSD规则不存在'
        });
      }
      
      res.json({
        code: 200,
        message: '获取成功',
        data: rule
      });
    } catch (error) {
      console.error('获取OSD规则详情失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取OSD规则详情失败',
        error: error.message
      });
    }
  }
  
  // 创建OSD规则
  async createOsdRule(req, res) {
    try {
      const { name, type, format, description, preview, status } = req.body;
      
      // 验证必填字段
      if (!name || !format) {
        return res.status(400).json({
          code: 400,
          message: '规则名称和格式模板不能为空'
        });
      }
      
      // 验证名称唯一性
      const isNameUnique = await osdRulesService.validateRuleName(name);
      if (!isNameUnique) {
        return res.status(400).json({
          code: 400,
          message: '规则名称已存在'
        });
      }
      
      const ruleId = await osdRulesService.createOsdRule({
        name,
        type: type || 'osd',
        format,
        description,
        preview,
        status: status !== undefined ? status : 1,
        created_by: req.user?.username || 'system'
      });
      
      res.json({
        code: 200,
        message: '创建成功',
        data: { id: ruleId }
      });
    } catch (error) {
      console.error('创建OSD规则失败:', error);
      res.status(500).json({
        code: 500,
        message: '创建OSD规则失败',
        error: error.message
      });
    }
  }
  
  // 更新OSD规则
  async updateOsdRule(req, res) {
    try {
      const { id } = req.params;
      const { name, type, format, description, preview, status } = req.body;
      
      // 验证必填字段
      if (!name || !format) {
        return res.status(400).json({
          code: 400,
          message: '规则名称和格式模板不能为空'
        });
      }
      
      // 验证名称唯一性
      const isNameUnique = await osdRulesService.validateRuleName(name, id);
      if (!isNameUnique) {
        return res.status(400).json({
          code: 400,
          message: '规则名称已存在'
        });
      }
      
      const success = await osdRulesService.updateOsdRule(id, {
        name,
        type: type || 'osd',
        format,
        description,
        preview,
        status: status !== undefined ? status : 1,
        updated_by: req.user?.username || 'system'
      });
      
      if (!success) {
        return res.status(404).json({
          code: 404,
          message: 'OSD规则不存在'
        });
      }
      
      res.json({
        code: 200,
        message: '更新成功'
      });
    } catch (error) {
      console.error('更新OSD规则失败:', error);
      res.status(500).json({
        code: 500,
        message: '更新OSD规则失败',
        error: error.message
      });
    }
  }
  
  // 删除OSD规则
  async deleteOsdRule(req, res) {
    try {
      const { id } = req.params;
      
      const success = await osdRulesService.deleteOsdRule(id);
      if (!success) {
        return res.status(404).json({
          code: 404,
          message: 'OSD规则不存在'
        });
      }
      
      res.json({
        code: 200,
        message: '删除成功'
      });
    } catch (error) {
      console.error('删除OSD规则失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '删除OSD规则失败',
        error: error.message
      });
    }
  }
  
  // 批量删除OSD规则
  async batchDeleteOsdRules(req, res) {
    try {
      const { ids } = req.body;
      
      if (!ids || !Array.isArray(ids) || ids.length === 0) {
        return res.status(400).json({
          code: 400,
          message: '请选择要删除的规则'
        });
      }
      
      const deletedCount = await osdRulesService.batchDeleteOsdRules(ids);
      
      res.json({
        code: 200,
        message: `成功删除 ${deletedCount} 条规则`
      });
    } catch (error) {
      console.error('批量删除OSD规则失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '批量删除OSD规则失败',
        error: error.message
      });
    }
  }
  
  // 获取可用的OSD规则
  async getAvailableOsdRules(req, res) {
    try {
      const rules = await osdRulesService.getAvailableOsdRules();
      
      res.json({
        code: 200,
        message: '获取成功',
        data: rules
      });
    } catch (error) {
      console.error('获取可用OSD规则失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取可用OSD规则失败',
        error: error.message
      });
    }
  }
}

module.exports = new OsdRulesController();
