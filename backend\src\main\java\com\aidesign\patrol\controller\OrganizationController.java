package com.aidesign.patrol.controller;

import com.aidesign.patrol.dto.OrganizationNodeDto;
import com.aidesign.patrol.dto.OrganizationStatsDto;
import com.aidesign.patrol.dto.Result;
import com.aidesign.patrol.entity.AdministrativeDivision;
import com.aidesign.patrol.entity.ExamSite;
import com.aidesign.patrol.service.OrganizationService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 机构管理控制器
 */
@RestController
@RequestMapping("/api/organization")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class OrganizationController {

    private final OrganizationService organizationService;

    /**
     * 获取机构树数据
     */
    @GetMapping("/tree")
    public Result<List<OrganizationNodeDto>> getOrganizationTree(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String level,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) String parentId) {
        
        List<OrganizationNodeDto> tree = organizationService.getOrganizationTree(keyword, level, status, parentId);
        return Result.success(tree);
    }

    /**
     * 获取机构详情
     */
    @GetMapping("/{id}")
    public Result<OrganizationNodeDto> getOrganizationDetail(@PathVariable String id) {
        OrganizationNodeDto detail = organizationService.getOrganizationDetail(id);
        if (detail == null) {
            return Result.error("机构不存在");
        }
        return Result.success(detail);
    }

    /**
     * 创建机构
     */
    @PostMapping
    public Result<Void> createOrganization(@RequestBody Map<String, Object> request) {
        try {
            String level = (String) request.get("level");
            
            if ("examSite".equals(level)) {
                // 创建考点
                ExamSite examSite = new ExamSite();
                examSite.setId((String) request.get("code"));
                examSite.setDistrictId((String) request.get("parentId"));
                examSite.setName((String) request.get("fullName"));
                examSite.setShortName((String) request.get("shortName"));
                examSite.setUri((String) request.get("uri"));
                examSite.setSortOrder((Integer) request.get("sort"));
                examSite.setStatus((Integer) request.get("status"));
                
                organizationService.createExamSite(examSite);
            } else {
                // 创建行政区划
                AdministrativeDivision division = new AdministrativeDivision();
                division.setId((String) request.get("code"));
                division.setParentId((String) request.get("parentId"));
                division.setName((String) request.get("fullName"));
                division.setShortName((String) request.get("shortName"));
                division.setSingleName((String) request.get("singleName"));
                division.setLevel(level);
                division.setSortOrder((Integer) request.get("sort"));
                division.setStatus((Integer) request.get("status"));
                
                organizationService.createAdministrativeDivision(division);
            }
            
            return Result.success();
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 更新机构
     */
    @PutMapping("/{id}")
    public Result<Void> updateOrganization(@PathVariable String id, @RequestBody Map<String, Object> request) {
        try {
            // 先获取机构详情判断类型
            OrganizationNodeDto detail = organizationService.getOrganizationDetail(id);
            if (detail == null) {
                return Result.error("机构不存在");
            }
            
            if ("examSite".equals(detail.getLevel())) {
                // 更新考点
                ExamSite examSite = new ExamSite();
                examSite.setName((String) request.get("fullName"));
                examSite.setShortName((String) request.get("shortName"));
                examSite.setUri((String) request.get("uri"));
                examSite.setSortOrder((Integer) request.get("sort"));
                examSite.setStatus((Integer) request.get("status"));
                
                organizationService.updateExamSite(id, examSite);
            } else {
                // 更新行政区划
                AdministrativeDivision division = new AdministrativeDivision();
                division.setName((String) request.get("fullName"));
                division.setShortName((String) request.get("shortName"));
                division.setSingleName((String) request.get("singleName"));
                division.setSortOrder((Integer) request.get("sort"));
                division.setStatus((Integer) request.get("status"));
                
                organizationService.updateAdministrativeDivision(id, division);
            }
            
            return Result.success();
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 删除机构
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteOrganization(@PathVariable String id) {
        try {
            organizationService.deleteOrganization(id);
            return Result.success();
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取机构统计信息
     */
    @GetMapping("/stats")
    public Result<OrganizationStatsDto> getOrganizationStats() {
        OrganizationStatsDto stats = organizationService.getOrganizationStats();
        return Result.success(stats);
    }

    /**
     * 验证机构码是否唯一
     */
    @PostMapping("/validate/code")
    public Result<Boolean> validateOrganizationCode(@RequestBody Map<String, String> request) {
        String code = request.get("code");
        String excludeId = request.get("excludeId");
        
        boolean isValid = organizationService.validateOrganizationCode(code, excludeId);
        return Result.success(isValid);
    }
}
