package com.aidesign.patrol.dto;

import lombok.Data;

/**
 * 登录响应DTO
 * 
 * <AUTHOR>
 * @version 3.0.0
 */
@Data
public class LoginResponse {

    /**
     * 访问令牌
     */
    private String token;

    /**
     * 令牌类型
     */
    private String tokenType = "Bearer";

    /**
     * 过期时间（毫秒）
     */
    private Long expires;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    /**
     * 用户信息
     */
    private UserInfo userInfo;

    @Data
    public static class UserInfo {
        private Long id;
        private String username;
        private String realName;
        private String email;
        private String phone;
        private String avatar;
        private Integer status;
        private Integer userType;
        private Long deptId;
        private String[] roles;
        private String[] permissions;
    }
}
