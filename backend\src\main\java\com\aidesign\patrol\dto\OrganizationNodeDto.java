package com.aidesign.patrol.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 机构节点DTO
 */
@Data
public class OrganizationNodeDto {

    /**
     * 机构ID
     */
    private String id;

    /**
     * 上级机构ID
     */
    private String parentId;

    /**
     * 机构全称
     */
    private String fullName;

    /**
     * 机构简称
     */
    private String shortName;

    /**
     * 单字简称（省份专用）
     */
    private String singleName;

    /**
     * URI（考点专用）
     */
    private String uri;

    /**
     * 机构层级：province-省份，city-地市，district-区县，examSite-考点
     */
    private String level;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态：1-正常，0-禁用
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 子级机构数量
     */
    private Integer childrenCount;

    /**
     * 考点数量
     */
    private Integer examSiteCount;

    /**
     * 子级机构列表
     */
    private List<OrganizationNodeDto> children;

    /**
     * 地址（考点专用）
     */
    private String address;

    /**
     * 联系人（考点专用）
     */
    private String contactPerson;

    /**
     * 联系电话（考点专用）
     */
    private String contactPhone;

    /**
     * 考场容量（考点专用）
     */
    private Integer capacity;
}
