package com.aidesign.patrol.dto;

import lombok.Data;

/**
 * 机构统计信息DTO
 */
@Data
public class OrganizationStatsDto {

    /**
     * 省份数量
     */
    private Integer provinceCount;

    /**
     * 地市数量
     */
    private Integer cityCount;

    /**
     * 区县数量
     */
    private Integer districtCount;

    /**
     * 考点数量
     */
    private Integer examSiteCount;

    /**
     * 总数量
     */
    private Integer totalCount;

    /**
     * 计算总数量
     */
    public Integer getTotalCount() {
        return (provinceCount != null ? provinceCount : 0) +
               (cityCount != null ? cityCount : 0) +
               (districtCount != null ? districtCount : 0) +
               (examSiteCount != null ? examSiteCount : 0);
    }
}
