package com.aidesign.patrol.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 行政区划实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("administrative_divisions")
public class AdministrativeDivision extends BaseEntity {

    /**
     * 行政区划代码
     */
    @TableId(type = IdType.INPUT)
    private String id;

    /**
     * 上级行政区划代码
     */
    private String parentId;

    /**
     * 行政区划名称
     */
    private String name;

    /**
     * 简称
     */
    private String shortName;

    /**
     * 单字简称（省份专用）
     */
    private String singleName;

    /**
     * 行政级别：province-省份，city-地市，district-区县
     */
    private String level;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 状态：1-正常，0-禁用
     */
    private Integer status;

    /**
     * 子级机构列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<Object> children;

    /**
     * 子级机构数量（非数据库字段）
     */
    @TableField(exist = false)
    private Integer childrenCount;

    /**
     * 考点数量（非数据库字段）
     */
    @TableField(exist = false)
    private Integer examSiteCount;

    /**
     * 全称（用于前端显示）
     */
    @TableField(exist = false)
    public String getFullName() {
        return this.name;
    }

    /**
     * 排序值（用于前端显示）
     */
    @TableField(exist = false)
    public Integer getSort() {
        return this.sortOrder;
    }
}
