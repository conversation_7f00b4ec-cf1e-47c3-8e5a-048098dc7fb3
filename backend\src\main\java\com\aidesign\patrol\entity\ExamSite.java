package com.aidesign.patrol.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 考点实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("exam_sites")
public class ExamSite extends BaseEntity {

    /**
     * 考点代码
     */
    @TableId(type = IdType.INPUT)
    private String id;

    /**
     * 上级机构代码（可以是省、地市或区县）
     */
    private String parentId;

    /**
     * 上级机构级别：province-省份，city-地市，district-区县
     */
    private String parentLevel;

    /**
     * 考点名称
     */
    private String name;

    /**
     * 考点简称
     */
    private String shortName;

    /**
     * 考点URI地址
     */
    private String uri;

    /**
     * 考点地址
     */
    private String address;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 考场容量
     */
    private Integer capacity;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 状态：1-正常，0-禁用
     */
    private Integer status;

    /**
     * 子级机构列表（非数据库字段，考点没有子级）
     */
    @TableField(exist = false)
    private List<Object> children;

    /**
     * 子级机构数量（非数据库字段，考点固定为0）
     */
    @TableField(exist = false)
    private Integer childrenCount = 0;

    /**
     * 考点数量（非数据库字段，考点固定为1）
     */
    @TableField(exist = false)
    private Integer examSiteCount = 1;

    /**
     * 层级（非数据库字段，考点固定为examSite）
     */
    @TableField(exist = false)
    private String level = "examSite";

    /**
     * 父级ID（用于前端显示）
     */
    @TableField(exist = false)
    public String getParentId() {
        return this.parentId;
    }

    /**
     * 全称（用于前端显示）
     */
    @TableField(exist = false)
    public String getFullName() {
        return this.name;
    }

    /**
     * 排序值（用于前端显示）
     */
    @TableField(exist = false)
    public Integer getSort() {
        return this.sortOrder;
    }
}
