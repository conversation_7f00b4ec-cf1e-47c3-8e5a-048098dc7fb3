package com.aidesign.patrol.mapper;

import com.aidesign.patrol.entity.AdministrativeDivision;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 行政区划Mapper接口
 */
@Mapper
public interface AdministrativeDivisionMapper extends BaseMapper<AdministrativeDivision> {

    /**
     * 获取机构树数据（包含统计信息）
     */
    @Select({
        "<script>",
        "SELECT ",
        "  ad.id,",
        "  ad.parent_id,",
        "  ad.name,",
        "  ad.short_name,",
        "  ad.single_name,",
        "  ad.level,",
        "  ad.sort_order,",
        "  ad.status,",
        "  ad.created_at,",
        "  ad.updated_at,",
        "  COUNT(DISTINCT child.id) as children_count,",
        "  CASE ",
        "    WHEN ad.level = 'district' THEN (",
        "      SELECT COUNT(*) FROM exam_sites es WHERE es.district_id = ad.id",
        "    )",
        "    WHEN ad.level = 'city' THEN (",
        "      SELECT COUNT(*) FROM exam_sites es ",
        "      WHERE es.district_id IN (",
        "        SELECT id FROM administrative_divisions WHERE parent_id = ad.id",
        "      )",
        "    )",
        "    WHEN ad.level = 'province' THEN (",
        "      SELECT COUNT(*) FROM exam_sites es ",
        "      WHERE es.district_id IN (",
        "        SELECT d.id FROM administrative_divisions d ",
        "        JOIN administrative_divisions c ON d.parent_id = c.id ",
        "        WHERE c.parent_id = ad.id",
        "      )",
        "    )",
        "    ELSE 0",
        "  END as exam_site_count",
        "FROM administrative_divisions ad",
        "LEFT JOIN administrative_divisions child ON child.parent_id = ad.id",
        "<where>",
        "  <if test='keyword != null and keyword != \"\"'>",
        "    AND (ad.name LIKE CONCAT('%', #{keyword}, '%') ",
        "         OR ad.short_name LIKE CONCAT('%', #{keyword}, '%') ",
        "         OR ad.id LIKE CONCAT('%', #{keyword}, '%'))",
        "  </if>",
        "  <if test='level != null and level != \"\"'>",
        "    AND ad.level = #{level}",
        "  </if>",
        "  <if test='status != null'>",
        "    AND ad.status = #{status}",
        "  </if>",
        "  <if test='parentId != null and parentId != \"\"'>",
        "    AND ad.parent_id = #{parentId}",
        "  </if>",
        "</where>",
        "GROUP BY ad.id, ad.parent_id, ad.name, ad.short_name, ad.single_name, ad.level, ad.sort_order, ad.status, ad.created_at, ad.updated_at",
        "ORDER BY ad.sort_order, ad.id",
        "</script>"
    })
    List<Map<String, Object>> getOrganizationTreeWithStats(@Param("keyword") String keyword,
                                                           @Param("level") String level,
                                                           @Param("status") Integer status,
                                                           @Param("parentId") String parentId);

    /**
     * 获取子级机构数量
     */
    @Select("SELECT COUNT(*) FROM administrative_divisions WHERE parent_id = #{parentId}")
    Integer getChildrenCount(@Param("parentId") String parentId);

    /**
     * 获取区县下的考点数量
     */
    @Select("SELECT COUNT(*) FROM exam_sites WHERE district_id = #{districtId}")
    Integer getExamSiteCountByDistrict(@Param("districtId") String districtId);

    /**
     * 获取地市下的考点数量
     */
    @Select({
        "SELECT COUNT(*) FROM exam_sites ",
        "WHERE district_id IN (",
        "  SELECT id FROM administrative_divisions WHERE parent_id = #{cityId}",
        ")"
    })
    Integer getExamSiteCountByCity(@Param("cityId") String cityId);

    /**
     * 获取省份下的考点数量
     */
    @Select({
        "SELECT COUNT(*) FROM exam_sites ",
        "WHERE district_id IN (",
        "  SELECT d.id FROM administrative_divisions d ",
        "  JOIN administrative_divisions c ON d.parent_id = c.id ",
        "  WHERE c.parent_id = #{provinceId}",
        ")"
    })
    Integer getExamSiteCountByProvince(@Param("provinceId") String provinceId);

    /**
     * 检查是否存在子级机构
     */
    @Select({
        "SELECT COUNT(*) FROM (",
        "  SELECT id FROM administrative_divisions WHERE parent_id = #{id}",
        "  UNION",
        "  SELECT id FROM exam_sites WHERE district_id = #{id}",
        ") as children"
    })
    Integer hasChildren(@Param("id") String id);

    /**
     * 获取统计信息
     */
    @Select({
        "SELECT ",
        "  SUM(CASE WHEN level = 'province' THEN 1 ELSE 0 END) as province_count,",
        "  SUM(CASE WHEN level = 'city' THEN 1 ELSE 0 END) as city_count,",
        "  SUM(CASE WHEN level = 'district' THEN 1 ELSE 0 END) as district_count",
        "FROM administrative_divisions"
    })
    Map<String, Object> getAdminDivisionStats();
}
