package com.aidesign.patrol.mapper;

import com.aidesign.patrol.entity.ExamSite;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 考点Mapper接口
 */
@Mapper
public interface ExamSiteMapper extends BaseMapper<ExamSite> {

    /**
     * 获取考点数据（用于机构树）
     */
    @Select({
        "<script>",
        "SELECT ",
        "  id,",
        "  parent_id,",
        "  parent_level,",
        "  name,",
        "  short_name,",
        "  uri,",
        "  'examSite' as level,",
        "  sort_order,",
        "  status,",
        "  created_at,",
        "  updated_at,",
        "  0 as children_count,",
        "  1 as exam_site_count",
        "FROM exam_sites",
        "<where>",
        "  <if test='keyword != null and keyword != \"\"'>",
        "    AND (name LIKE CONCAT('%', #{keyword}, '%') ",
        "         OR short_name LIKE CONCAT('%', #{keyword}, '%') ",
        "         OR id LIKE CONCAT('%', #{keyword}, '%'))",
        "  </if>",
        "  <if test='status != null'>",
        "    AND status = #{status}",
        "  </if>",
        "  <if test='parentId != null and parentId != \"\"'>",
        "    AND parent_id = #{parentId}",
        "  </if>",
        "  <if test='parentLevel != null and parentLevel != \"\"'>",
        "    AND parent_level = #{parentLevel}",
        "  </if>",
        "</where>",
        "ORDER BY sort_order, id",
        "</script>"
    })
    List<Map<String, Object>> getExamSiteTreeData(@Param("keyword") String keyword,
                                                  @Param("status") Integer status,
                                                  @Param("parentId") String parentId,
                                                  @Param("parentLevel") String parentLevel);

    /**
     * 获取考点总数
     */
    @Select("SELECT COUNT(*) FROM exam_sites")
    Integer getTotalCount();

    /**
     * 根据上级机构ID获取考点列表
     */
    @Select("SELECT * FROM exam_sites WHERE parent_id = #{parentId} ORDER BY sort_order, id")
    List<ExamSite> getByParentId(@Param("parentId") String parentId);

    /**
     * 根据区县ID获取考点列表（兼容旧方法）
     */
    @Select("SELECT * FROM exam_sites WHERE parent_id = #{districtId} AND parent_level = 'district' ORDER BY sort_order, id")
    List<ExamSite> getByDistrictId(@Param("districtId") String districtId);

    /**
     * 根据地市ID获取考点列表（包括直属和下级区县的考点）
     */
    @Select({
        "SELECT es.* FROM exam_sites es",
        "WHERE (es.parent_id = #{cityId} AND es.parent_level = 'city')",
        "   OR (es.parent_id IN (",
        "       SELECT id FROM administrative_divisions",
        "       WHERE parent_id = #{cityId} AND level = 'district'",
        "   ) AND es.parent_level = 'district')",
        "ORDER BY es.sort_order, es.id"
    })
    List<ExamSite> getByCityId(@Param("cityId") String cityId);

    /**
     * 根据省份ID获取考点列表（包括直属、地市直属和区县考点）
     */
    @Select({
        "SELECT es.* FROM exam_sites es",
        "WHERE (es.parent_id = #{provinceId} AND es.parent_level = 'province')",
        "   OR (es.parent_id IN (",
        "       SELECT id FROM administrative_divisions",
        "       WHERE parent_id = #{provinceId} AND level = 'city'",
        "   ) AND es.parent_level = 'city')",
        "   OR (es.parent_id IN (",
        "       SELECT d.id FROM administrative_divisions d",
        "       JOIN administrative_divisions c ON d.parent_id = c.id",
        "       WHERE c.parent_id = #{provinceId} AND d.level = 'district'",
        "   ) AND es.parent_level = 'district')",
        "ORDER BY es.sort_order, es.id"
    })
    List<ExamSite> getByProvinceId(@Param("provinceId") String provinceId);

    /**
     * 检查考点代码是否已存在
     */
    @Select("SELECT COUNT(*) FROM exam_sites WHERE id = #{id}")
    Integer existsById(@Param("id") String id);

    /**
     * 检查考点代码是否已存在（排除指定ID）
     */
    @Select("SELECT COUNT(*) FROM exam_sites WHERE id = #{id} AND id != #{excludeId}")
    Integer existsByIdExclude(@Param("id") String id, @Param("excludeId") String excludeId);
}
