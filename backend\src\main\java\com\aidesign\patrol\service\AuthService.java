package com.aidesign.patrol.service;

import com.aidesign.patrol.dto.LoginRequest;
import com.aidesign.patrol.dto.LoginResponse;
import com.aidesign.patrol.dto.CaptchaResponse;

/**
 * 认证服务接口
 * 
 * <AUTHOR>
 * @version 3.0.0
 */
public interface AuthService {

    /**
     * 用户登录
     * 
     * @param request 登录请求
     * @param clientIp 客户端IP
     * @return 登录响应
     */
    LoginResponse login(LoginRequest request, String clientIp);

    /**
     * 用户登出
     * 
     * @param token 令牌
     * @param username 用户名
     */
    void logout(String token, String username);

    /**
     * 刷新令牌
     * 
     * @param token 当前令牌
     * @return 新的登录响应
     */
    LoginResponse refreshToken(String token);

    /**
     * 修改密码
     * 
     * @param username 用户名
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     */
    void changePassword(String username, String oldPassword, String newPassword);

    /**
     * 生成验证码
     * 
     * @return 验证码响应
     */
    CaptchaResponse generateCaptcha();

    /**
     * 验证验证码
     * 
     * @param uuid 验证码UUID
     * @param captcha 验证码
     * @return 是否验证成功
     */
    boolean verifyCaptcha(String uuid, String captcha);
}
