package com.aidesign.patrol.service;

import com.aidesign.patrol.dto.OrganizationNodeDto;
import com.aidesign.patrol.dto.OrganizationStatsDto;
import com.aidesign.patrol.entity.AdministrativeDivision;
import com.aidesign.patrol.entity.ExamSite;

import java.util.List;
import java.util.Map;

/**
 * 机构管理服务接口
 */
public interface OrganizationService {

    /**
     * 获取机构树数据
     */
    List<OrganizationNodeDto> getOrganizationTree(String keyword, String level, Integer status, String parentId);

    /**
     * 获取机构详情
     */
    OrganizationNodeDto getOrganizationDetail(String id);

    /**
     * 创建行政区划
     */
    void createAdministrativeDivision(AdministrativeDivision division);

    /**
     * 创建考点
     */
    void createExamSite(ExamSite examSite);

    /**
     * 更新行政区划
     */
    void updateAdministrativeDivision(String id, AdministrativeDivision division);

    /**
     * 更新考点
     */
    void updateExamSite(String id, ExamSite examSite);

    /**
     * 删除机构
     */
    void deleteOrganization(String id);

    /**
     * 获取机构统计信息
     */
    OrganizationStatsDto getOrganizationStats();

    /**
     * 验证机构码是否唯一
     */
    boolean validateOrganizationCode(String code, String excludeId);

    /**
     * 检查是否存在子级机构
     */
    boolean hasChildren(String id);

    /**
     * 构建树形结构
     */
    List<OrganizationNodeDto> buildTree(List<OrganizationNodeDto> nodes, String parentId);
}
