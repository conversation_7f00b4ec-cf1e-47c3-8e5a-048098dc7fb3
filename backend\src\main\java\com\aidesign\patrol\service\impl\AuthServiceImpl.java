package com.aidesign.patrol.service.impl;

import com.aidesign.patrol.dto.LoginRequest;
import com.aidesign.patrol.dto.LoginResponse;
import com.aidesign.patrol.dto.CaptchaResponse;
import com.aidesign.patrol.entity.User;
import com.aidesign.patrol.service.AuthService;
import com.aidesign.patrol.service.UserService;
import com.aidesign.patrol.utils.JwtUtils;
import com.aidesign.patrol.utils.CaptchaUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 认证服务实现类
 * 
 * <AUTHOR>
 * @version 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

    private final UserService userService;
    private final JwtUtils jwtUtils;
    private final PasswordEncoder passwordEncoder;
    private final RedisTemplate<String, Object> redisTemplate;
    private final CaptchaUtils captchaUtils;

    private static final String CAPTCHA_KEY_PREFIX = "captcha:";
    private static final String TOKEN_BLACKLIST_PREFIX = "token:blacklist:";
    private static final int CAPTCHA_EXPIRE_MINUTES = 5;

    @Override
    public LoginResponse login(LoginRequest request, String clientIp) {
        try {
            // 验证验证码
            if (!verifyCaptcha(request.getUuid(), request.getCaptcha())) {
                throw new RuntimeException("验证码错误或已过期");
            }

            // 查找用户
            User user = userService.getUserByUsername(request.getUsername());
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }

            // 检查用户状态
            if (user.getStatus() == 0) {
                throw new RuntimeException("用户已被禁用");
            }

            // 验证密码
            if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
                throw new RuntimeException("密码错误");
            }

            // 生成JWT令牌
            Map<String, Object> claims = new HashMap<>();
            claims.put("userId", user.getId());
            claims.put("username", user.getUsername());
            claims.put("userType", user.getUserType());
            
            String token = jwtUtils.generateToken(user.getUsername(), claims);
            
            // 构建响应
            LoginResponse response = new LoginResponse();
            response.setToken(token);
            response.setExpires(System.currentTimeMillis() + 24 * 60 * 60 * 1000); // 24小时
            
            // 构建用户信息
            LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
            userInfo.setId(user.getId());
            userInfo.setUsername(user.getUsername());
            userInfo.setRealName(user.getRealName());
            userInfo.setEmail(user.getEmail());
            userInfo.setPhone(user.getPhone());
            userInfo.setAvatar(user.getAvatar());
            userInfo.setStatus(user.getStatus());
            userInfo.setUserType(user.getUserType());
            userInfo.setDeptId(user.getDeptId());
            
            // 获取用户角色和权限
            String[] roles = userService.getUserRoles(user.getId());
            String[] permissions = userService.getUserPermissions(user.getId());
            userInfo.setRoles(roles);
            userInfo.setPermissions(permissions);
            
            response.setUserInfo(userInfo);

            // 记录登录日志
            userService.recordLoginLog(user.getUsername(), clientIp, true, "登录成功");

            // 更新最后登录时间
            userService.updateLastLoginTime(user.getId(), clientIp);

            log.info("用户 {} 登录成功，IP: {}", user.getUsername(), clientIp);
            
            return response;
            
        } catch (Exception e) {
            // 记录登录失败日志
            userService.recordLoginLog(request.getUsername(), clientIp, false, e.getMessage());
            log.error("用户 {} 登录失败: {}", request.getUsername(), e.getMessage());
            throw e;
        }
    }

    @Override
    public void logout(String token, String username) {
        try {
            // 将令牌加入黑名单
            Long remainingTime = jwtUtils.getTokenRemainingTime(token);
            if (remainingTime > 0) {
                redisTemplate.opsForValue().set(
                    TOKEN_BLACKLIST_PREFIX + token, 
                    username, 
                    remainingTime, 
                    TimeUnit.MILLISECONDS
                );
            }
            
            log.info("用户 {} 登出成功", username);
            
        } catch (Exception e) {
            log.error("用户 {} 登出失败: {}", username, e.getMessage());
            throw new RuntimeException("登出失败");
        }
    }

    @Override
    public LoginResponse refreshToken(String token) {
        try {
            String username = jwtUtils.getUsernameFromToken(token);
            if (username == null) {
                throw new RuntimeException("无效的令牌");
            }

            // 检查令牌是否在黑名单中
            if (redisTemplate.hasKey(TOKEN_BLACKLIST_PREFIX + token)) {
                throw new RuntimeException("令牌已失效");
            }

            // 生成新令牌
            String newToken = jwtUtils.refreshToken(token);
            if (newToken == null) {
                throw new RuntimeException("令牌刷新失败");
            }

            // 将旧令牌加入黑名单
            Long remainingTime = jwtUtils.getTokenRemainingTime(token);
            if (remainingTime > 0) {
                redisTemplate.opsForValue().set(
                    TOKEN_BLACKLIST_PREFIX + token, 
                    username, 
                    remainingTime, 
                    TimeUnit.MILLISECONDS
                );
            }

            LoginResponse response = new LoginResponse();
            response.setToken(newToken);
            response.setExpires(System.currentTimeMillis() + 24 * 60 * 60 * 1000);

            return response;
            
        } catch (Exception e) {
            log.error("令牌刷新失败: {}", e.getMessage());
            throw e;
        }
    }

    @Override
    public void changePassword(String username, String oldPassword, String newPassword) {
        try {
            User user = userService.getUserByUsername(username);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }

            // 验证旧密码
            if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
                throw new RuntimeException("原密码错误");
            }

            // 更新密码
            String encodedNewPassword = passwordEncoder.encode(newPassword);
            userService.updatePassword(user.getId(), encodedNewPassword);

            log.info("用户 {} 修改密码成功", username);
            
        } catch (Exception e) {
            log.error("用户 {} 修改密码失败: {}", username, e.getMessage());
            throw e;
        }
    }

    @Override
    public CaptchaResponse generateCaptcha() {
        try {
            String uuid = UUID.randomUUID().toString();
            String code = captchaUtils.generateRandomCode(4);
            String imageBase64 = captchaUtils.generateCaptchaImage(code);

            // 将验证码存储到Redis
            redisTemplate.opsForValue().set(
                CAPTCHA_KEY_PREFIX + uuid, 
                code, 
                CAPTCHA_EXPIRE_MINUTES, 
                TimeUnit.MINUTES
            );

            return new CaptchaResponse(uuid, imageBase64);
            
        } catch (Exception e) {
            log.error("生成验证码失败: {}", e.getMessage());
            throw new RuntimeException("生成验证码失败");
        }
    }

    @Override
    public boolean verifyCaptcha(String uuid, String captcha) {
        try {
            if (uuid == null || captcha == null) {
                return false;
            }

            String key = CAPTCHA_KEY_PREFIX + uuid;
            String storedCode = (String) redisTemplate.opsForValue().get(key);
            
            if (storedCode == null) {
                return false;
            }

            // 验证成功后删除验证码
            boolean isValid = storedCode.equalsIgnoreCase(captcha);
            if (isValid) {
                redisTemplate.delete(key);
            }

            return isValid;
            
        } catch (Exception e) {
            log.error("验证码验证失败: {}", e.getMessage());
            return false;
        }
    }
}
