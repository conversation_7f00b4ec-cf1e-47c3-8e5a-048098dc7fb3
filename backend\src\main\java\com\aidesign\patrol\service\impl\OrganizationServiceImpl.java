package com.aidesign.patrol.service.impl;

import com.aidesign.patrol.dto.OrganizationNodeDto;
import com.aidesign.patrol.dto.OrganizationStatsDto;
import com.aidesign.patrol.entity.AdministrativeDivision;
import com.aidesign.patrol.entity.ExamSite;
import com.aidesign.patrol.mapper.AdministrativeDivisionMapper;
import com.aidesign.patrol.mapper.ExamSiteMapper;
import com.aidesign.patrol.service.OrganizationService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 机构管理服务实现类
 */
@Service
@RequiredArgsConstructor
public class OrganizationServiceImpl implements OrganizationService {

    private final AdministrativeDivisionMapper adminDivisionMapper;
    private final ExamSiteMapper examSiteMapper;

    @Override
    public List<OrganizationNodeDto> getOrganizationTree(String keyword, String level, Integer status, String parentId) {
        List<OrganizationNodeDto> allNodes = new ArrayList<>();

        // 获取行政区划数据
        List<Map<String, Object>> adminData = adminDivisionMapper.getOrganizationTreeWithStats(keyword, level, status, parentId);
        for (Map<String, Object> data : adminData) {
            OrganizationNodeDto node = new OrganizationNodeDto();
            node.setId((String) data.get("id"));
            node.setParentId((String) data.get("parent_id"));
            node.setFullName((String) data.get("name"));
            node.setShortName((String) data.get("short_name"));
            node.setSingleName((String) data.get("single_name"));
            node.setLevel((String) data.get("level"));
            node.setSort((Integer) data.get("sort_order"));
            node.setStatus((Integer) data.get("status"));
            node.setCreateTime((LocalDateTime) data.get("created_at"));
            node.setUpdateTime((LocalDateTime) data.get("updated_at"));
            node.setChildrenCount(((Number) data.get("children_count")).intValue());
            node.setExamSiteCount(((Number) data.get("exam_site_count")).intValue());
            allNodes.add(node);
        }

        // 获取考点数据（如果不是只查询特定层级的行政区划）
        if (level == null || "examSite".equals(level)) {
            List<Map<String, Object>> examData = examSiteMapper.getExamSiteTreeData(keyword, status, parentId, null);
            for (Map<String, Object> data : examData) {
                OrganizationNodeDto node = new OrganizationNodeDto();
                node.setId((String) data.get("id"));
                node.setParentId((String) data.get("parent_id"));
                node.setFullName((String) data.get("name"));
                node.setShortName((String) data.get("short_name"));
                node.setUri((String) data.get("uri"));
                node.setLevel((String) data.get("level"));
                node.setSort((Integer) data.get("sort_order"));
                node.setStatus((Integer) data.get("status"));
                node.setCreateTime((LocalDateTime) data.get("created_at"));
                node.setUpdateTime((LocalDateTime) data.get("updated_at"));
                node.setChildrenCount(((Number) data.get("children_count")).intValue());
                node.setExamSiteCount(((Number) data.get("exam_site_count")).intValue());
                allNodes.add(node);
            }
        }

        // 构建树形结构
        return buildTree(allNodes, null);
    }

    @Override
    public OrganizationNodeDto getOrganizationDetail(String id) {
        // 先查询行政区划
        AdministrativeDivision adminDivision = adminDivisionMapper.selectById(id);
        if (adminDivision != null) {
            OrganizationNodeDto node = new OrganizationNodeDto();
            BeanUtils.copyProperties(adminDivision, node);
            node.setFullName(adminDivision.getName());
            node.setSort(adminDivision.getSortOrder());
            node.setCreateTime(adminDivision.getCreatedAt());
            node.setUpdateTime(adminDivision.getUpdatedAt());
            
            // 设置统计信息
            node.setChildrenCount(adminDivisionMapper.getChildrenCount(id));
            
            switch (adminDivision.getLevel()) {
                case "district":
                    node.setExamSiteCount(adminDivisionMapper.getExamSiteCountByDistrict(id));
                    break;
                case "city":
                    node.setExamSiteCount(adminDivisionMapper.getExamSiteCountByCity(id));
                    break;
                case "province":
                    node.setExamSiteCount(adminDivisionMapper.getExamSiteCountByProvince(id));
                    break;
                default:
                    node.setExamSiteCount(0);
            }
            
            return node;
        }

        // 查询考点
        ExamSite examSite = examSiteMapper.selectById(id);
        if (examSite != null) {
            OrganizationNodeDto node = new OrganizationNodeDto();
            BeanUtils.copyProperties(examSite, node);
            node.setParentId(examSite.getDistrictId());
            node.setFullName(examSite.getName());
            node.setSort(examSite.getSortOrder());
            node.setCreateTime(examSite.getCreatedAt());
            node.setUpdateTime(examSite.getUpdatedAt());
            node.setChildrenCount(0);
            node.setExamSiteCount(1);
            return node;
        }

        return null;
    }

    @Override
    @Transactional
    public void createAdministrativeDivision(AdministrativeDivision division) {
        // 检查机构码是否已存在
        if (!validateOrganizationCode(division.getId(), null)) {
            throw new RuntimeException("机构码已存在");
        }
        
        division.setCreatedAt(LocalDateTime.now());
        division.setUpdatedAt(LocalDateTime.now());
        adminDivisionMapper.insert(division);
    }

    @Override
    @Transactional
    public void createExamSite(ExamSite examSite) {
        // 检查机构码是否已存在
        if (!validateOrganizationCode(examSite.getId(), null)) {
            throw new RuntimeException("机构码已存在");
        }

        // 如果没有设置parentLevel，根据parentId自动确定
        if (examSite.getParentLevel() == null || examSite.getParentLevel().isEmpty()) {
            AdministrativeDivision parent = adminDivisionMapper.selectById(examSite.getParentId());
            if (parent != null) {
                examSite.setParentLevel(parent.getLevel());
            } else {
                throw new RuntimeException("上级机构不存在");
            }
        }

        examSite.setCreatedAt(LocalDateTime.now());
        examSite.setUpdatedAt(LocalDateTime.now());
        examSiteMapper.insert(examSite);
    }

    @Override
    @Transactional
    public void updateAdministrativeDivision(String id, AdministrativeDivision division) {
        AdministrativeDivision existing = adminDivisionMapper.selectById(id);
        if (existing == null) {
            throw new RuntimeException("机构不存在");
        }
        
        existing.setName(division.getName());
        existing.setShortName(division.getShortName());
        existing.setSingleName(division.getSingleName());
        existing.setSortOrder(division.getSortOrder());
        existing.setStatus(division.getStatus());
        existing.setUpdatedAt(LocalDateTime.now());
        
        adminDivisionMapper.updateById(existing);
    }

    @Override
    @Transactional
    public void updateExamSite(String id, ExamSite examSite) {
        ExamSite existing = examSiteMapper.selectById(id);
        if (existing == null) {
            throw new RuntimeException("考点不存在");
        }
        
        existing.setName(examSite.getName());
        existing.setShortName(examSite.getShortName());
        existing.setUri(examSite.getUri());
        existing.setSortOrder(examSite.getSortOrder());
        existing.setStatus(examSite.getStatus());
        existing.setUpdatedAt(LocalDateTime.now());
        
        examSiteMapper.updateById(existing);
    }

    @Override
    @Transactional
    public void deleteOrganization(String id) {
        // 检查是否有子级机构
        if (hasChildren(id)) {
            throw new RuntimeException("存在下级机构，无法删除");
        }
        
        // 先尝试删除行政区划
        int adminDeleted = adminDivisionMapper.deleteById(id);
        if (adminDeleted == 0) {
            // 删除考点
            int examDeleted = examSiteMapper.deleteById(id);
            if (examDeleted == 0) {
                throw new RuntimeException("机构不存在");
            }
        }
    }

    @Override
    public OrganizationStatsDto getOrganizationStats() {
        OrganizationStatsDto stats = new OrganizationStatsDto();
        
        // 获取行政区划统计
        Map<String, Object> adminStats = adminDivisionMapper.getAdminDivisionStats();
        stats.setProvinceCount(((Number) adminStats.get("province_count")).intValue());
        stats.setCityCount(((Number) adminStats.get("city_count")).intValue());
        stats.setDistrictCount(((Number) adminStats.get("district_count")).intValue());
        
        // 获取考点统计
        stats.setExamSiteCount(examSiteMapper.getTotalCount());
        
        return stats;
    }

    @Override
    public boolean validateOrganizationCode(String code, String excludeId) {
        // 检查行政区划表
        QueryWrapper<AdministrativeDivision> adminQuery = new QueryWrapper<>();
        adminQuery.eq("id", code);
        if (StringUtils.hasText(excludeId)) {
            adminQuery.ne("id", excludeId);
        }
        long adminCount = adminDivisionMapper.selectCount(adminQuery);
        
        // 检查考点表
        QueryWrapper<ExamSite> examQuery = new QueryWrapper<>();
        examQuery.eq("id", code);
        if (StringUtils.hasText(excludeId)) {
            examQuery.ne("id", excludeId);
        }
        long examCount = examSiteMapper.selectCount(examQuery);
        
        return adminCount == 0 && examCount == 0;
    }

    @Override
    public boolean hasChildren(String id) {
        Integer childrenCount = adminDivisionMapper.hasChildren(id);
        return childrenCount != null && childrenCount > 0;
    }

    @Override
    public List<OrganizationNodeDto> buildTree(List<OrganizationNodeDto> nodes, String parentId) {
        return nodes.stream()
                .filter(node -> {
                    if (parentId == null) {
                        return node.getParentId() == null;
                    }
                    return parentId.equals(node.getParentId());
                })
                .peek(node -> {
                    List<OrganizationNodeDto> children = buildTree(nodes, node.getId());
                    node.setChildren(children);
                })
                .collect(Collectors.toList());
    }
}
