package com.aidesign.patrol.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.Random;

/**
 * 验证码工具类
 * 
 * <AUTHOR>
 * @version 3.0.0
 */
@Slf4j
@Component
public class CaptchaUtils {

    private static final int WIDTH = 120;
    private static final int HEIGHT = 40;
    private static final int FONT_SIZE = 20;
    private static final String CHARACTERS = "ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789";
    
    private final Random random = new Random();

    /**
     * 生成随机验证码
     */
    public String generateRandomCode(int length) {
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < length; i++) {
            code.append(CHARACTERS.charAt(random.nextInt(CHARACTERS.length())));
        }
        return code.toString();
    }

    /**
     * 生成验证码图片
     */
    public String generateCaptchaImage(String code) {
        try {
            BufferedImage image = new BufferedImage(WIDTH, HEIGHT, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = image.createGraphics();

            // 设置抗锯齿
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            // 填充背景
            g2d.setColor(getRandomColor(200, 250));
            g2d.fillRect(0, 0, WIDTH, HEIGHT);

            // 绘制干扰线
            drawInterferenceLines(g2d);

            // 绘制验证码字符
            drawCode(g2d, code);

            // 添加噪点
            drawNoise(g2d);

            g2d.dispose();

            // 转换为Base64
            return imageToBase64(image);

        } catch (Exception e) {
            log.error("生成验证码图片失败", e);
            throw new RuntimeException("生成验证码图片失败");
        }
    }

    /**
     * 绘制验证码字符
     */
    private void drawCode(Graphics2D g2d, String code) {
        int x = 15;
        int y = HEIGHT / 2 + 5;
        
        for (int i = 0; i < code.length(); i++) {
            // 随机颜色
            g2d.setColor(getRandomColor(20, 130));
            
            // 随机字体
            Font font = new Font("Arial", Font.BOLD + Font.ITALIC, FONT_SIZE + random.nextInt(5));
            g2d.setFont(font);
            
            // 随机旋转角度
            double angle = (random.nextDouble() - 0.5) * 0.4;
            g2d.rotate(angle, x, y);
            
            // 绘制字符
            g2d.drawString(String.valueOf(code.charAt(i)), x, y);
            
            // 恢复旋转
            g2d.rotate(-angle, x, y);
            
            x += (WIDTH - 30) / code.length();
        }
    }

    /**
     * 绘制干扰线
     */
    private void drawInterferenceLines(Graphics2D g2d) {
        for (int i = 0; i < 5; i++) {
            g2d.setColor(getRandomColor(160, 200));
            g2d.setStroke(new BasicStroke(1.0f + random.nextFloat()));
            
            int x1 = random.nextInt(WIDTH);
            int y1 = random.nextInt(HEIGHT);
            int x2 = random.nextInt(WIDTH);
            int y2 = random.nextInt(HEIGHT);
            
            g2d.drawLine(x1, y1, x2, y2);
        }
    }

    /**
     * 添加噪点
     */
    private void drawNoise(Graphics2D g2d) {
        for (int i = 0; i < 50; i++) {
            g2d.setColor(getRandomColor(50, 200));
            int x = random.nextInt(WIDTH);
            int y = random.nextInt(HEIGHT);
            g2d.fillOval(x, y, 1, 1);
        }
    }

    /**
     * 获取随机颜色
     */
    private Color getRandomColor(int min, int max) {
        int r = min + random.nextInt(max - min);
        int g = min + random.nextInt(max - min);
        int b = min + random.nextInt(max - min);
        return new Color(r, g, b);
    }

    /**
     * 图片转Base64
     */
    private String imageToBase64(BufferedImage image) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "png", baos);
        byte[] imageBytes = baos.toByteArray();
        return "data:image/png;base64," + Base64.getEncoder().encodeToString(imageBytes);
    }

    /**
     * 生成拼图验证码数据
     */
    public String generatePuzzleCaptcha() {
        // 这里可以实现拼图验证码的生成逻辑
        // 返回JSON格式的拼图数据
        return "{}";
    }

    /**
     * 验证拼图验证码
     */
    public boolean verifyPuzzleCaptcha(String puzzleData, int x, int y) {
        // 这里实现拼图验证码的验证逻辑
        // 检查拼图块的位置是否正确
        return true;
    }
}
