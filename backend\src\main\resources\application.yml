server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: patrol-system
  
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************************************************
    username: root
    password: Aiwa75210!
    
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      update-strategy: not_null
      insert-strategy: not_null
      select-strategy: not_empty

# 日志配置
logging:
  level:
    com.aidesign.patrol: debug
    org.springframework.security: debug
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'

# JWT配置
jwt:
  secret: aidesign-patrol-system-jwt-secret-key-2024
  expiration: 86400000  # 24小时
  header: Authorization
  prefix: Bearer

# 文件上传配置
file:
  upload:
    path: ./uploads/
    max-size: 10MB
