// 认证中间件
const jwt = require('jsonwebtoken');

// JWT密钥（生产环境应该从环境变量获取）
const JWT_SECRET = process.env.JWT_SECRET || 'patrol-system-secret-key';

/**
 * 验证JWT Token中间件
 */
function authenticateToken(req, res, next) {
  // 在开发环境下，暂时跳过认证
  if (process.env.NODE_ENV === 'development' || !process.env.JWT_SECRET) {
    // 模拟用户信息
    req.user = {
      id: 1,
      username: 'admin',
      role: 'admin'
    };
    return next();
  }

  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({
      code: 401,
      message: '访问令牌缺失'
    });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({
        code: 403,
        message: '访问令牌无效'
      });
    }

    req.user = user;
    next();
  });
}

/**
 * 生成JWT Token
 */
function generateToken(user) {
  return jwt.sign(
    {
      id: user.id,
      username: user.username,
      role: user.role
    },
    JWT_SECRET,
    { expiresIn: '24h' }
  );
}

/**
 * 验证管理员权限
 */
function requireAdmin(req, res, next) {
  if (!req.user || req.user.role !== 'admin') {
    return res.status(403).json({
      code: 403,
      message: '需要管理员权限'
    });
  }
  next();
}

/**
 * 可选的认证中间件（不强制要求登录）
 */
function optionalAuth(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return next();
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (!err) {
      req.user = user;
    }
    next();
  });
}

module.exports = {
  authenticateToken,
  generateToken,
  requireAdmin,
  optionalAuth
};
