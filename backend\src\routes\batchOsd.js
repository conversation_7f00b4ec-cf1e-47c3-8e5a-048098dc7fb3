// 批量OSD设置路由
const express = require('express');
const router = express.Router();
const batchOsdController = require('../controllers/batchOsdController');
const executionStateController = require('../controllers/executionStateController');

/**
 * @swagger
 * /api/batch-osd/plans:
 *   get:
 *     summary: 获取批量OSD计划列表
 *     tags: [Batch OSD]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 页码
 *       - in: query
 *         name: size
 *         schema:
 *           type: integer
 *         description: 每页数量
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, running, completed, failed, cancelled, paused]
 *         description: 状态
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/plans', batchOsdController.getBatchOsdPlans);

/**
 * @swagger
 * /api/batch-osd/plans/{id}:
 *   get:
 *     summary: 获取批量OSD计划详情
 *     tags: [Batch OSD]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 计划ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 计划不存在
 */
router.get('/plans/:id', batchOsdController.getBatchOsdPlanById);

/**
 * @swagger
 * /api/batch-osd/plans:
 *   post:
 *     summary: 创建批量OSD计划
 *     tags: [Batch OSD]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - rule_id
 *               - target_type
 *               - execute_mode
 *             properties:
 *               name:
 *                 type: string
 *                 description: 计划名称
 *               description:
 *                 type: string
 *                 description: 计划描述
 *               rule_id:
 *                 type: integer
 *                 description: OSD规则ID
 *               target_type:
 *                 type: string
 *                 enum: [all, province, city, district, custom]
 *                 description: 目标类型
 *               target_ids:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 目标ID列表
 *               execute_mode:
 *                 type: string
 *                 enum: [immediate, scheduled]
 *                 description: 执行方式
 *               schedule_time:
 *                 type: string
 *                 format: date-time
 *                 description: 计划执行时间
 *               concurrency:
 *                 type: integer
 *                 description: 并发数量
 *     responses:
 *       200:
 *         description: 创建成功
 *       400:
 *         description: 参数错误
 */
router.post('/plans', batchOsdController.createBatchOsdPlan);

/**
 * @swagger
 * /api/batch-osd/plans/{id}:
 *   put:
 *     summary: 更新批量OSD计划
 *     tags: [Batch OSD]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 计划ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - rule_id
 *               - target_type
 *               - execute_mode
 *             properties:
 *               name:
 *                 type: string
 *                 description: 计划名称
 *               description:
 *                 type: string
 *                 description: 计划描述
 *               rule_id:
 *                 type: integer
 *                 description: OSD规则ID
 *               target_type:
 *                 type: string
 *                 enum: [all, province, city, district, custom]
 *                 description: 目标类型
 *               target_ids:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 目标ID列表
 *               execute_mode:
 *                 type: string
 *                 enum: [immediate, scheduled]
 *                 description: 执行方式
 *               schedule_time:
 *                 type: string
 *                 format: date-time
 *                 description: 计划执行时间
 *               concurrency:
 *                 type: integer
 *                 description: 并发数量
 *     responses:
 *       200:
 *         description: 更新成功
 *       400:
 *         description: 参数错误
 *       404:
 *         description: 计划不存在
 */
router.put('/plans/:id', batchOsdController.updateBatchOsdPlan);

/**
 * @swagger
 * /api/batch-osd/plans/{id}:
 *   delete:
 *     summary: 删除批量OSD计划
 *     tags: [Batch OSD]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 计划ID
 *     responses:
 *       200:
 *         description: 删除成功
 *       404:
 *         description: 计划不存在
 */
router.delete('/plans/:id', batchOsdController.deleteBatchOsdPlan);

/**
 * @swagger
 * /api/batch-osd/plans/batch:
 *   delete:
 *     summary: 批量删除计划
 *     tags: [Batch OSD]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - ids
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: 计划ID列表
 *     responses:
 *       200:
 *         description: 删除成功
 *       400:
 *         description: 参数错误
 */
router.delete('/plans/batch', batchOsdController.batchDeleteBatchOsdPlans);

/**
 * @swagger
 * /api/batch-osd/plans/{id}/execute:
 *   post:
 *     summary: 执行批量OSD计划
 *     tags: [Batch OSD]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 计划ID
 *     responses:
 *       200:
 *         description: 执行成功
 *       400:
 *         description: 状态不允许执行
 *       404:
 *         description: 计划不存在
 */
router.post('/plans/:id/execute', batchOsdController.executeBatchOsdPlan);

/**
 * @swagger
 * /api/batch-osd/plans/{id}/pause:
 *   post:
 *     summary: 暂停批量OSD计划
 *     tags: [Batch OSD]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 计划ID
 *     responses:
 *       200:
 *         description: 暂停成功
 *       400:
 *         description: 状态不允许暂停
 *       404:
 *         description: 计划不存在
 */
router.post('/plans/:id/pause', batchOsdController.pauseBatchOsdPlan);

/**
 * @swagger
 * /api/batch-osd/plans/{id}/resume:
 *   post:
 *     summary: 继续执行批量OSD计划
 *     tags: [Batch OSD]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 计划ID
 *     responses:
 *       200:
 *         description: 继续执行成功
 *       400:
 *         description: 状态不允许继续执行
 *       404:
 *         description: 计划不存在
 */
router.post('/plans/:id/resume', batchOsdController.resumeBatchOsdPlan);

/**
 * @swagger
 * /api/batch-osd/plans/{id}/cancel:
 *   post:
 *     summary: 取消批量OSD计划
 *     tags: [Batch OSD]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 计划ID
 *     responses:
 *       200:
 *         description: 取消成功
 *       400:
 *         description: 状态不允许取消
 *       404:
 *         description: 计划不存在
 */
router.post('/plans/:id/cancel', batchOsdController.cancelBatchOsdPlan);

/**
 * @swagger
 * /api/batch-osd/available-rules:
 *   get:
 *     summary: 获取可用的OSD规则
 *     tags: [Batch OSD]
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/available-rules', batchOsdController.getAvailableOsdRules);

/**
 * @swagger
 * /api/batch-osd/plans/{id}/stats:
 *   get:
 *     summary: 获取计划执行统计
 *     tags: [Batch OSD]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 计划ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 计划不存在
 */
router.get('/plans/:id/stats', batchOsdController.getPlanExecutionStats);

/**
 * @swagger
 * /api/batch-osd/plans/{id}/monitor:
 *   get:
 *     summary: 获取计划实时监控数据
 *     tags: [Batch OSD]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 计划ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 计划不存在
 */
router.get('/plans/:id/monitor', batchOsdController.getPlanMonitorData);

// ==================== 数据持久化相关路由 ====================

/**
 * @swagger
 * /api/batch-osd/plans-with-persistence:
 *   post:
 *     summary: 创建带数据持久化的批量OSD计划
 *     tags: [Batch OSD Persistence]
 */
router.post('/plans-with-persistence', batchOsdController.createBatchOsdPlanWithPersistence);

/**
 * @swagger
 * /api/batch-osd/plans/{id}/execute-with-persistence:
 *   post:
 *     summary: 执行带数据持久化的批量OSD计划
 *     tags: [Batch OSD Persistence]
 */
router.post('/plans/:id/execute-with-persistence', batchOsdController.executeBatchOsdPlanWithPersistence);

/**
 * @swagger
 * /api/batch-osd/plans/{id}/restore-backup:
 *   post:
 *     summary: 一键恢复OSD备份
 *     tags: [Batch OSD Persistence]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 计划ID
 *     responses:
 *       200:
 *         description: 恢复成功
 *       400:
 *         description: 计划未启用备份功能或恢复失败
 *       404:
 *         description: 计划不存在
 */
router.post('/plans/:id/restore-backup', batchOsdController.restoreOsdBackup);

/**
 * @swagger
 * /api/batch-osd/plans/{id}/detail-with-persistence:
 *   get:
 *     summary: 获取计划详细信息（包含持久化数据）
 *     tags: [Batch OSD Persistence]
 */
router.get('/plans/:id/detail-with-persistence', batchOsdController.getBatchOsdPlanDetailWithPersistence);

/**
 * @swagger
 * /api/batch-osd/plans/{id}/sites:
 *   get:
 *     summary: 获取计划选定的考点列表
 *     tags: [Batch OSD Persistence]
 */
router.get('/plans/:id/sites', batchOsdController.getPlanSites);

/**
 * @swagger
 * /api/batch-osd/plans/{planId}/sites/{siteId}/rooms:
 *   get:
 *     summary: 获取考点的考场列表
 *     tags: [Batch OSD Persistence]
 */
router.get('/plans/:planId/sites/:siteId/rooms', batchOsdController.getSiteExamRooms);

/**
 * @swagger
 * /api/batch-osd/plan/{planId}/site/{siteId}/channels:
 *   get:
 *     summary: 获取考点的通道执行详情
 *     tags: [Batch OSD]
 *     parameters:
 *       - in: path
 *         name: planId
 *         required: true
 *         schema:
 *           type: integer
 *         description: 计划ID
 *       - in: path
 *         name: siteId
 *         required: true
 *         schema:
 *           type: string
 *         description: 考点ID
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/plan/:planId/site/:siteId/channels', batchOsdController.getSiteChannels);

/**
 * @swagger
 * /api/batch-osd/plans/{planId}/target-sites:
 *   get:
 *     summary: 获取计划的目标考点列表
 *     tags: [Batch OSD]
 *     parameters:
 *       - in: path
 *         name: planId
 *         required: true
 *         schema:
 *           type: integer
 *         description: 计划ID
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/plans/:planId/target-sites', batchOsdController.getPlanTargetSites);

/**
 * @swagger
 * /api/batch-osd/plans/{planId}/room-channel-bindings:
 *   get:
 *     summary: 获取计划的房间通道绑定信息
 *     tags: [Batch OSD]
 *     parameters:
 *       - in: path
 *         name: planId
 *         required: true
 *         schema:
 *           type: integer
 *         description: 计划ID
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/plans/:planId/room-channel-bindings', batchOsdController.getPlanRoomChannelBindings);

/**
 * @swagger
 * /api/batch-osd/plans/{planId}/osd-data:
 *   get:
 *     summary: 获取计划的OSD设置数据（包含原始OSD和目标OSD）
 *     tags: [Batch OSD]
 *     parameters:
 *       - in: path
 *         name: planId
 *         required: true
 *         schema:
 *           type: integer
 *         description: 计划ID
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/plans/:planId/osd-data', batchOsdController.getPlanOsdData);

/**
 * @swagger
 * /api/batch-osd/plans/{id}/execution-logs-with-filter:
 *   get:
 *     summary: 获取计划执行日志（支持筛选）
 *     tags: [Batch OSD Persistence]
 */
router.get('/plans/:id/execution-logs-with-filter', batchOsdController.getPlanExecutionLogsWithFilter);

/**
 * @swagger
 * /api/batch-osd/plans-with-persistence:
 *   post:
 *     summary: 创建批量OSD计划（带完整数据持久化）
 *     tags: [Batch OSD Persistence]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: 计划名称
 *               description:
 *                 type: string
 *                 description: 计划描述
 *               rule_id:
 *                 type: integer
 *                 description: OSD规则ID
 *               target_type:
 *                 type: string
 *                 description: 目标类型
 *               target_ids:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 目标ID列表
 *               execute_mode:
 *                 type: string
 *                 enum: [immediate, scheduled]
 *                 description: 执行方式
 *               schedule_time:
 *                 type: string
 *                 format: date-time
 *                 description: 计划执行时间
 *               concurrency:
 *                 type: integer
 *                 description: 并发数
 *               selected_sites:
 *                 type: array
 *                 description: 选定的考点数据
 *               generated_rooms:
 *                 type: array
 *                 description: 生成的考场数据
 *               room_channel_bindings:
 *                 type: array
 *                 description: 考场与视频通道绑定关系
 *               osd_config:
 *                 type: object
 *                 description: OSD配置
 *               room_config:
 *                 type: object
 *                 description: 考场配置
 *               schedule_config:
 *                 type: object
 *                 description: 调度配置
 *     responses:
 *       200:
 *         description: 创建成功
 */
router.post('/plans-with-persistence', batchOsdController.createBatchOsdPlanWithPersistence);

/**
 * @swagger
 * /api/batch-osd/plans/{id}/backup-restore-results:
 *   get:
 *     summary: 获取OSD备份恢复结果
 *     tags: [Batch OSD]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 计划ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 计划不存在
 */
router.get('/plans/:id/backup-restore-results', batchOsdController.getOsdBackupRestoreResults);

// ==================== 执行状态管理路由 ====================

/**
 * @swagger
 * /api/batch-osd/plans/{planId}/execution-state:
 *   post:
 *     summary: 保存执行状态快照
 *     tags: [Batch OSD Execution State]
 *     parameters:
 *       - in: path
 *         name: planId
 *         required: true
 *         schema:
 *           type: integer
 *         description: 计划ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               stateType:
 *                 type: string
 *                 enum: [initial, progress, completed]
 *                 description: 状态类型
 *               executionPhase:
 *                 type: string
 *                 enum: [osd_setting, osd_restore]
 *                 description: 执行阶段
 *               executionStats:
 *                 type: object
 *                 description: 执行统计信息
 *               executionFlags:
 *                 type: object
 *                 description: 执行标志
 *               sitesTreeData:
 *                 type: object
 *                 description: 机构考点树数据
 *               currentSelectedSite:
 *                 type: object
 *                 description: 当前选中的考点
 *               osdExecutionList:
 *                 type: array
 *                 description: OSD执行列表
 *               concurrencyConfig:
 *                 type: integer
 *                 description: 并发配置
 *     responses:
 *       200:
 *         description: 保存成功
 */
router.post('/plans/:planId/execution-state', executionStateController.saveExecutionState);

/**
 * @swagger
 * /api/batch-osd/plans/{planId}/execution-state:
 *   get:
 *     summary: 获取执行状态快照
 *     tags: [Batch OSD Execution State]
 *     parameters:
 *       - in: path
 *         name: planId
 *         required: true
 *         schema:
 *           type: integer
 *         description: 计划ID
 *       - in: query
 *         name: executionPhase
 *         schema:
 *           type: string
 *           enum: [osd_setting, osd_restore]
 *         description: 执行阶段（可选）
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 未找到执行状态
 */
router.get('/plans/:planId/execution-state', executionStateController.getExecutionState);

/**
 * @swagger
 * /api/batch-osd/plans/{planId}/execution-state:
 *   delete:
 *     summary: 删除执行状态快照
 *     tags: [Batch OSD Execution State]
 *     parameters:
 *       - in: path
 *         name: planId
 *         required: true
 *         schema:
 *           type: integer
 *         description: 计划ID
 *     responses:
 *       200:
 *         description: 删除成功
 */
router.delete('/plans/:planId/execution-state', executionStateController.deleteExecutionState);

// 获取执行状态历史
router.get('/plans/:planId/execution-state/history', executionStateController.getExecutionStateHistory);

// 清理过期状态
router.delete('/plans/:planId/execution-state/cleanup', executionStateController.cleanupOldStates);

module.exports = router;
