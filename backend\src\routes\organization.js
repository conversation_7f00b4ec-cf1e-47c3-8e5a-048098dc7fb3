const express = require('express');
const router = express.Router();
const organizationController = require('../controllers/organizationController');
const { authenticateToken } = require('../middleware/auth');

// 应用认证中间件
router.use(authenticateToken);

/**
 * @swagger
 * /api/organization/tree:
 *   get:
 *     summary: 获取机构树数据
 *     tags: [Organization]
 *     parameters:
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: level
 *         schema:
 *           type: string
 *           enum: [province, city, district, examSite]
 *         description: 机构层级
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *           enum: [0, 1]
 *         description: 状态
 *       - in: query
 *         name: parentId
 *         schema:
 *           type: string
 *         description: 父级机构ID
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取成功
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/OrganizationNode'
 */
router.get('/tree', organizationController.getOrganizationTree);

/**
 * @swagger
 * /api/organization/{id}:
 *   get:
 *     summary: 获取机构详情
 *     tags: [Organization]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 机构ID
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取成功
 *                 data:
 *                   $ref: '#/components/schemas/OrganizationNode'
 *       404:
 *         description: 机构不存在
 */
router.get('/:id', organizationController.getOrganizationDetail);

/**
 * @swagger
 * /api/organization:
 *   post:
 *     summary: 创建机构
 *     tags: [Organization]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - parentId
 *               - level
 *               - code
 *               - fullName
 *               - shortName
 *             properties:
 *               parentId:
 *                 type: string
 *                 description: 上级机构ID
 *               level:
 *                 type: string
 *                 enum: [province, city, district, examSite]
 *                 description: 机构层级
 *               code:
 *                 type: string
 *                 description: 机构码
 *               fullName:
 *                 type: string
 *                 description: 机构全称
 *               shortName:
 *                 type: string
 *                 description: 机构简称
 *               singleName:
 *                 type: string
 *                 description: 单字简称（省份专用）
 *               uri:
 *                 type: string
 *                 description: URI（考点专用）
 *               sort:
 *                 type: integer
 *                 description: 排序
 *               status:
 *                 type: integer
 *                 enum: [0, 1]
 *                 description: 状态
 *     responses:
 *       200:
 *         description: 创建成功
 *       400:
 *         description: 参数错误或机构码已存在
 */
router.post('/', organizationController.createOrganization);

/**
 * @swagger
 * /api/organization/{id}:
 *   put:
 *     summary: 更新机构
 *     tags: [Organization]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 机构ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               fullName:
 *                 type: string
 *                 description: 机构全称
 *               shortName:
 *                 type: string
 *                 description: 机构简称
 *               singleName:
 *                 type: string
 *                 description: 单字简称（省份专用）
 *               uri:
 *                 type: string
 *                 description: URI（考点专用）
 *               sort:
 *                 type: integer
 *                 description: 排序
 *               status:
 *                 type: integer
 *                 enum: [0, 1]
 *                 description: 状态
 *     responses:
 *       200:
 *         description: 更新成功
 *       404:
 *         description: 机构不存在
 */
router.put('/:id', organizationController.updateOrganization);

/**
 * @swagger
 * /api/organization/{id}:
 *   delete:
 *     summary: 删除机构
 *     tags: [Organization]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 机构ID
 *     responses:
 *       200:
 *         description: 删除成功
 *       400:
 *         description: 存在下级机构，无法删除
 *       404:
 *         description: 机构不存在
 */
router.delete('/:id', organizationController.deleteOrganization);

/**
 * @swagger
 * /api/organization/stats:
 *   get:
 *     summary: 获取机构统计信息
 *     tags: [Organization]
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     provinceCount:
 *                       type: integer
 *                       description: 省份数量
 *                     cityCount:
 *                       type: integer
 *                       description: 地市数量
 *                     districtCount:
 *                       type: integer
 *                       description: 区县数量
 *                     examSiteCount:
 *                       type: integer
 *                       description: 考点数量
 *                     totalCount:
 *                       type: integer
 *                       description: 总数量
 */
router.get('/stats', organizationController.getOrganizationStats);

/**
 * @swagger
 * components:
 *   schemas:
 *     OrganizationNode:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: 机构ID
 *         parentId:
 *           type: string
 *           description: 上级机构ID
 *         fullName:
 *           type: string
 *           description: 机构全称
 *         shortName:
 *           type: string
 *           description: 机构简称
 *         singleName:
 *           type: string
 *           description: 单字简称（省份专用）
 *         uri:
 *           type: string
 *           description: URI（考点专用）
 *         level:
 *           type: string
 *           enum: [province, city, district, examSite]
 *           description: 机构层级
 *         sort:
 *           type: integer
 *           description: 排序
 *         status:
 *           type: integer
 *           enum: [0, 1]
 *           description: 状态
 *         createTime:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *         updateTime:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 *         childrenCount:
 *           type: integer
 *           description: 子级机构数量
 *         examSiteCount:
 *           type: integer
 *           description: 考点数量
 *         children:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/OrganizationNode'
 *           description: 子级机构
 */

module.exports = router;
