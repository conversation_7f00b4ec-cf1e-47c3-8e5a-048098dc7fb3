// OSD规则管理路由
const express = require('express');
const router = express.Router();
const osdRulesController = require('../controllers/osdRulesController');

/**
 * @swagger
 * /api/osd-rules:
 *   get:
 *     summary: 获取OSD规则列表
 *     tags: [OSD Rules]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 页码
 *       - in: query
 *         name: size
 *         schema:
 *           type: integer
 *         description: 每页数量
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *           enum: [0, 1]
 *         description: 状态
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/', osdRulesController.getOsdRules);

/**
 * @swagger
 * /api/osd-rules/{id}:
 *   get:
 *     summary: 获取OSD规则详情
 *     tags: [OSD Rules]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 规则ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 规则不存在
 */
router.get('/:id', osdRulesController.getOsdRuleById);

/**
 * @swagger
 * /api/osd-rules:
 *   post:
 *     summary: 创建OSD规则
 *     tags: [OSD Rules]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - format
 *             properties:
 *               name:
 *                 type: string
 *                 description: 规则名称
 *               type:
 *                 type: string
 *                 description: 规则类型
 *               format:
 *                 type: string
 *                 description: 格式模板
 *               description:
 *                 type: string
 *                 description: 规则描述
 *               preview:
 *                 type: string
 *                 description: 预览效果
 *               status:
 *                 type: integer
 *                 description: 状态
 *     responses:
 *       200:
 *         description: 创建成功
 *       400:
 *         description: 参数错误
 */
router.post('/', osdRulesController.createOsdRule);

/**
 * @swagger
 * /api/osd-rules/{id}:
 *   put:
 *     summary: 更新OSD规则
 *     tags: [OSD Rules]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 规则ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - format
 *             properties:
 *               name:
 *                 type: string
 *                 description: 规则名称
 *               type:
 *                 type: string
 *                 description: 规则类型
 *               format:
 *                 type: string
 *                 description: 格式模板
 *               description:
 *                 type: string
 *                 description: 规则描述
 *               preview:
 *                 type: string
 *                 description: 预览效果
 *               status:
 *                 type: integer
 *                 description: 状态
 *     responses:
 *       200:
 *         description: 更新成功
 *       400:
 *         description: 参数错误
 *       404:
 *         description: 规则不存在
 */
router.put('/:id', osdRulesController.updateOsdRule);

/**
 * @swagger
 * /api/osd-rules/{id}:
 *   delete:
 *     summary: 删除OSD规则
 *     tags: [OSD Rules]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 规则ID
 *     responses:
 *       200:
 *         description: 删除成功
 *       404:
 *         description: 规则不存在
 */
router.delete('/:id', osdRulesController.deleteOsdRule);

/**
 * @swagger
 * /api/osd-rules/batch:
 *   delete:
 *     summary: 批量删除OSD规则
 *     tags: [OSD Rules]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - ids
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: 规则ID列表
 *     responses:
 *       200:
 *         description: 删除成功
 *       400:
 *         description: 参数错误
 */
router.delete('/batch', osdRulesController.batchDeleteOsdRules);

/**
 * @swagger
 * /api/osd-rules/available:
 *   get:
 *     summary: 获取可用的OSD规则
 *     tags: [OSD Rules]
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/available', osdRulesController.getAvailableOsdRules);

module.exports = router;
