// 批量OSD数据持久化服务
const { query, transaction } = require('../config/database');

class BatchOsdPersistenceService {

  // ==================== 计划选定考点管理 ====================

  /**
   * 保存计划选定的考点数据
   * @param {number} planId - 计划ID
   * @param {Array} sites - 考点数据数组
   */
  async savePlanSites(planId, sites) {
    return await transaction(async (connection) => {
      // 先清除该计划的旧数据
      await connection.execute('DELETE FROM batch_osd_plan_sites WHERE plan_id = ?', [planId]);

      if (sites && sites.length > 0) {
        // 批量插入新数据
        const insertSQL = `
          INSERT INTO batch_osd_plan_sites
          (plan_id, site_id, site_name, site_code, province_id, province_name,
           city_id, city_name, district_id, district_name, address, contact_person,
           contact_phone, room_count, status)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        for (const site of sites) {
          // 处理考场数量字段映射：前端使用roomCount，后端使用room_count
          // 优先使用roomCount，如果没有则使用room_count，最后默认为30
          const roomCount = site.roomCount || site.room_count || 30;
          
          console.log(`保存考点 ${site.name || site.site_name}，考场数量: ${roomCount}`);
          
          await connection.execute(insertSQL, [
            planId,
            site.id || site.site_id || null,
            site.name || site.site_name || null,
            site.code || site.site_code || null,
            site.province_id || null,
            site.province_name || null,
            site.city_id || null,
            site.city_name || null,
            site.district_id || null,
            site.district_name || null,
            site.address || null,
            site.contact_person || null,
            site.contact_phone || null,
            roomCount,
            'selected'
          ]);
        }
      }

      return sites.length;
    });
  }

  /**
   * 获取计划选定的考点列表
   * @param {number} planId - 计划ID
   */
  async getPlanSites(planId) {
    const sql = `
      SELECT * FROM batch_osd_plan_sites
      WHERE plan_id = ?
      ORDER BY create_time ASC
    `;
    return await query(sql, [planId]);
  }

  /**
   * 更新考点处理状态
   * @param {number} planId - 计划ID
   * @param {string} siteId - 考点ID
   * @param {string} status - 状态
   */
  async updateSiteStatus(planId, siteId, status) {
    const sql = `
      UPDATE batch_osd_plan_sites
      SET status = ?, update_time = CURRENT_TIMESTAMP
      WHERE plan_id = ? AND site_id = ?
    `;
    const result = await query(sql, [status, planId, siteId]);
    return result.affectedRows > 0;
  }

  // ==================== 考场编号生成管理 ====================

  /**
   * 生成并保存考场编号
   * @param {number} planId - 计划ID
   * @param {string} siteId - 考点ID
   * @param {number} roomCount - 考场数量
   * @param {Object} options - 生成选项
   */
  async generateAndSaveExamRooms(planId, siteId, roomCount, options = {}) {
    return await transaction(async (connection) => {
      // 先清除该考点的旧考场数据
      await connection.execute(
        'DELETE FROM batch_osd_exam_rooms WHERE plan_id = ? AND site_id = ?',
        [planId, siteId]
      );

      const rooms = [];
      const insertSQL = `
        INSERT INTO batch_osd_exam_rooms
        (plan_id, site_id, room_number, room_name, room_type, capacity,
         floor_number, building_name, equipment_info, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      for (let i = 1; i <= roomCount; i++) {
        const roomNumber = this.generateRoomNumber(i, options.numberFormat);
        const roomName = `${options.prefix || '考场'}${roomNumber}`;
        const floorNumber = Math.ceil(i / (options.roomsPerFloor || 20));

        const roomData = {
          plan_id: planId,
          site_id: siteId,
          room_number: roomNumber,
          room_name: roomName,
          room_type: options.roomType || 'standard',
          capacity: options.capacity || 30,
          floor_number: floorNumber,
          building_name: options.buildingName || `${floorNumber}号楼`,
          equipment_info: JSON.stringify(options.equipment || {}),
          status: 'generated'
        };

        const result = await connection.execute(insertSQL, [
          roomData.plan_id, roomData.site_id, roomData.room_number,
          roomData.room_name, roomData.room_type, roomData.capacity,
          roomData.floor_number, roomData.building_name,
          roomData.equipment_info, roomData.status
        ]);

        // 添加插入后的ID
        roomData.id = result[0].insertId;
        rooms.push(roomData);
      }

      return rooms;
    });
  }

  /**
   * 生成考场编号
   * @param {number} index - 序号
   * @param {string} format - 格式 ('01', '001', 'A01', etc.)
   */
  generateRoomNumber(index, format = '01') {
    switch (format) {
      case '001':
        return String(index).padStart(3, '0');
      case 'A01':
        return 'A' + String(index).padStart(2, '0');
      case 'A001':
        return 'A' + String(index).padStart(3, '0');
      default:
        return String(index).padStart(2, '0');
    }
  }

  /**
   * 获取考点的考场列表
   * @param {number} planId - 计划ID
   * @param {string} siteId - 考点ID
   */
  async getSiteExamRooms(planId, siteId) {
    const sql = `
      SELECT * FROM batch_osd_exam_rooms
      WHERE plan_id = ? AND site_id = ?
      ORDER BY room_number ASC
    `;
    return await query(sql, [planId, siteId]);
  }

  // ==================== 考点考场绑定关系管理 ====================

  /**
   * 创建考点考场绑定关系
   * @param {number} planId - 计划ID
   * @param {string} siteId - 考点ID
   * @param {Array} roomIds - 考场ID数组
   * @param {string} osdTemplate - OSD模板
   */
  async createSiteRoomBindings(planId, siteId, roomIds, osdTemplate) {
    return await transaction(async (connection) => {
      // 先清除该考点的旧绑定关系
      await connection.execute(
        'DELETE FROM batch_osd_site_room_bindings WHERE plan_id = ? AND site_id = ?',
        [planId, siteId]
      );

      const insertSQL = `
        INSERT INTO batch_osd_site_room_bindings
        (plan_id, site_id, room_id, binding_type, binding_order,
         osd_template, osd_content, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const bindings = [];
      for (let i = 0; i < roomIds.length; i++) {
        const roomId = roomIds[i];
        const osdContent = await this.generateOsdContent(planId, siteId, roomId, osdTemplate);

        await connection.execute(insertSQL, [
          planId, siteId, roomId, 'auto', i + 1,
          osdTemplate, osdContent, true
        ]);

        bindings.push({
          plan_id: planId,
          site_id: siteId,
          room_id: roomId,
          binding_order: i + 1,
          osd_content: osdContent
        });
      }

      return bindings;
    });
  }

  /**
   * 生成OSD内容
   * @param {number} planId - 计划ID
   * @param {string} siteId - 考点ID
   * @param {number} roomId - 考场ID
   * @param {string} template - OSD模板
   */
  async generateOsdContent(planId, siteId, roomId, template) {
    // 获取考点信息
    const siteInfo = await this.getPlanSiteInfo(planId, siteId);
    // 获取考场信息
    const roomInfo = await this.getExamRoomInfo(roomId);

    if (!siteInfo || !roomInfo) {
      return template;
    }

    // 替换模板中的占位符
    let content = template;
    content = content.replace(/\[省份\]/g, siteInfo.province_name || '');
    content = content.replace(/\[省份简称\]/g, this.getProvinceShortName(siteInfo.province_name));
    content = content.replace(/\[地市\]/g, siteInfo.city_name || '');
    content = content.replace(/\[区县\]/g, siteInfo.district_name || '');
    content = content.replace(/\[考点名称\]/g, siteInfo.site_name || '');
    content = content.replace(/\[考点编号\]/g, siteInfo.site_code || '');
    content = content.replace(/\[考场号\]/g, roomInfo.room_number || '');
    content = content.replace(/考场\[考场号\]/g, `考场${roomInfo.room_number || ''}`);

    return content;
  }

  /**
   * 获取省份简称
   */
  getProvinceShortName(provinceName) {
    const shortNames = {
      '四川省': '川', '浙江省': '浙', '江苏省': '苏', '广东省': '粤',
      '山东省': '鲁', '河南省': '豫', '湖北省': '鄂', '湖南省': '湘'
    };
    return shortNames[provinceName] || provinceName?.charAt(0) || '';
  }

  /**
   * 获取计划考点信息
   */
  async getPlanSiteInfo(planId, siteId) {
    const sql = 'SELECT * FROM batch_osd_plan_sites WHERE plan_id = ? AND site_id = ?';
    const results = await query(sql, [planId, siteId]);
    return results[0] || null;
  }

  /**
   * 获取考场信息
   */
  async getExamRoomInfo(roomId) {
    const sql = 'SELECT * FROM batch_osd_exam_rooms WHERE id = ?';
    const results = await query(sql, [roomId]);
    return results[0] || null;
  }

  // ==================== 执行结果日志管理 ====================

  /**
   * 创建执行日志
   * @param {Object} logData - 日志数据
   */
  async createExecutionLog(logData) {
    const sql = `
      INSERT INTO batch_osd_execution_logs
      (plan_id, site_id, site_name, status, message, execution_type,
       progress, start_time, operator)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await query(sql, [
      logData.plan_id,
      logData.site_id || null,
      logData.site_name || null,
      logData.status || 'pending',
      logData.message || null,
      logData.execution_type || 'site',
      logData.progress || 0,
      logData.start_time || null,
      logData.operator || 'system'
    ]);

    return result.insertId;
  }

  /**
   * 更新执行日志
   * @param {number} logId - 日志ID
   * @param {Object} updateData - 更新数据
   */
  async updateExecutionLog(logId, updateData) {
    const fields = [];
    const values = [];

    if (updateData.status !== undefined) {
      fields.push('status = ?');
      values.push(updateData.status);
    }
    if (updateData.progress !== undefined) {
      fields.push('progress = ?');
      values.push(updateData.progress);
    }
    if (updateData.message !== undefined) {
      fields.push('message = ?');
      values.push(updateData.message);
    }
    if (updateData.error_code !== undefined) {
      fields.push('error_code = ?');
      values.push(updateData.error_code);
    }
    if (updateData.error_message !== undefined) {
      fields.push('error_message = ?');
      values.push(updateData.error_message);
    }
    if (updateData.response_data !== undefined) {
      fields.push('response_data = ?');
      values.push(JSON.stringify(updateData.response_data));
    }
    if (updateData.end_time !== undefined) {
      fields.push('end_time = ?');
      values.push(updateData.end_time);
    }
    if (updateData.duration !== undefined) {
      fields.push('duration = ?');
      values.push(updateData.duration);
    }
    if (updateData.retry_count !== undefined) {
      fields.push('retry_count = ?');
      values.push(updateData.retry_count);
    }

    if (fields.length === 0) {
      return false;
    }

    fields.push('update_time = CURRENT_TIMESTAMP');
    values.push(logId);

    const sql = `UPDATE batch_osd_execution_logs SET ${fields.join(', ')} WHERE id = ?`;
    const result = await query(sql, values);
    return result.affectedRows > 0;
  }

  /**
   * 获取计划执行日志
   * @param {number} planId - 计划ID
   * @param {Object} options - 查询选项
   */
  async getPlanExecutionLogs(planId, options = {}) {
    let sql = `
      SELECT l.*, r.room_name, r.room_type
      FROM batch_osd_execution_logs l
      LEFT JOIN batch_osd_exam_rooms r ON l.room_id = r.id
      WHERE l.plan_id = ?
    `;
    const params = [planId];

    if (options.siteId) {
      sql += ' AND l.site_id = ?';
      params.push(options.siteId);
    }

    if (options.status) {
      sql += ' AND l.status = ?';
      params.push(options.status);
    }

    if (options.executionType) {
      sql += ' AND l.execution_type = ?';
      params.push(options.executionType);
    }

    sql += ' ORDER BY l.create_time DESC';

    if (options.limit) {
      sql += ' LIMIT ?';
      params.push(options.limit);
    }

    return await query(sql, params);
  }

  // ==================== 新增方法：支持完整计划数据保存 ====================

  /**
   * 保存生成的考场数据
   * @param {number} planId - 计划ID
   * @param {Array} rooms - 考场数据数组
   */
  async saveGeneratedRooms(planId, rooms) {
    return await transaction(async (connection) => {
      console.log('保存生成的考场数据，计划ID:', planId, '考场数量:', rooms.length);

      // 先清除该计划的旧考场数据
      await connection.execute('DELETE FROM batch_osd_generated_rooms WHERE plan_id = ?', [planId]);

      if (rooms.length === 0) {
        return 0;
      }

      const insertSQL = `
        INSERT INTO batch_osd_generated_rooms
        (plan_id, room_id, site_id, room_name, room_number, room_type,
         osd_label, floor_number, capacity, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      for (const room of rooms) {
        await connection.execute(insertSQL, [
          planId,
          room.id || null,
          room.siteId || null,
          room.roomName || room.label || null,
          room.roomNumber || null,
          room.type || 'room',
          room.osdLabel || null,
          room.floor || 1,
          room.capacity || 30,
          'generated'
        ]);
      }

      console.log('生成的考场数据保存完成');
      return rooms.length;
    });
  }

  /**
   * 保存考场与视频通道的绑定关系
   * @param {number} planId - 计划ID
   * @param {Array} bindings - 绑定关系数组
   */
  async saveRoomChannelBindings(planId, bindings) {
    return await transaction(async (connection) => {
      console.log('保存绑定关系，计划ID:', planId, '绑定数量:', bindings.length);

      // 先清除该计划的旧绑定关系
      await connection.execute('DELETE FROM batch_osd_room_channel_bindings WHERE plan_id = ?', [planId]);

      if (bindings.length === 0) {
        return 0;
      }

      const insertSQL = `
        INSERT INTO batch_osd_room_channel_bindings
        (plan_id, room_id, channel_id, binding_type, binding_order, is_active)
        VALUES (?, ?, ?, ?, ?, ?)
      `;

      for (let i = 0; i < bindings.length; i++) {
        const binding = bindings[i];
        await connection.execute(insertSQL, [
          planId,
          binding.room_id,
          binding.channel_id,
          'manual',
          i + 1,
          true
        ]);
      }

      console.log('绑定关系保存完成');
      return bindings.length;
    });
  }

  /**
   * 保存计划配置信息
   * @param {number} planId - 计划ID
   * @param {Object} configs - 配置数据
   */
  async savePlanConfigs(planId, configs) {
    return await transaction(async (connection) => {
      console.log('保存计划配置，计划ID:', planId);

      // 先清除该计划的旧配置
      await connection.execute('DELETE FROM batch_osd_plan_configs WHERE plan_id = ?', [planId]);

      const insertSQL = `
        INSERT INTO batch_osd_plan_configs
        (plan_id, config_type, config_data)
        VALUES (?, ?, ?)
      `;

      // 保存各种配置（确保转换为JSON字符串）
      if (configs.osd_config) {
        await connection.execute(insertSQL, [planId, 'osd_config', JSON.stringify(configs.osd_config)]);
      }

      if (configs.room_config) {
        await connection.execute(insertSQL, [planId, 'room_config', JSON.stringify(configs.room_config)]);
      }

      if (configs.schedule_config) {
        await connection.execute(insertSQL, [planId, 'schedule_config', JSON.stringify(configs.schedule_config)]);
      }

      if (configs.backup_config) {
        await connection.execute(insertSQL, [planId, 'backup_config', JSON.stringify(configs.backup_config)]);
      }

      console.log('计划配置保存完成');
      return true;
    });
  }

  // ==================== OSD备份管理 ====================

  /**
   * 保存OSD备份数据
   * @param {number} planId - 计划ID
   * @param {Array} backupData - 备份数据数组
   */
  async saveOsdBackupData(planId, backupData) {
    return await transaction(async (connection) => {
      console.log('保存OSD备份数据，计划ID:', planId, '数量:', backupData.length);

      // 先清除该计划的旧备份数据
      await connection.execute('DELETE FROM batch_osd_backup_data WHERE plan_id = ?', [planId]);

      if (backupData && backupData.length > 0) {
        const insertSQL = `
          INSERT INTO batch_osd_backup_data
          (plan_id, channel_id, channel_name, site_id, site_name, room_id, room_name,
           original_osd_content, backup_time, backup_strategy, retention_days, status)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        for (const backup of backupData) {
          await connection.execute(insertSQL, [
            planId,
            backup.channel_id,
            backup.channel_name || null,
            backup.site_id || null,
            backup.site_name || null,
            backup.room_id || null,
            backup.room_name || null,
            backup.original_osd_content || null,
            backup.backup_time || new Date(),
            backup.backup_strategy || 'auto',
            backup.retention_days || 30,
            'backed_up'
          ]);
        }
      }

      console.log('OSD备份数据保存完成');
      return backupData.length;
    });
  }

  /**
   * 获取OSD备份数据
   * @param {number} planId - 计划ID
   */
  async getOsdBackupData(planId) {
    const sql = `
      SELECT id, plan_id, channel_id, channel_name, site_id, site_name,
             room_id, room_name, original_osd_content, backup_time,
             backup_strategy, retention_days, status, restore_time
      FROM batch_osd_backup_data
      WHERE plan_id = ? AND status IN ('backed_up', 'restore_failed')
      ORDER BY backup_time DESC
    `;

    const results = await query(sql, [planId]);
    console.log('获取OSD备份数据，计划ID:', planId, '数量:', results.length);
    return results;
  }

  /**
   * 更新备份恢复状态
   * @param {number} backupId - 备份ID
   * @param {string} status - 状态
   */
  async updateBackupRestoreStatus(backupId, status) {
    const sql = `
      UPDATE batch_osd_backup_data
      SET status = ?, restore_time = CASE WHEN ? = 'restored' THEN CURRENT_TIMESTAMP ELSE restore_time END
      WHERE id = ?
    `;

    const result = await query(sql, [status, status, backupId]);
    return result.affectedRows > 0;
  }

  /**
   * 清理过期的备份数据
   * @param {number} planId - 计划ID（可选）
   */
  async cleanupExpiredBackups(planId = null) {
    let sql = `
      DELETE FROM batch_osd_backup_data
      WHERE retention_days > 0
        AND backup_time < DATE_SUB(NOW(), INTERVAL retention_days DAY)
    `;
    const params = [];

    if (planId) {
      sql += ' AND plan_id = ?';
      params.push(planId);
    }

    const result = await query(sql, params);
    console.log('清理过期备份数据，删除数量:', result.affectedRows);
    return result.affectedRows;
  }

  /**
   * 获取计划的备份统计信息
   * @param {number} planId - 计划ID
   */
  async getBackupStats(planId) {
    const sql = `
      SELECT
        COUNT(*) as total_backups,
        SUM(CASE WHEN status = 'backed_up' THEN 1 ELSE 0 END) as available_backups,
        SUM(CASE WHEN status = 'restored' THEN 1 ELSE 0 END) as restored_backups,
        SUM(CASE WHEN status = 'restore_failed' THEN 1 ELSE 0 END) as failed_restores,
        MIN(backup_time) as earliest_backup,
        MAX(backup_time) as latest_backup
      FROM batch_osd_backup_data
      WHERE plan_id = ?
    `;

    const results = await query(sql, [planId]);
    return results[0] || null;
  }

  /**
   * 获取考点的视频通道绑定关系
   * @param {number} planId - 计划ID
   * @param {string} siteId - 考点ID
   */
  async getSiteRoomChannelBindings(planId, siteId) {
    const sql = `
      SELECT 
        b.id, b.plan_id, b.room_id, b.channel_id, b.binding_type, b.binding_order,
        r.site_id, r.room_name, r.room_number, r.room_type,
        CONCAT('通道', b.channel_id) as channel_name,
        CONCAT(r.room_name, ' - OSD内容') as osd_content,
        b.create_time as binding_time
      FROM batch_osd_room_channel_bindings b
      LEFT JOIN batch_osd_generated_rooms r ON b.room_id = r.room_id AND b.plan_id = r.plan_id
      WHERE b.plan_id = ? AND r.site_id = ?
      ORDER BY r.room_number ASC
    `;
    
    const results = await query(sql, [planId, siteId]);
    console.log(`查询考点 ${siteId} 的绑定关系，找到 ${results.length} 条记录`);
    return results;
  }

  /**
   * 保存OSD备份数据
   * @param {Object} backupData - 备份数据
   */
  async saveOsdBackupData(backupData) {
    const sql = `
      INSERT INTO batch_osd_backup_data
      (plan_id, channel_id, channel_name, site_id, site_name, room_id, room_name,
       original_osd_content, backup_strategy, retention_days, status)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'backed_up')
    `;

    const result = await query(sql, [
      backupData.plan_id,
      backupData.channel_id,
      backupData.channel_name,
      backupData.site_id,
      backupData.site_name,
      backupData.room_id,
      backupData.room_name,
      backupData.original_osd_content,
      backupData.backup_strategy,
      backupData.retention_days
    ]);

    return result.insertId;
  }

  /**
   * 获取计划的OSD备份数据
   * @param {number} planId - 计划ID
   */
  async getOsdBackupData(planId) {
    const sql = `
      SELECT id, plan_id, channel_id, channel_name, site_id, site_name,
             room_id, room_name, original_osd_content, backup_time,
             backup_strategy, retention_days, status, restore_time
      FROM batch_osd_backup_data
      WHERE plan_id = ? AND status IN ('backed_up', 'restore_failed')
      ORDER BY backup_time DESC
    `;
    return await query(sql, [planId]);
  }

  /**
   * 获取计划的OSD备份数据（包含恢复结果）
   * @param {number} planId - 计划ID
   */
  async getOsdBackupDataWithRestoreResults(planId) {
    const sql = `
      SELECT b.id, b.plan_id, b.channel_id, b.channel_name, b.site_id, b.site_name,
             b.room_id, b.room_name, b.original_osd_content, b.backup_time,
             b.backup_strategy, b.retention_days, b.status, b.restore_time,
             CASE
               WHEN b.status = 'restored' THEN '恢复成功'
               WHEN b.status = 'restore_failed' THEN '恢复失败'
               WHEN b.status = 'backed_up' THEN '已备份'
               ELSE '未知状态'
             END as restore_status_text
      FROM batch_osd_backup_data b
      WHERE b.plan_id = ?
      ORDER BY b.site_name, b.room_name, b.backup_time DESC
    `;
    return await query(sql, [planId]);
  }

  /**
   * 更新备份数据的恢复状态
   * @param {number} backupId - 备份数据ID
   * @param {string} status - 新状态
   */
  async updateBackupRestoreStatus(backupId, status) {
    const sql = `
      UPDATE batch_osd_backup_data
      SET status = ?, restore_time = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    const result = await query(sql, [status, backupId]);
    return result.affectedRows > 0;
  }

  /**
   * 获取计划配置信息
   * @param {number} planId - 计划ID
   */
  async getPlanConfigs(planId) {
    const sql = `
      SELECT config_type, config_data
      FROM batch_osd_plan_configs
      WHERE plan_id = ?
    `;
    const results = await query(sql, [planId]);

    // 将结果转换为对象格式，并解析JSON数据
    const configs = {};
    results.forEach(row => {
      try {
        // 如果config_data是字符串，则解析为对象
        if (typeof row.config_data === 'string') {
          configs[row.config_type] = JSON.parse(row.config_data);
        } else {
          configs[row.config_type] = row.config_data;
        }
      } catch (error) {
        console.error(`解析配置数据失败 (${row.config_type}):`, error);
        // 如果解析失败，使用空对象作为默认值
        configs[row.config_type] = {};
      }
    });

    return configs;
  }
}

module.exports = new BatchOsdPersistenceService();
