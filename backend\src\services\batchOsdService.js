// 批量OSD设置服务
const { query, transaction } = require('../config/database');
const batchOsdPersistenceService = require('./batchOsdPersistenceService');

class BatchOsdService {

  // 获取批量OSD计划列表
  async getBatchOsdPlans(params = {}) {
    const { page = 1, size = 20, keyword, status } = params;
    const offset = (page - 1) * size;

    let sql = `
      SELECT p.id, p.name, p.description, p.rule_id, p.target_type, p.target_ids,
             p.execute_mode, p.schedule_time, p.concurrency, p.target_count,
             p.completed_count, p.success_count, p.failed_count, p.status,
             p.start_time, p.end_time, p.created_by,
             p.create_time as created_at, p.create_time as updated_at,
             r.name as rule_name,
             CASE WHEN EXISTS(
               SELECT 1 FROM batch_osd_plan_configs pc
               WHERE pc.plan_id = p.id AND pc.config_type = 'backup_config'
               AND JSON_EXTRACT(CAST(pc.config_data AS JSON), '$.enableBackup') = true
             ) THEN 1 ELSE 0 END as has_backup_enabled,
             CASE WHEN EXISTS(
               SELECT 1 FROM batch_osd_plan_configs pc
               WHERE pc.plan_id = p.id AND pc.config_type = 'backup_config'
               AND JSON_EXTRACT(CAST(pc.config_data AS JSON), '$.enableBackup') = true
             ) AND EXISTS(
               SELECT 1 FROM batch_osd_backup_data bd
               WHERE bd.plan_id = p.id AND bd.status IN ('backed_up', 'restore_failed')
             ) THEN 1 ELSE 0 END as hasBackup
      FROM batch_osd_plans p
      LEFT JOIN osd_rules r ON p.rule_id = r.id
      WHERE 1=1
    `;
    let countSql = 'SELECT COUNT(*) as total FROM batch_osd_plans WHERE 1=1';
    const queryParams = [];
    const countParams = [];

    // 关键词搜索
    if (keyword) {
      sql += ' AND (p.name LIKE ? OR p.description LIKE ?)';
      countSql += ' AND (name LIKE ? OR description LIKE ?)';
      queryParams.push(`%${keyword}%`, `%${keyword}%`);
      countParams.push(`%${keyword}%`, `%${keyword}%`);
    }

    // 状态筛选
    if (status) {
      sql += ' AND p.status = ?';
      countSql += ' AND status = ?';
      queryParams.push(status);
      countParams.push(status);
    }

    sql += ' ORDER BY p.create_time DESC LIMIT ? OFFSET ?';
    queryParams.push(size, offset);

    // 执行查询
    const [records, countResult] = await Promise.all([
      query(sql, queryParams),
      query(countSql, countParams)
    ]);

    // 处理JSON字段并计算考点级别的统计数据
    for (const record of records) {
      if (record.target_ids) {
        try {
          record.target_ids = JSON.parse(record.target_ids);
        } catch (e) {
          record.target_ids = [];
        }
      }
      // 确保hasBackup字段是布尔值
      record.hasBackup = Boolean(record.hasBackup) || Boolean(record.has_backup_enabled);

      // 计算考点级别的统计数据
      const siteStats = await this.calculateSiteStats(record.id);
      record.target_count = siteStats.totalSites; // 目标考点数量
      record.completed_count = siteStats.completedSites; // 已完成考点数量
      record.success_count = siteStats.successSites; // 成功考点数量
      record.failed_count = siteStats.failedSites; // 失败考点数量
      record.progress = siteStats.totalSites > 0 ? Math.round((siteStats.completedSites / siteStats.totalSites) * 100) : 0;
    }

    const total = countResult[0].total;

    return {
      records,
      total,
      size,
      current: page,
      pages: Math.ceil(total / size)
    };
  }

  // 获取批量OSD计划详情
  async getBatchOsdPlanById(id) {
    const sql = `
      SELECT p.*, r.name as rule_name, r.format as rule_format
      FROM batch_osd_plans p
      LEFT JOIN osd_rules r ON p.rule_id = r.id
      WHERE p.id = ?
    `;
    const results = await query(sql, [id]);
    const plan = results[0] || null;

    if (plan && plan.target_ids) {
      try {
        plan.target_ids = JSON.parse(plan.target_ids);
      } catch (e) {
        plan.target_ids = [];
      }
    }

    return plan;
  }

  // 创建批量OSD计划
  async createBatchOsdPlan(planData) {
    const {
      name, description, rule_id, target_type, target_ids = [],
      execute_mode, schedule_time, concurrency = 3, created_by
    } = planData;

    const sql = `
      INSERT INTO batch_osd_plans
      (name, description, rule_id, target_type, target_ids, execute_mode,
       schedule_time, concurrency, created_by)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const targetIdsJson = JSON.stringify(target_ids || []);
    const result = await query(sql, [
      name || null,
      description || null,
      rule_id,
      target_type || 'all',
      targetIdsJson,
      execute_mode || 'immediate',
      schedule_time || null,
      concurrency || 3,
      created_by || 'system'
    ]);

    return result.insertId;
  }

  // 更新批量OSD计划
  async updateBatchOsdPlan(id, planData) {
    const {
      name, description, rule_id, target_type, target_ids,
      execute_mode, schedule_time, concurrency, updated_by
    } = planData;

    const sql = `
      UPDATE batch_osd_plans
      SET name = ?, description = ?, rule_id = ?, target_type = ?, target_ids = ?,
          execute_mode = ?, schedule_time = ?, concurrency = ?,
          update_time = CURRENT_TIMESTAMP
      WHERE id = ?
    `;

    const targetIdsJson = JSON.stringify(target_ids || []);
    const result = await query(sql, [
      name || null,
      description || null,
      rule_id,
      target_type || 'all',
      targetIdsJson,
      execute_mode || 'immediate',
      schedule_time || null,
      concurrency || 3,
      id
    ]);

    return result.affectedRows > 0;
  }

  // 删除批量OSD计划
  async deleteBatchOsdPlan(id) {
    return await transaction(async (connection) => {
      // 删除执行日志
      await connection.execute('DELETE FROM batch_osd_execution_logs WHERE plan_id = ?', [id]);

      // 删除计划
      const [result] = await connection.execute('DELETE FROM batch_osd_plans WHERE id = ?', [id]);
      return result.affectedRows > 0;
    });
  }

  // 批量删除计划
  async batchDeleteBatchOsdPlans(ids) {
    return await transaction(async (connection) => {
      // 删除执行日志
      const logSql = `DELETE FROM batch_osd_execution_logs WHERE plan_id IN (${ids.map(() => '?').join(',')})`;
      await connection.execute(logSql, ids);

      // 删除计划
      const planSql = `DELETE FROM batch_osd_plans WHERE id IN (${ids.map(() => '?').join(',')})`;
      const [result] = await connection.execute(planSql, ids);
      return result.affectedRows;
    });
  }

  // 执行批量OSD计划
  async executeBatchOsdPlan(id) {
    const sql = `
      UPDATE batch_osd_plans
      SET status = 'running', start_time = CURRENT_TIMESTAMP, update_time = CURRENT_TIMESTAMP
      WHERE id = ? AND status = 'pending'
    `;

    const result = await query(sql, [id]);
    return result.affectedRows > 0;
  }

  // 暂停批量OSD计划
  async pauseBatchOsdPlan(id) {
    const sql = `
      UPDATE batch_osd_plans
      SET status = 'paused'
      WHERE id = ? AND status = 'running'
    `;

    const result = await query(sql, [id]);
    return result.affectedRows > 0;
  }

  // 取消批量OSD计划
  async cancelBatchOsdPlan(id) {
    const sql = `
      UPDATE batch_osd_plans
      SET status = 'cancelled', end_time = CURRENT_TIMESTAMP
      WHERE id = ? AND status IN ('pending', 'running', 'paused')
    `;

    const result = await query(sql, [id]);
    return result.affectedRows > 0;
  }

  // 更新计划执行统计
  async updatePlanStats(id, stats) {
    const { target_count, completed_count, success_count, failed_count, status, end_time } = stats;

    let sql = `
      UPDATE batch_osd_plans
      SET target_count = ?, completed_count = ?, success_count = ?, failed_count = ?,
          status = ?
    `;
    const params = [target_count, completed_count, success_count, failed_count, status, id];

    if (end_time) {
      sql = sql.replace('status = ?', 'status = ?, end_time = ?');
      params.splice(-1, 0, end_time);
    }

    sql += ' WHERE id = ?';

    const result = await query(sql, params);
    return result.affectedRows > 0;
  }

  // 获取计划执行统计
  async getPlanExecutionStats(planId) {
    const sql = `
      SELECT target_count, completed_count, success_count, failed_count,
             (completed_count - success_count) as pending_count
      FROM batch_osd_plans
      WHERE id = ?
    `;

    const results = await query(sql, [planId]);
    return results[0] || null;
  }

  // ==================== 数据持久化相关方法 ====================

  /**
   * 创建完整的批量OSD计划（包含数据持久化）
   * @param {Object} planData - 计划数据
   * @param {Array} selectedSites - 选定的考点数据
   */
  async createBatchOsdPlanWithPersistence(planData, selectedSites = []) {
    return await transaction(async (connection) => {
      // 1. 创建基础计划
      const planId = await this.createBatchOsdPlan(planData);

      // 2. 保存选定的考点数据
      if (selectedSites.length > 0) {
        await batchOsdPersistenceService.savePlanSites(planId, selectedSites);

        // 3. 为每个考点生成考场编号
        for (const site of selectedSites) {
          const roomCount = site.room_count || 30; // 默认30个考场
          const rooms = await batchOsdPersistenceService.generateAndSaveExamRooms(
            planId,
            site.id || site.site_id,
            roomCount,
            {
              numberFormat: '01',
              prefix: '考场',
              roomType: 'standard',
              capacity: 30,
              roomsPerFloor: 20
            }
          );

          // 4. 创建考点考场绑定关系
          const roomIds = rooms.map(room => room.id).filter(id => id !== undefined);
          if (roomIds.length > 0) {
            const osdTemplate = await this.getOsdTemplateByRuleId(planData.rule_id);
            await batchOsdPersistenceService.createSiteRoomBindings(
              planId,
              site.id || site.site_id,
              roomIds,
              osdTemplate
            );
          }
        }

        // 5. 更新计划的目标总数
        await this.updatePlanTargetCount(planId, selectedSites.length);
      }

      return planId;
    });
  }

  /**
   * 执行批量OSD计划（包含完整的执行流程和数据持久化）
   * @param {number} planId - 计划ID
   */
  async executeBatchOsdPlanWithPersistence(planId) {
    return await transaction(async (connection) => {
      console.log('开始执行带持久化的批量OSD计划，ID:', planId);

      // 1. 获取计划的所有考点和考场数据
      const sites = await batchOsdPersistenceService.getPlanSites(planId);
      console.log('获取到考点数据:', sites.length, '个');

      if (sites.length === 0) {
        throw new Error('计划中没有找到考点数据，无法执行');
      }

      // 2. 计算实际的目标数量（考点数量，不是考场数量）
      const siteCount = sites.length;
      console.log('目标考点数量:', siteCount);

      // 3. 更新计划的目标数量和状态
      await this.updatePlanTargetCount(planId, siteCount);
      await this.executeBatchOsdPlan(planId);
      console.log('计划状态已更新为执行中，目标考点数量:', siteCount);

      // 4. 为每个考点创建执行日志
      for (const site of sites) {
        const logId = await batchOsdPersistenceService.createExecutionLog({
          plan_id: planId,
          site_id: site.site_id,
          site_name: site.site_name,
          execution_type: 'site',
          status: 'pending',
          progress: 0,
          message: '等待执行',
          start_time: new Date(),
          operator: 'system'
        });

        // 5. 异步执行OSD设置（不阻塞响应）
        setImmediate(() => {
          this.simulateOsdExecution(planId, site, logId).then(() => {
            // 执行完成后更新计划状态
            this.updatePlanCompletionStatus(planId);
          }).catch(error => {
            console.error('执行OSD设置失败:', error);
          });
        });
      }

      console.log('批量OSD计划执行启动成功');
      return true;
    });
  }

  /**
   * 模拟OSD执行过程（包含备份功能）
   * @param {number} planId - 计划ID
   * @param {Object} site - 考点信息
   * @param {number} logId - 日志ID
   */
  async simulateOsdExecution(planId, site, logId) {
    try {
      // 检查是否启用了备份功能
      const planDetail = await this.getBatchOsdPlanDetailWithPersistence(planId);
      const backupConfig = planDetail.backup_config || {};
      const enableBackup = backupConfig.enableBackup === true;

      console.log(`开始执行考点 ${site.site_name} 的OSD设置，备份功能: ${enableBackup ? '启用' : '禁用'}`);

      // 更新状态为执行中
      await batchOsdPersistenceService.updateExecutionLog(logId, {
        status: 'running',
        progress: 5,
        message: enableBackup ? '开始备份原始OSD内容...' : '开始执行OSD设置'
      });

      // 获取考点的考场和视频通道绑定关系
      const bindings = await batchOsdPersistenceService.getSiteRoomChannelBindings(planId, site.site_id);
      console.log(`获取到 ${bindings.length} 个视频通道绑定关系`);

      let backupCount = 0;
      let settingCount = 0;

      // 如果启用了备份，先备份所有视频通道的原始OSD
      if (enableBackup) {
        await batchOsdPersistenceService.updateExecutionLog(logId, {
          progress: 10,
          message: '正在备份原始OSD内容...'
        });

        for (const binding of bindings) {
          try {
            // 模拟从摄像机获取当前OSD内容（实际项目中这里会调用真实的摄像机API）
            const originalOsdContent = await this.simulateGetCameraOsd(binding.channel_id);

            // 保存备份数据
            await batchOsdPersistenceService.saveOsdBackupData({
              plan_id: planId,
              channel_id: binding.channel_id,
              channel_name: binding.channel_name || `通道${binding.channel_id}`,
              site_id: site.site_id,
              site_name: site.site_name,
              room_id: binding.room_id,
              room_name: binding.room_number,
              original_osd_content: originalOsdContent,
              backup_strategy: backupConfig.backupStrategy || 'auto',
              retention_days: backupConfig.retentionDays || 30
            });

            backupCount++;
            console.log(`备份通道 ${binding.channel_id} 的OSD内容: ${originalOsdContent}`);
          } catch (error) {
            console.error(`备份通道 ${binding.channel_id} 失败:`, error);
          }
        }

        await batchOsdPersistenceService.updateExecutionLog(logId, {
          progress: 30,
          message: `OSD备份完成，共备份 ${backupCount} 个视频通道`
        });
      }

      // 执行OSD设置
      await batchOsdPersistenceService.updateExecutionLog(logId, {
        progress: 40,
        message: '开始设置新的OSD内容...'
      });

      // 获取计划信息
      const [planInfo] = await query('SELECT * FROM batch_osd_plans WHERE id = ?', [planId]);
      const planConcurrency = planInfo?.concurrency || 3;
      console.log(`🚀 开始OSD设置模拟，并发数: ${planConcurrency}, 总通道数: ${bindings.length}`);

      // 按考场分组进行OSD设置模拟
      const roomGroups = {};
      bindings.forEach(binding => {
        const roomKey = binding.room_id;
        if (!roomGroups[roomKey]) {
          roomGroups[roomKey] = [];
        }
        roomGroups[roomKey].push(binding);
      });

      const rooms = Object.keys(roomGroups);
      console.log(`📋 共需要设置 ${rooms.length} 个考场的OSD`);

      // 按并发数处理考场
      for (let i = 0; i < rooms.length; i += planConcurrency) {
        const roomBatch = rooms.slice(i, i + planConcurrency);
        const batchNumber = Math.floor(i / planConcurrency) + 1;
        const totalBatches = Math.ceil(rooms.length / planConcurrency);

        console.log(`📦 处理第 ${batchNumber}/${totalBatches} 批考场，共 ${roomBatch.length} 个考场`);

        // 并发处理当前批次的考场
        await Promise.all(roomBatch.map(async (roomId) => {
          const roomBindings = roomGroups[roomId];
          const firstBinding = roomBindings[0];

          console.log(`🏫 开始设置考场: ${firstBinding.site_name || '未知考点'} - ${firstBinding.room_name || roomId}`);

          // 更新该考场所有通道状态为执行中
          const bindingIds = roomBindings.map(b => b.id);
          await query(
            `UPDATE batch_osd_room_channel_bindings SET status='running', start_time=NOW() WHERE id IN (${bindingIds.map(() => '?').join(',')})`,
            bindingIds
          );

          // 为该考场的所有通道逐个设置，每个通道都有独立的设置时间
          for (const binding of roomBindings) {
            const startTime = new Date();

            console.log(`🔧 开始设置通道: ${firstBinding.site_name || '未知考点'} - ${firstBinding.room_name || roomId} - 通道${binding.channel_id}`);

            // 每个通道独立的设置时间（1-10秒）
            const channelExecutionTime = Math.random() * 9000 + 1000;
            await new Promise(resolve => setTimeout(resolve, channelExecutionTime));

            try {
              // 随机通道设置结果（80%成功率）
              const channelSuccess = Math.random() > 0.2;
              const status = channelSuccess ? 'success' : 'failed';

              // 随机生成失败原因
              const failureReasons = [
                '网络连接超时',
                '设备响应异常',
                '认证失败',
                '设备忙碌',
                '参数错误',
                '设备离线',
                '权限不足',
                '设备故障'
              ];

              let message;
              if (channelSuccess) {
                // 获取OSD规则内容
                const [osdRule] = await query('SELECT * FROM osd_rules WHERE id = ?', [planInfo.rule_id]);
                let newOsdContent;
                if (osdRule && osdRule.content) {
                  newOsdContent = osdRule.content.replace(/\{考点名称\}/g, firstBinding.site_name || '考点')
                                                 .replace(/\{考场名称\}/g, firstBinding.room_name || roomId)
                                                 .replace(/\{考试名称\}/g, '2026年研究生考试');
                } else {
                  newOsdContent = `2026年研究生考试-${firstBinding.site_name || '考点'}-${firstBinding.room_name || roomId}`;
                }

                // 实际将OSD内容写入数据库
                try {
                  // 先备份原始OSD内容
                  await query(`
                    INSERT INTO channel_osd_content
                    (channel_id, site_id, site_name, room_id, room_name, original_osd, current_osd, backup_osd, plan_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE
                    backup_osd = current_osd,
                    current_osd = VALUES(current_osd),
                    plan_id = VALUES(plan_id),
                    last_update_time = NOW()
                  `, [
                    binding.channel_id,
                    firstBinding.site_id || binding.room_id.split('_')[0],
                    firstBinding.site_name || '未知考点',
                    binding.room_id,
                    firstBinding.room_name || roomId,
                    `原始OSD-${binding.channel_id}`,
                    newOsdContent,
                    `备份OSD-${binding.channel_id}`,
                    planId
                  ]);

                  message = `OSD设置成功: ${newOsdContent}`;
                  console.log(`✅ 通道${binding.channel_id} OSD设置成功: ${newOsdContent}`);
                } catch (osdError) {
                  console.error(`OSD内容写入失败:`, osdError);
                  message = `OSD设置失败: 数据库写入错误`;
                }
              } else {
                message = `设置失败：${failureReasons[Math.floor(Math.random() * failureReasons.length)]}`;
                console.log(`❌ 通道${binding.channel_id} OSD设置失败: ${message}`);
              }

              // 计算执行耗时
              const endTime = new Date();
              const duration = endTime.getTime() - startTime.getTime();

              // 更新执行结果
              await query(
                `UPDATE batch_osd_room_channel_bindings SET status=?, message=?, end_time=NOW(), duration=?, site_name=?, room_name=? WHERE id=?`,
                [status, message, duration, firstBinding.site_name || '未知考点', firstBinding.room_name || roomId, binding.id]
              );

            } catch (error) {
              const endTime = new Date();
              const duration = endTime.getTime() - startTime.getTime();
              const errorMessage = `执行异常: ${error.message}`;

              console.error(`❌ 通道${binding.channel_id} 执行异常:`, error);

              await query(
                `UPDATE batch_osd_room_channel_bindings SET status='failed', message=?, end_time=NOW(), duration=?, site_name=?, room_name=? WHERE id=?`,
                [errorMessage, duration, firstBinding.site_name || '未知考点', firstBinding.room_name || roomId, binding.id]
              );
            }
          }

          // 统计该考场结果
          const roomResults = await query(
            `SELECT status FROM batch_osd_room_channel_bindings WHERE plan_id = ? AND room_id = ?`,
            [planId, roomId]
          );

          const successCount = roomResults.filter(r => r.status === 'success').length;
          const failedCount = roomResults.filter(r => r.status === 'failed').length;

          console.log(`🏫 考场 ${firstBinding.room_name || roomId} 设置完成: 成功 ${successCount}, 失败 ${failedCount}`);

          // 检查该考点的所有通道是否都已完成
          await checkAndUpdateSiteCompletion(planId, firstBinding.site_id || binding.room_id.split('_')[0]);

          // 更新计划统计
          await this.updatePlanStats(planId);
        }));

        // 批次间稍作停顿
        if (i + planConcurrency < rooms.length) {
          console.log(`⏳ 等待下一批考场...`);
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      console.log(`🎉 计划 ${planId} 的OSD设置模拟完成`);

      // 更新执行进度
      await batchOsdPersistenceService.updateExecutionLog(logId, {
        progress: 80,
        message: 'OSD设置执行完成，正在统计结果...'
      });

      // 模拟执行结果（90%成功率）
      const isSuccess = settingCount > bindings.length * 0.8;

      if (isSuccess) {
        await batchOsdPersistenceService.updateExecutionLog(logId, {
          status: 'success',
          progress: 100,
          message: `OSD设置完成，${enableBackup ? `备份 ${backupCount} 个，` : ''}设置 ${settingCount} 个视频通道`,
          end_time: new Date(),
          duration: 5000 + Math.floor(Math.random() * 3000)
        });

        // 更新考点状态
        await batchOsdPersistenceService.updateSiteStatus(planId, site.site_id, 'completed');
      } else {
        await batchOsdPersistenceService.updateExecutionLog(logId, {
          status: 'failed',
          progress: 90,
          message: `执行部分失败，${enableBackup ? `备份 ${backupCount} 个，` : ''}设置 ${settingCount}/${bindings.length} 个视频通道`,
          error_code: 'PARTIAL_FAILURE',
          error_message: '部分视频通道设置失败，请检查网络连接和设备状态',
          end_time: new Date(),
          duration: 3000 + Math.floor(Math.random() * 2000)
        });

        // 更新考点状态
        await batchOsdPersistenceService.updateSiteStatus(planId, site.site_id, 'failed');
      }

    } catch (error) {
      console.error('执行OSD设置异常:', error);
      await batchOsdPersistenceService.updateExecutionLog(logId, {
        status: 'failed',
        progress: 0,
        message: '执行异常',
        error_code: 'SYSTEM_ERROR',
        error_message: error.message,
        end_time: new Date()
      });

      await batchOsdPersistenceService.updateSiteStatus(planId, site.site_id, 'failed');
    }
  }

  /**
   * 获取OSD模板根据规则ID
   * @param {number} ruleId - 规则ID
   */
  async getOsdTemplateByRuleId(ruleId) {
    const sql = 'SELECT format FROM osd_rules WHERE id = ?';
    const results = await query(sql, [ruleId]);
    return results[0]?.format || '[考点名称] 考场[考场号]';
  }

  /**
   * 更新计划目标总数（考点数量）
   * @param {number} planId - 计划ID
   * @param {number} targetCount - 目标考点数量
   */
  async updatePlanTargetCount(planId, targetCount) {
    console.log(`计划 ${planId} 目标考点数量更新为: ${targetCount}`);

    const sql = 'UPDATE batch_osd_plans SET target_count = ? WHERE id = ?';
    const result = await query(sql, [targetCount, planId]);
    return result.affectedRows > 0;
  }

  /**
   * 模拟从摄像机获取当前OSD内容
   * @param {string} channelId - 视频通道ID
   * @returns {string} 当前OSD内容
   */
  async simulateGetCameraOsd(channelId) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));

    // 模拟获取摄像机的原始OSD内容（物理安装位置）
    const locations = [
      '教学楼A-101教室-前门',
      '教学楼A-101教室-后门',
      '教学楼A-101教室-讲台',
      '教学楼A-102教室-前门',
      '教学楼A-102教室-后门',
      '教学楼B-201教室-前门',
      '教学楼B-201教室-讲台',
      '实验楼C-301实验室-入口',
      '图书馆-阅览室1-北侧',
      '图书馆-阅览室1-南侧'
    ];

    // 根据通道ID生成一个固定的原始位置（确保同一通道每次获取的内容一致）
    const index = parseInt(channelId.replace(/\D/g, '')) % locations.length;
    const originalLocation = locations[index];

    console.log(`模拟获取通道 ${channelId} 的原始OSD: ${originalLocation}`);
    return originalLocation;
  }

  /**
   * 模拟设置摄像机OSD内容
   * @param {string} channelId - 视频通道ID
   * @param {string} osdContent - 要设置的OSD内容
   * @returns {boolean} 设置是否成功
   */
  async simulateSetCameraOsd(channelId, osdContent) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 300));

    // 模拟设置成功率（95%）
    const success = Math.random() > 0.05;

    console.log(`模拟设置通道 ${channelId} 的OSD: ${osdContent} - ${success ? '成功' : '失败'}`);
    return success;
  }

  /**
   * 模拟OSD恢复操作
   * @param {Object} backup - 备份数据
   * @returns {boolean} 恢复是否成功
   */
  async simulateOsdRestore(backup) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 500));

    // 模拟恢复成功率（92%）
    const success = Math.random() > 0.08;

    console.log(`模拟恢复通道 ${backup.channel_id} 的OSD: ${backup.original_osd_content} - ${success ? '成功' : '失败'}`);
    return success;
  }

  /**
   * 更新计划完成状态
   * @param {number} planId - 计划ID
   */
  async updatePlanCompletionStatus(planId) {
    // 统计执行结果 - 按执行日志统计（每个考点一个日志）
    const statsSQL = `
      SELECT
        COUNT(*) as total_count,
        SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success_count,
        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_count,
        SUM(CASE WHEN status IN ('success', 'failed') THEN 1 ELSE 0 END) as completed_count
      FROM batch_osd_execution_logs
      WHERE plan_id = ?
    `;

    const [stats] = await query(statsSQL, [planId]);

    // 确保数据类型一致
    const totalCount = Number(stats.total_count);
    const successCount = Number(stats.success_count);
    const failedCount = Number(stats.failed_count);
    const completedCount = Number(stats.completed_count);

    console.log(`计划 ${planId} 执行统计: 总数=${totalCount}, 成功=${successCount}, 失败=${failedCount}, 已完成=${completedCount}`);

    // 判断是否所有任务都已完成
    const allCompleted = completedCount === totalCount && totalCount > 0;
    const hasCompleted = completedCount > 0;

    console.log(`计划 ${planId} 状态判断: 全部完成=${allCompleted}, 有完成=${hasCompleted}`);

    // 更新计划统计信息
    const updateSQL = `
      UPDATE batch_osd_plans
      SET completed_count = ?, success_count = ?, failed_count = ?,
          status = CASE
            WHEN ? THEN 'completed'
            WHEN ? THEN 'running'
            ELSE 'pending'
          END,
          end_time = CASE
            WHEN ? THEN CURRENT_TIMESTAMP
            ELSE end_time
          END
      WHERE id = ?
    `;

    const result = await query(updateSQL, [
      completedCount,
      successCount,
      failedCount,
      allCompleted, // 全部完成时设为completed
      hasCompleted, // 有完成时设为running
      allCompleted, // 全部完成时设置结束时间
      planId
    ]);

    console.log(`计划 ${planId} 状态更新: ${allCompleted ? 'completed' : hasCompleted ? 'running' : 'pending'}`);
    return result.affectedRows > 0;
  }

  /**
   * 获取计划详细信息（包含持久化数据）
   * @param {number} planId - 计划ID
   */
  async getBatchOsdPlanDetailWithPersistence(planId) {
    // 获取基础计划信息
    const plan = await this.getBatchOsdPlanById(planId);
    if (!plan) {
      return null;
    }

    // 获取选定的考点
    const sites = await batchOsdPersistenceService.getPlanSites(planId);

    // 获取执行日志
    const logs = await batchOsdPersistenceService.getPlanExecutionLogs(planId, { limit: 100 });

    // 获取计划配置（包括备份配置）
    const configs = await batchOsdPersistenceService.getPlanConfigs(planId);
    // 备份配置已经是解析后的对象，不需要再次JSON.parse
    const backupConfig = configs?.backup_config || { enableBackup: false };

    // 计算实际的任务数量
    // 注意：数据库中存储的是room_count字段，但可能为0，需要从生成的考场数据中获取
    let roomCount = sites.reduce((sum, site) => sum + (site.room_count || 0), 0);

    // 如果考点数据中的room_count为0，则从生成的考场数据中获取
    if (roomCount === 0) {
      const generatedRooms = await query(`
        SELECT COUNT(*) as count
        FROM batch_osd_generated_rooms
        WHERE plan_id = ?
      `, [planId]);
      roomCount = generatedRooms[0].count || 0;
    }

    // 使用绑定关系数量作为实际目标数量
    const bindingsCount = await query(`
      SELECT COUNT(*) as count
      FROM batch_osd_room_channel_bindings
      WHERE plan_id = ?
    `, [planId]);
    const actualTargetCount = bindingsCount[0].count || 0;

    return {
      ...plan,
      sites: sites,
      execution_logs: logs,
      site_count: sites.length,
      room_count: roomCount,
      // 使用实际的考场数量作为目标数量
      target_count: actualTargetCount,
      // 添加备份配置
      backup_config: backupConfig,
      hasBackup: Boolean(backupConfig && backupConfig.enableBackup)
    };
  }

  /**
   * 创建完整的批量OSD计划（包含所有相关数据）
   * @param {Object} fullPlanData - 完整的计划数据
   */
  async createBatchOsdPlanWithFullData(fullPlanData) {
    return await transaction(async (connection) => {
      console.log('开始创建完整的批量OSD计划');

      // 1. 创建基础计划
      const {
        name, description, rule_id, target_type, target_ids,
        execute_mode, schedule_time, concurrency, created_by,
        selected_sites, generated_rooms, room_channel_bindings,
        osd_config, room_config, schedule_config
      } = fullPlanData;

      const planId = await this.createBatchOsdPlan({
        name, description, rule_id, target_type, target_ids,
        execute_mode, schedule_time, concurrency, created_by
      });

      console.log('基础计划创建成功，ID:', planId);

      // 2. 保存选定的考点数据
      if (selected_sites && selected_sites.length > 0) {
        console.log('保存选定的考点数据，数量:', selected_sites.length);
        await batchOsdPersistenceService.savePlanSites(planId, selected_sites);
      }

      // 3. 保存生成的考场数据
      if (generated_rooms && generated_rooms.length > 0) {
        console.log('保存生成的考场数据，数量:', generated_rooms.length);
        await batchOsdPersistenceService.saveGeneratedRooms(planId, generated_rooms);
      }

      // 4. 保存考场与视频通道的绑定关系
      if (room_channel_bindings && room_channel_bindings.length > 0) {
        console.log('保存绑定关系，数量:', room_channel_bindings.length);
        await batchOsdPersistenceService.saveRoomChannelBindings(planId, room_channel_bindings);
      }

      // 5. 保存配置信息（包括备份配置）
      const { backup_config } = fullPlanData;
      const configData = {
        osd_config: JSON.stringify(osd_config || {}),
        room_config: JSON.stringify(room_config || {}),
        schedule_config: JSON.stringify(schedule_config || {}),
        backup_config: JSON.stringify(backup_config || { enableBackup: false })
      };

      console.log('保存配置信息');
      await batchOsdPersistenceService.savePlanConfigs(planId, configData);

      // 6. 如果是立即执行模式，自动启动执行
      if (execute_mode === 'immediate') {
        console.log('检测到立即执行模式，自动启动执行...');
        try {
          // 异步执行，不阻塞创建响应
          setImmediate(async () => {
            try {
              await this.executeBatchOsdPlanWithPersistence(planId);
              console.log('立即执行计划启动成功，ID:', planId);
            } catch (error) {
              console.error('立即执行计划失败，ID:', planId, '错误:', error);
            }
          });
        } catch (error) {
          console.error('启动立即执行失败:', error);
        }
      }

      console.log('完整计划创建成功，ID:', planId);
      return planId;
    });
  }

  /**
   * 暂停批量OSD计划
   * @param {number} planId - 计划ID
   */
  async pauseBatchOsdPlan(planId) {
    try {
      console.log(`暂停计划 ${planId}`);

      // 更新计划状态为暂停
      const sql = 'UPDATE batch_osd_plans SET status = ? WHERE id = ?';
      const result = await query(sql, ['paused', planId]);

      if (result.affectedRows === 0) {
        throw new Error('计划不存在或已被删除');
      }

      console.log(`计划 ${planId} 已暂停`);
      return { planId, status: 'paused' };
    } catch (error) {
      console.error(`暂停计划 ${planId} 失败:`, error);
      throw error;
    }
  }

  /**
   * 继续执行批量OSD计划
   * @param {number} planId - 计划ID
   */
  async resumeBatchOsdPlan(planId) {
    try {
      console.log(`继续执行计划 ${planId}`);

      // 更新计划状态为运行中
      const sql = 'UPDATE batch_osd_plans SET status = ? WHERE id = ?';
      const result = await query(sql, ['running', planId]);

      if (result.affectedRows === 0) {
        throw new Error('计划不存在或已被删除');
      }

      console.log(`计划 ${planId} 已继续执行`);
      return { planId, status: 'running' };
    } catch (error) {
      console.error(`继续执行计划 ${planId} 失败:`, error);
      throw error;
    }
  },

  /**
   * 取消批量OSD计划
   * @param {number} planId - 计划ID
   */
  async cancelBatchOsdPlan(planId) {
    try {
      console.log(`取消计划 ${planId}`);

      // 更新计划状态为已取消，并设置结束时间
      const sql = 'UPDATE batch_osd_plans SET status = ?, end_time = ? WHERE id = ?';
      const result = await query(sql, ['cancelled', new Date(), planId]);

      if (result.affectedRows === 0) {
        throw new Error('计划不存在或已被删除');
      }

      console.log(`计划 ${planId} 已取消`);
      return { planId, status: 'cancelled', endTime: new Date() };
    } catch (error) {
      console.error(`取消计划 ${planId} 失败:`, error);
      throw error;
    }
  },

  /**
   * 一键恢复OSD备份
   * @param {number} planId - 计划ID
   */
  async restoreOsdBackup(planId) {
    return await transaction(async (connection) => {
      console.log('开始执行OSD备份恢复，计划ID:', planId);

      // 1. 获取计划详细信息
      const planDetail = await this.getBatchOsdPlanDetailWithPersistence(planId);
      if (!planDetail) {
        console.error('计划不存在:', planId);
        return false;
      }

      // 2. 检查是否启用了备份功能
      const backupConfig = planDetail.backup_config || {};
      if (!backupConfig.enableBackup) {
        console.error('计划未启用备份功能:', planId);
        return false;
      }

      // 3. 获取备份数据
      const backupData = await batchOsdPersistenceService.getOsdBackupData(planId);
      if (!backupData || backupData.length === 0) {
        console.error('没有找到备份数据:', planId);
        return false;
      }

      // 4. 创建恢复执行日志
      const restoreLogId = await batchOsdPersistenceService.createExecutionLog({
        plan_id: planId,
        site_id: 'RESTORE_OPERATION',
        site_name: 'OSD备份恢复操作',
        status: 'running',
        progress: 0,
        message: '开始恢复OSD备份',
        execution_type: 'restore',
        start_time: new Date(),
        operator: 'system'
      });

      // 5. 执行恢复操作
      try {
        let successCount = 0;
        let failedCount = 0;

        for (let i = 0; i < backupData.length; i++) {
          const backup = backupData[i];
          const progress = Math.floor(((i + 1) / backupData.length) * 90);

          // 更新进度
          await batchOsdPersistenceService.updateExecutionLog(restoreLogId, {
            progress: progress,
            message: `正在恢复 ${backup.channel_name || backup.channel_id} 的OSD...`
          });

          // 模拟恢复操作（实际项目中这里会调用真实的OSD恢复接口）
          const restoreSuccess = await this.simulateOsdRestore(backup);

          if (restoreSuccess) {
            successCount++;
            // 更新备份数据状态
            await batchOsdPersistenceService.updateBackupRestoreStatus(backup.id, 'restored');
          } else {
            failedCount++;
            await batchOsdPersistenceService.updateBackupRestoreStatus(backup.id, 'restore_failed');
          }

          // 模拟网络延迟
          await new Promise(resolve => setTimeout(resolve, 100));
        }

        // 6. 更新恢复日志
        const isSuccess = failedCount === 0;
        await batchOsdPersistenceService.updateExecutionLog(restoreLogId, {
          status: isSuccess ? 'success' : 'partial_success',
          progress: 100,
          message: `恢复完成，成功: ${successCount}, 失败: ${failedCount}`,
          end_time: new Date(),
          duration: backupData.length * 100 + 1000
        });

        console.log(`OSD备份恢复完成，成功: ${successCount}, 失败: ${failedCount}`);
        return true;

      } catch (error) {
        console.error('OSD备份恢复失败:', error);

        // 更新失败日志
        await batchOsdPersistenceService.updateExecutionLog(restoreLogId, {
          status: 'failed',
          progress: 0,
          message: '恢复失败',
          error_code: 'RESTORE_ERROR',
          error_message: error.message,
          end_time: new Date()
        });

        return false;
      }
    });
  },

  /**
   * 模拟OSD恢复操作
   * @param {Object} backup - 备份数据
   */
  async simulateOsdRestore(backup) {
    try {
      // 模拟网络请求延迟
      await new Promise(resolve => setTimeout(resolve, 50 + Math.random() * 100));

      // 模拟95%的成功率
      return Math.random() > 0.05;
    } catch (error) {
      console.error('模拟OSD恢复失败:', error);
      return false;
    }
  },

  // 获取计划的考点数据
  async getPlanSites(planId) {
    try {
      // 从绑定数据中提取考点信息
      const bindings = await query(`
        SELECT DISTINCT channel_id
        FROM batch_osd_room_channel_bindings
        WHERE plan_id = ?
      `, [planId]);

      // 从channel_id中提取site_id并去重
      const siteMap = new Map();
      bindings.forEach(binding => {
        if (binding.channel_id) {
          const channelParts = binding.channel_id.split('_');
          if (channelParts.length >= 2) {
            const siteId = channelParts[1];
            if (!siteMap.has(siteId)) {
              siteMap.set(siteId, {
                site_id: siteId,
                site_name: `考点${siteId}`
              });
            }
          }
        }
      });

      const sites = Array.from(siteMap.values());
      console.log(`获取到计划${planId}的考点数据:`, sites.length, '个');
      return sites;
    } catch (error) {
      console.error('获取计划考点数据失败:', error);
      return [];
    }
  },

  // 计算考点级别的统计数据
  async calculateSiteStats(planId) {
    try {
      // 获取考点数据和通道绑定数据
      const sites = await this.getPlanSites(planId);
      const bindings = await this.getPlanChannelBindings(planId);

      // 按考点统计执行情况
      const siteStats = new Map();

      // 初始化考点统计
      sites.forEach(site => {
        siteStats.set(site.site_id, {
          site_id: site.site_id,
          site_name: site.site_name,
          total_channels: 0,
          completed_channels: 0,
          success_channels: 0,
          failed_channels: 0,
          status: 'pending'
        });
      });

      // 统计每个考点的通道执行情况
      bindings.forEach(binding => {
        const siteId = binding.site_id || 'unknown';
        if (siteStats.has(siteId)) {
          const stat = siteStats.get(siteId);
          stat.total_channels++;

          if (['completed', 'success', 'failed'].includes(binding.status)) {
            stat.completed_channels++;
          }
          if (binding.status === 'completed' || binding.status === 'success') {
            stat.success_channels++;
          }
          if (binding.status === 'failed') {
            stat.failed_channels++;
          }
        }
      });

      // 计算考点级别的统计
      let completedSites = 0;
      let successSites = 0;
      let failedSites = 0;

      siteStats.forEach(stat => {
        if (stat.completed_channels === stat.total_channels && stat.total_channels > 0) {
          completedSites++;
          if (stat.failed_channels === 0) {
            stat.status = 'success';
            successSites++;
          } else {
            stat.status = 'failed';
            failedSites++;
          }
        } else if (stat.completed_channels > 0) {
          stat.status = 'running';
        }
      });

      return {
        totalSites: sites.length,
        completedSites,
        successSites,
        failedSites,
        pendingSites: sites.length - completedSites
      };
    } catch (error) {
      console.error('计算考点统计数据失败:', error);
      return {
        totalSites: 0,
        completedSites: 0,
        successSites: 0,
        failedSites: 0,
        pendingSites: 0
      };
    }
  },

  // 获取计划的通道绑定数据
  async getPlanChannelBindings(planId) {
    try {
      // 只查询确定存在的字段
      const bindings = await query(`
        SELECT
          id, plan_id, channel_id
        FROM batch_osd_room_channel_bindings
        WHERE plan_id = ?
        ORDER BY id DESC
      `, [planId]);

      console.log(`获取到计划${planId}的绑定数据:`, bindings.length, '条');

      // 为每个绑定添加默认字段，并从channel_id中提取site_id
      return bindings.map((binding, index) => {
        // 从channel_id中提取site_id (假设格式为 channel_siteId_roomId)
        const channelParts = binding.channel_id ? binding.channel_id.split('_') : [];
        const siteId = channelParts.length >= 2 ? channelParts[1] : '';

        return {
          ...binding,
          channel_name: binding.channel_id || `通道${index + 1}`,
          site_id: siteId,
          site_name: '未知考点',
          room_id: channelParts.length >= 3 ? channelParts[2] : '',
          room_name: `考场${channelParts.length >= 3 ? channelParts[2] : index + 1}`,
          status: 'completed', // 假设已完成
          message: '设置完成',
          start_time: new Date().toISOString(),
          end_time: new Date().toISOString(),
          created_at: new Date().toISOString()
        };
      });
    } catch (error) {
      console.error('获取计划通道绑定数据失败:', error);
      return [];
    }
  },

  // 获取考点的通道执行详情
  async getSiteChannelDetails(planId, siteId) {
    try {
      console.log(`获取考点 ${siteId} 的通道执行详情，计划ID: ${planId}`);

      // 获取该考点的所有通道绑定信息（通过room_id匹配考点）
      const channels = await query(`
        SELECT
          b.id,
          b.channel_id,
          b.room_id,
          b.site_name,
          b.room_name,
          b.status,
          b.message,
          b.start_time,
          b.end_time,
          b.duration
        FROM batch_osd_room_channel_bindings b
        WHERE b.plan_id = ? AND b.room_id LIKE ?
        ORDER BY b.room_id, b.channel_id
      `, [planId, `${siteId}_%`]);

      console.log(`找到 ${channels.length} 个通道绑定记录`);

      // 统计各种状态的数量
      const stats = {
        total: channels.length,
        pending: channels.filter(c => c.status === 'pending').length,
        running: channels.filter(c => c.status === 'running').length,
        success: channels.filter(c => c.status === 'success').length,
        failed: channels.filter(c => c.status === 'failed').length
      };

      // 格式化通道数据
      const formattedChannels = channels.map(channel => ({
        id: channel.id,
        channel_id: channel.channel_id,
        room_id: channel.room_id,
        room_name: channel.room_name || `考场${channel.room_id}`,
        site_name: channel.site_name,
        status: channel.status || 'pending',
        message: channel.message || '等待执行',
        start_time: channel.start_time,
        end_time: channel.end_time,
        duration: channel.duration
      }));

      console.log(`考点 ${siteId} 通道状态统计:`, stats);

      return {
        siteId,
        siteName: channels[0]?.site_name || '未知考点',
        stats,
        channels: formattedChannels
      };

    } catch (error) {
      console.error('获取考点通道详情失败:', error);
      throw error;
    }
  },

  // 获取计划的目标考点列表
  async getPlanTargetSites(planId) {
    try {
      console.log(`获取计划 ${planId} 的目标考点列表`);

      // 获取计划信息
      const [planInfo] = await query('SELECT * FROM batch_osd_plans WHERE id = ?', [planId]);
      if (!planInfo) {
        throw new Error('计划不存在');
      }

      let targetSites = [];

      if (planInfo.target_type === 'custom') {
        // 自定义选择，从 batch_osd_plan_sites 表获取
        targetSites = await query(`
          SELECT
            ps.site_id,
            ps.site_name,
            '所属区域' as parent_name,
            '地址信息' as address,
            0 as room_count,
            NOW() as created_at
          FROM batch_osd_plan_sites ps
          WHERE ps.plan_id = ?
          ORDER BY ps.site_name
        `, [planId]);
      } else {
        // 其他类型（全部、按省份、按地市、按区县），从绑定表获取实际的考点
        targetSites = await query(`
          SELECT DISTINCT
            b.site_name,
            SUBSTRING_INDEX(b.room_id, '_', 1) as site_id,
            '系统生成' as parent_name,
            '地址信息' as address,
            COUNT(DISTINCT b.room_id) as room_count,
            MIN(b.created_at) as created_at
          FROM batch_osd_room_channel_bindings b
          WHERE b.plan_id = ?
          GROUP BY SUBSTRING_INDEX(b.room_id, '_', 1), b.site_name
          ORDER BY b.site_name
        `, [planId]);
      }

      console.log(`找到 ${targetSites.length} 个目标考点`);

      return targetSites;

    } catch (error) {
      console.error('获取计划目标考点列表失败:', error);
      throw error;
    }
  },

  // 获取计划的房间通道绑定信息
  async getPlanRoomChannelBindings(planId) {
    try {
      console.log(`获取计划 ${planId} 的房间通道绑定信息`);

      // 从数据库获取房间通道绑定信息
      const bindings = await query(`
        SELECT
          room_id,
          channel_id,
          site_name,
          created_at
        FROM batch_osd_room_channel_bindings
        WHERE plan_id = ?
        ORDER BY room_id, channel_id
      `, [planId]);

      console.log(`获取到计划 ${planId} 的房间通道绑定:`, bindings.length, '个');

      // 转换为前端期望的格式
      const bindingsMap = {};
      bindings.forEach(binding => {
        bindingsMap[binding.room_id] = binding.channel_id;
      });

      return bindingsMap;
    } catch (error) {
      console.error('获取计划房间通道绑定信息失败:', error);
      throw error;
    }
  },

  // 获取计划的OSD设置数据
  async getPlanOsdData(planId) {
    try {
      console.log(`获取计划 ${planId} 的OSD设置数据`);

      // 获取计划基本信息和OSD规则
      const plan = await query(`
        SELECT p.*, r.format as rule_format, r.name as rule_name
        FROM batch_osd_plans p
        LEFT JOIN osd_rules r ON p.rule_id = r.id
        WHERE p.id = ?
      `, [planId]);

      if (plan.length === 0) {
        throw new Error('计划不存在');
      }

      const planInfo = plan[0];

      // 获取房间通道绑定信息（包含原始OSD和目标OSD）
      const osdData = await query(`
        SELECT
          b.room_id,
          b.channel_id,
          b.site_name,
          r.room_number,
          r.site_id,
          s.site_name,
          s.parent_name,
          b.original_osd,
          b.target_osd,
          b.current_osd,
          b.status,
          b.created_at,
          b.updated_at
        FROM batch_osd_room_channel_bindings b
        LEFT JOIN batch_osd_generated_rooms r ON b.room_id = r.id
        LEFT JOIN batch_osd_plan_sites s ON r.site_id = s.site_id AND s.plan_id = b.plan_id
        WHERE b.plan_id = ?
        ORDER BY s.site_name, r.room_number, b.channel_id
      `, [planId]);

      console.log(`获取到计划 ${planId} 的OSD数据:`, osdData.length, '条');

      // 如果数据库中没有OSD数据，生成默认数据
      if (osdData.length === 0) {
        console.log('数据库中没有OSD数据，生成默认数据');
        return await this.generateDefaultOsdData(planId, planInfo);
      }

      return {
        planInfo,
        osdList: osdData
      };
    } catch (error) {
      console.error('获取计划OSD设置数据失败:', error);
      throw error;
    }
  },

  // 生成默认OSD数据（当数据库中没有数据时）
  async generateDefaultOsdData(planId, planInfo) {
    try {
      // 获取计划的考点信息
      const sites = await this.getPlanTargetSites(planId);

      // 获取房间通道绑定
      const bindings = await this.getPlanRoomChannelBindings(planId);

      const osdList = [];

      // 为每个绑定生成OSD数据
      Object.entries(bindings).forEach(([roomId, channelId]) => {
        // 从roomId中提取考点和考场信息
        const roomParts = roomId.split('_');
        const siteId = roomParts[0];
        const roomNumber = roomParts[roomParts.length - 1];

        // 找到对应的考点信息
        const site = sites.find(s => s.site_id === siteId);
        if (!site) return;

        // 生成原始OSD（物理位置标签）
        const originalOsd = this.generatePhysicalLocationOsd(site, roomNumber, channelId);

        // 生成目标OSD（按规则生成）
        const targetOsd = this.generateRuleBasedOsd(site, roomNumber, channelId, planInfo.rule_format);

        osdList.push({
          room_id: roomId,
          channel_id: channelId,
          site_id: siteId,
          site_name: site.site_name,
          parent_name: site.parent_name,
          room_number: roomNumber,
          original_osd: originalOsd,
          target_osd: targetOsd,
          current_osd: originalOsd,
          status: 'pending'
        });
      });

      return {
        planInfo,
        osdList
      };
    } catch (error) {
      console.error('生成默认OSD数据失败:', error);
      throw error;
    }
  },

  // 生成物理位置OSD标签
  generatePhysicalLocationOsd(site, roomNumber, channelId) {
    // 模拟从摄像机获取的物理安装位置信息
    const locations = [
      '前门监控点',
      '后门监控点',
      '讲台监控点',
      '左侧监控点',
      '右侧监控点',
      '中央监控点'
    ];

    // 根据channelId生成固定的位置
    const channelNum = parseInt(channelId.replace(/\D/g, '')) || 1;
    const location = locations[(channelNum - 1) % locations.length];

    return `${site.site_name}-${roomNumber}考场-${location}`;
  },

  // 生成基于规则的OSD标签
  generateRuleBasedOsd(site, roomNumber, channelId, ruleFormat) {
    let format = ruleFormat || '[考点名称] 考场[考场号]';

    // 获取当前时间
    const now = new Date();
    const timeStr = now.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });

    // 替换规则中的占位符
    const replacements = {
      '[考点名称]': site.site_name,
      '[考场号]': roomNumber,
      '[通道号]': channelId.split('_').pop() || '1',
      '[时间]': timeStr,
      '[日期]': timeStr.split(' ')[0],
      '[机构名称]': site.parent_name || '教育局'
    };

    let result = format;
    Object.entries(replacements).forEach(([placeholder, value]) => {
      result = result.replace(new RegExp(placeholder, 'g'), value);
    });

    return result;
  }
}

// 工具函数
function sleepRandom(min = 1000, max = 10000) {
  const ms = Math.floor(Math.random() * (max - min + 1)) + min;
  console.log(`⏱️  模拟执行时间: ${Math.round(ms/1000)}秒`);
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 检查并更新考点完成状态
async function checkAndUpdateSiteCompletion(planId, siteId) {
  try {
    // 获取该考点的所有通道状态（通过room_id匹配考点）
    const siteChannels = await query(
      `SELECT status FROM batch_osd_room_channel_bindings WHERE plan_id = ? AND room_id LIKE ?`,
      [planId, `${siteId}_%`]
    );

    if (siteChannels.length === 0) {
      console.log(`⚠️  考点 ${siteId} 没有找到相关通道`);
      return;
    }

    // 统计各种状态的通道数量
    const totalChannels = siteChannels.length;
    const completedChannels = siteChannels.filter(ch => ch.status === 'success' || ch.status === 'failed').length;
    const successChannels = siteChannels.filter(ch => ch.status === 'success').length;
    const failedChannels = siteChannels.filter(ch => ch.status === 'failed').length;
    const runningChannels = siteChannels.filter(ch => ch.status === 'running').length;
    const pendingChannels = siteChannels.filter(ch => ch.status === 'pending').length;

    console.log(`📊 考点 ${siteId} 通道状态: 总数=${totalChannels}, 已完成=${completedChannels}, 成功=${successChannels}, 失败=${failedChannels}, 执行中=${runningChannels}, 待执行=${pendingChannels}`);

    // 判断考点状态 - 只要所有通道都执行完成（无论成功失败），考点就是完成状态
    let siteStatus = 'running';
    let progress = Math.round((completedChannels / totalChannels) * 100);

    if (completedChannels === totalChannels) {
      // 所有通道都已完成，考点状态为完成
      siteStatus = 'completed';
      progress = 100;
      console.log(`✅ 考点 ${siteId} 所有通道设置完成，状态: completed, 成功:${successChannels}, 失败:${failedChannels}`);
    } else if (runningChannels > 0) {
      siteStatus = 'running';
      console.log(`⚡ 考点 ${siteId} 仍有通道在执行中，进度: ${progress}%`);
    } else {
      siteStatus = 'pending';
      progress = 0;
      console.log(`⏳ 考点 ${siteId} 仍有通道待执行`);
    }

    // 更新考点执行日志状态
    await query(
      `UPDATE batch_osd_execution_logs SET
       status = ?,
       progress = ?,
       message = ?,
       end_time = CASE WHEN ? = 'completed' THEN NOW() ELSE end_time END,
       updated_at = NOW()
       WHERE plan_id = ? AND execution_type = 'site' AND target_id = ?`,
      [
        siteStatus,
        progress,
        `通道设置进度: ${completedChannels}/${totalChannels} (成功:${successChannels}, 失败:${failedChannels})`,
        siteStatus,
        planId,
        siteId
      ]
    );

  } catch (error) {
    console.error(`❌ 检查考点 ${siteId} 完成状态失败:`, error);
  }
}

// 单通道OSD设置模拟方法
async function simulateSingleChannel(bindingId, failRate = 0.2) {
  const startTime = new Date();

  try {
    // 获取通道信息
    const [binding] = await query(
      `SELECT * FROM batch_osd_room_channel_bindings WHERE id = ?`,
      [bindingId]
    );

    if (!binding) {
      throw new Error('通道绑定信息不存在');
    }

    console.log(`🔄 重试设置通道: ${binding.site_name} - ${binding.room_name} - 通道${binding.channel_id}`);

    // 更新状态为执行中
    await query(
      `UPDATE batch_osd_room_channel_bindings SET status='running', start_time=NOW() WHERE id=?`,
      [bindingId]
    );

    // 随机执行时间（1-10秒）
    await sleepRandom(1000, 10000);

    // 随机执行结果
    const isSuccess = Math.random() > failRate;
    const status = isSuccess ? 'success' : 'failed';

    // 随机生成失败原因
    const failureReasons = [
      '网络连接超时',
      '设备响应异常',
      '认证失败',
      '设备忙碌',
      '参数错误',
      '设备离线',
      '权限不足',
      '设备故障'
    ];

    const message = isSuccess
      ? `OSD重试设置成功`
      : `重试失败：${failureReasons[Math.floor(Math.random() * failureReasons.length)]}`;

    // 计算执行耗时
    const endTime = new Date();
    const duration = endTime.getTime() - startTime.getTime();

    // 更新执行结果
    await query(
      `UPDATE batch_osd_room_channel_bindings SET status=?, message=?, end_time=NOW(), duration=? WHERE id=?`,
      [status, message, duration, bindingId]
    );

    const statusIcon = isSuccess ? '✅' : '❌';
    console.log(`${statusIcon} 通道${binding.channel_id} 重试完成: ${status} - ${message}`);

    return isSuccess;
  } catch (error) {
    const endTime = new Date();
    const duration = endTime.getTime() - startTime.getTime();
    const errorMessage = `重试异常: ${error.message}`;

    console.error(`❌ 通道重试异常:`, error);

    await query(
      `UPDATE batch_osd_room_channel_bindings SET status='failed', message=?, end_time=NOW(), duration=? WHERE id=?`,
      [errorMessage, duration, bindingId]
    );

    return false;
  }
}

module.exports = new BatchOsdService();
