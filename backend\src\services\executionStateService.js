const { query } = require('../config/database');

/**
 * 批量OSD执行状态管理服务
 */
class ExecutionStateService {
  /**
   * 保存执行状态快照
   */
  async saveExecutionState(planId, stateData) {
    try {
      console.log('💾 保存执行状态快照，计划ID:', planId);
      
      const {
        stateType = 'progress',
        executionPhase = 'osd_setting',
        executionStats = {},
        executionFlags = {},
        sitesTreeData = null,
        currentSelectedSite = null,
        osdExecutionList = [],
        concurrencyConfig = 3
      } = stateData;
      
      // 删除该计划的旧状态快照（保持最新状态）
      await query(
        'DELETE FROM batch_osd_execution_states WHERE plan_id = ? AND state_type = ? AND execution_phase = ?',
        [planId, stateType, executionPhase]
      );
      
      // 插入新的状态快照
      const result = await query(`
        INSERT INTO batch_osd_execution_states (
          plan_id, state_type, execution_phase,
          total_count, completed_count, success_count, failed_count, current_count,
          is_executing, is_completed, is_starting,
          sites_tree_data, current_selected_site, osd_execution_list,
          concurrency_config, snapshot_time
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
      `, [
        planId, stateType, executionPhase,
        executionStats.total || 0,
        executionStats.completed || 0,
        executionStats.success || 0,
        executionStats.failed || 0,
        executionStats.current || 0,
        executionFlags.isExecuting || false,
        executionFlags.isCompleted || false,
        executionFlags.isStarting || false,
        JSON.stringify(sitesTreeData),
        JSON.stringify(currentSelectedSite),
        JSON.stringify(osdExecutionList),
        concurrencyConfig
      ]);
      
      console.log('✅ 执行状态快照保存成功，ID:', result.insertId);
      return { success: true, stateId: result.insertId };
      
    } catch (error) {
      console.error('❌ 保存执行状态失败:', error);
      throw error;
    }
  }

  /**
   * 获取执行状态快照
   */
  async getExecutionState(planId, executionPhase = null) {
    try {
      console.log('📖 获取执行状态快照，计划ID:', planId, '执行阶段:', executionPhase);
      
      let sql = 'SELECT * FROM batch_osd_execution_states WHERE plan_id = ?';
      let params = [planId];
      
      if (executionPhase) {
        sql += ' AND execution_phase = ?';
        params.push(executionPhase);
      }
      
      sql += ' ORDER BY snapshot_time DESC LIMIT 1';
      
      const results = await query(sql, params);
      
      if (results.length === 0) {
        console.log('⚠️ 未找到执行状态快照');
        return null;
      }
      
      const state = results[0];
      
      // 解析JSON字段
      const executionState = {
        id: state.id,
        planId: state.plan_id,
        stateType: state.state_type,
        executionPhase: state.execution_phase,
        executionStats: {
          total: state.total_count,
          completed: state.completed_count,
          success: state.success_count,
          failed: state.failed_count,
          current: state.current_count
        },
        executionFlags: {
          isExecuting: state.is_executing,
          isCompleted: state.is_completed,
          isStarting: state.is_starting
        },
        sitesTreeData: state.sites_tree_data ? JSON.parse(state.sites_tree_data) : null,
        currentSelectedSite: state.current_selected_site ? JSON.parse(state.current_selected_site) : null,
        osdExecutionList: state.osd_execution_list ? JSON.parse(state.osd_execution_list) : [],
        concurrencyConfig: state.concurrency_config,
        snapshotTime: state.snapshot_time
      };
      
      console.log('✅ 执行状态快照获取成功');
      return executionState;
      
    } catch (error) {
      console.error('❌ 获取执行状态失败:', error);
      throw error;
    }
  }

  /**
   * 删除执行状态快照
   */
  async deleteExecutionState(planId) {
    try {
      console.log('🗑️ 删除执行状态快照，计划ID:', planId);
      
      const result = await query(
        'DELETE FROM batch_osd_execution_states WHERE plan_id = ?',
        [planId]
      );
      
      console.log('✅ 执行状态快照删除成功，影响行数:', result.affectedRows);
      return { success: true, deletedCount: result.affectedRows };
      
    } catch (error) {
      console.error('❌ 删除执行状态失败:', error);
      throw error;
    }
  }

  /**
   * 获取计划的所有执行状态历史
   */
  async getExecutionStateHistory(planId) {
    try {
      console.log('📚 获取执行状态历史，计划ID:', planId);
      
      const results = await query(`
        SELECT 
          id, state_type, execution_phase, 
          total_count, completed_count, success_count, failed_count,
          is_executing, is_completed, concurrency_config,
          snapshot_time
        FROM batch_osd_execution_states 
        WHERE plan_id = ? 
        ORDER BY snapshot_time DESC
      `, [planId]);
      
      console.log('✅ 获取执行状态历史成功，共', results.length, '条记录');
      return results;
      
    } catch (error) {
      console.error('❌ 获取执行状态历史失败:', error);
      throw error;
    }
  }

  /**
   * 清理过期的执行状态快照（保留最近10个）
   */
  async cleanupOldStates(planId, keepCount = 10) {
    try {
      console.log('🧹 清理过期执行状态，计划ID:', planId, '保留数量:', keepCount);
      
      // 获取需要保留的状态ID
      const keepStates = await query(`
        SELECT id FROM batch_osd_execution_states 
        WHERE plan_id = ? 
        ORDER BY snapshot_time DESC 
        LIMIT ?
      `, [planId, keepCount]);
      
      if (keepStates.length === 0) {
        console.log('⚠️ 没有找到需要保留的状态');
        return { deletedCount: 0 };
      }
      
      const keepIds = keepStates.map(state => state.id);
      const placeholders = keepIds.map(() => '?').join(',');
      
      // 删除不在保留列表中的状态
      const result = await query(`
        DELETE FROM batch_osd_execution_states 
        WHERE plan_id = ? AND id NOT IN (${placeholders})
      `, [planId, ...keepIds]);
      
      console.log('✅ 清理完成，删除了', result.affectedRows, '条过期状态');
      return { deletedCount: result.affectedRows };
      
    } catch (error) {
      console.error('❌ 清理过期状态失败:', error);
      throw error;
    }
  }
}

module.exports = new ExecutionStateService();
