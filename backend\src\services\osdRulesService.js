// OSD规则管理服务
const { query, transaction } = require('../config/database');

class OsdRulesService {

  // 获取OSD规则列表
  async getOsdRules(params = {}) {
    const { page = 1, size = 20, keyword, status } = params;
    const offset = (page - 1) * size;

    let sql = `
      SELECT id, name, type, format, description, preview, status,
             create_time as created_at, update_time as updated_at
      FROM osd_rules
      WHERE 1=1
    `;
    let countSql = 'SELECT COUNT(*) as total FROM osd_rules WHERE 1=1';
    const queryParams = [];
    const countParams = [];

    // 关键词搜索
    if (keyword) {
      sql += ' AND (name LIKE ? OR description LIKE ?)';
      countSql += ' AND (name LIKE ? OR description LIKE ?)';
      queryParams.push(`%${keyword}%`, `%${keyword}%`);
      countParams.push(`%${keyword}%`, `%${keyword}%`);
    }

    // 状态筛选
    if (status !== undefined) {
      sql += ' AND status = ?';
      countSql += ' AND status = ?';
      queryParams.push(status);
      countParams.push(status);
    }

    sql += ' ORDER BY create_time DESC LIMIT ? OFFSET ?';
    queryParams.push(size, offset);

    // 执行查询
    const [records, countResult] = await Promise.all([
      query(sql, queryParams),
      query(countSql, countParams)
    ]);

    const total = countResult[0].total;

    return {
      records,
      total,
      size,
      current: page,
      pages: Math.ceil(total / size)
    };
  }

  // 获取OSD规则详情
  async getOsdRuleById(id) {
    const sql = `
      SELECT id, name, type, format, description, preview, status,
             create_time as created_at, update_time as updated_at
      FROM osd_rules
      WHERE id = ?
    `;
    const results = await query(sql, [id]);
    return results[0] || null;
  }

  // 创建OSD规则
  async createOsdRule(ruleData) {
    const { name, type, format, description, preview, status = 1, created_by } = ruleData;

    const sql = `
      INSERT INTO osd_rules (name, type, format, description, preview, status)
      VALUES (?, ?, ?, ?, ?, ?)
    `;

    const result = await query(sql, [name, type, format, description, preview, status]);
    return result.insertId;
  }

  // 更新OSD规则
  async updateOsdRule(id, ruleData) {
    const { name, type, format, description, preview, status, updated_by } = ruleData;

    const sql = `
      UPDATE osd_rules
      SET name = ?, type = ?, format = ?, description = ?, preview = ?,
          status = ?, update_time = CURRENT_TIMESTAMP
      WHERE id = ?
    `;

    const result = await query(sql, [name, type, format, description, preview, status, id]);
    return result.affectedRows > 0;
  }

  // 删除OSD规则
  async deleteOsdRule(id) {
    // 检查是否有关联的批量计划
    const checkSql = 'SELECT COUNT(*) as count FROM batch_osd_plans WHERE rule_id = ?';
    const checkResult = await query(checkSql, [id]);

    if (checkResult[0].count > 0) {
      throw new Error('该规则已被批量计划使用，无法删除');
    }

    const sql = 'DELETE FROM osd_rules WHERE id = ?';
    const result = await query(sql, [id]);
    return result.affectedRows > 0;
  }

  // 批量删除OSD规则
  async batchDeleteOsdRules(ids) {
    return await transaction(async (connection) => {
      // 检查是否有关联的批量计划
      const checkSql = `SELECT rule_id FROM batch_osd_plans WHERE rule_id IN (${ids.map(() => '?').join(',')})`;
      const [checkResult] = await connection.execute(checkSql, ids);

      if (checkResult.length > 0) {
        const usedIds = checkResult.map(row => row.rule_id);
        throw new Error(`规则 ${usedIds.join(', ')} 已被批量计划使用，无法删除`);
      }

      // 执行删除
      const sql = `DELETE FROM osd_rules WHERE id IN (${ids.map(() => '?').join(',')})`;
      const [result] = await connection.execute(sql, ids);
      return result.affectedRows;
    });
  }

  // 获取可用的OSD规则（用于批量计划选择）
  async getAvailableOsdRules() {
    const sql = `
      SELECT id, name, type, format
      FROM osd_rules
      WHERE status = 1
      ORDER BY name
    `;
    return await query(sql);
  }

  // 验证规则名称是否唯一
  async validateRuleName(name, excludeId = null) {
    let sql = 'SELECT COUNT(*) as count FROM osd_rules WHERE name = ?';
    const params = [name];

    if (excludeId) {
      sql += ' AND id != ?';
      params.push(excludeId);
    }

    const result = await query(sql, params);
    return result[0].count === 0;
  }
}

module.exports = new OsdRulesService();
