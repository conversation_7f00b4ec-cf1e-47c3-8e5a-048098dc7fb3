// 测试服务器 - 简化版本，用于诊断问题
const express = require('express');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 8081;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 请求日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} ${req.method} ${req.url}`);
  next();
});

// 健康检查
app.get('/health', (req, res) => {
  console.log('收到健康检查请求');
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'patrol-system-test-api'
  });
});

// 机构树API
app.get('/api/organization/tree', async (req, res) => {
  console.log('收到机构树请求');

  try {
    // 简单的测试数据
    const testData = [
      {
        id: '51',
        name: '四川省',
        fullName: '四川省',
        shortName: '川',
        singleName: '四川',
        level: 1,
        sort: 1,
        status: 1,
        createTime: '2024-01-01 00:00:00',
        updateTime: '2024-01-01 00:00:00',
        children: [
          {
            id: '5101',
            name: '成都市',
            fullName: '成都市',
            shortName: '成都',
            singleName: '成都',
            level: 2,
            sort: 1,
            status: 1,
            createTime: '2024-01-01 00:00:00',
            updateTime: '2024-01-01 00:00:00',
            children: []
          }
        ]
      }
    ];

    res.json({
      code: 200,
      message: '获取成功',
      data: testData
    });
  } catch (error) {
    console.error('机构树API错误:', error);
    res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// OSD规则API
app.get('/api/osd-rules', async (req, res) => {
  console.log('收到OSD规则请求');

  try {
    const testData = [
      {
        id: 1,
        name: '标准考试OSD模板',
        description: '适用于标准化考试的OSD显示模板',
        template_content: '考点：{site_name} 考场：{room_number} 时间：{current_time}',
        font_size: 24,
        font_color: '#FFFFFF',
        background_color: '#000000',
        position_x: 10,
        position_y: 10,
        display_duration: 0,
        is_active: 1,
        create_time: '2024-01-01 00:00:00',
        update_time: '2024-01-01 00:00:00'
      }
    ];

    res.json({
      code: 200,
      message: '获取成功',
      data: testData
    });
  } catch (error) {
    console.error('OSD规则API错误:', error);
    res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 批量OSD计划API
app.get('/api/batch-osd/plans', async (req, res) => {
  console.log('收到批量OSD计划请求');

  try {
    const testData = [
      {
        id: 1,
        name: '2024年研究生考试OSD设置',
        description: '用于2024年研究生考试的OSD批量设置',
        rule_id: 1,
        rule_name: '标准考试OSD模板',
        target_type: 'custom',
        target_count: 55,
        execute_mode: 'scheduled',
        scheduled_time: '2024-12-20 08:00:00',
        concurrency: 5,
        status: 'completed',
        progress: 100,
        success_count: 55,
        failed_count: 0,
        create_time: '2024-01-15 10:00:00',
        update_time: '2024-01-15 12:00:00',
        creator: 'admin'
      }
    ];

    res.json({
      code: 200,
      message: '获取成功',
      data: {
        records: testData,
        total: testData.length,
        current: 1,
        size: 20
      }
    });
  } catch (error) {
    console.error('批量OSD计划API错误:', error);
    res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 获取计划的目标考点
app.get('/api/batch-osd/plans/:planId/target-sites', async (req, res) => {
  const { planId } = req.params;
  console.log(`收到获取计划 ${planId} 目标考点请求`);

  try {
    const testData = [
      {
        site_id: '510104001',
        site_name: '成都市石室中学',
        parent_name: '青羊区教育局',
        parent_id: 'org_510105',
        address: '成都市青羊区文庙前街93号',
        room_count: 30
      },
      {
        site_id: '510105001',
        site_name: '成都树德中学',
        parent_name: '青羊区教育局',
        parent_id: 'org_510105',
        address: '成都市青羊区光华大道一段1341号',
        room_count: 25
      }
    ];

    res.json({
      code: 200,
      message: '获取成功',
      data: testData
    });
  } catch (error) {
    console.error('目标考点API错误:', error);
    res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 获取计划的OSD数据
app.get('/api/batch-osd/plans/:planId/osd-data', async (req, res) => {
  const { planId } = req.params;
  console.log(`收到获取计划 ${planId} OSD数据请求`);

  try {
    // 生成更丰富的测试数据，模拟真实的执行设置数据
    const generateOsdData = () => {
      const sites = [
        { id: '510104001', name: '成都市石室中学', roomCount: 30 },
        { id: '510105001', name: '成都树德中学', roomCount: 25 }
      ];

      const osdList = [];
      let roomIdCounter = 1;

      sites.forEach(site => {
        // 为每个考点生成多个考场的OSD数据
        for (let roomNum = 1; roomNum <= site.roomCount; roomNum++) {
          const roomNumber = roomNum.toString().padStart(3, '0');

          // 每个考场可能有1-2个摄像机通道
          const channelCount = Math.floor(Math.random() * 2) + 1;

          for (let channelNum = 1; channelNum <= channelCount; channelNum++) {
            osdList.push({
              room_id: roomIdCounter,
              channel_id: channelNum,
              site_id: site.id,
              site_name: site.name,
              room_number: roomNumber,
              original_osd: `${site.name} 考场${roomNumber}`,
              target_osd: `${site.name.replace('成都市', '').replace('成都', '')} ${roomNumber}考场`,
              current_osd: `${site.name.replace('成都市', '').replace('成都', '')} ${roomNumber}考场`,
              status: Math.random() > 0.1 ? 'completed' : (Math.random() > 0.5 ? 'failed' : 'pending'),
              camera_name: `${site.name}-${roomNumber}考场-摄像机${channelNum}`,
              camera_uri: `rtsp://192.168.1.${Math.floor(Math.random() * 200 + 1)}:554/stream${channelNum}`
            });
            roomIdCounter++;
          }
        }
      });

      return { osdList };
    };

    const testData = generateOsdData();

    res.json({
      code: 200,
      message: '获取成功',
      data: testData
    });
  } catch (error) {
    console.error('OSD数据API错误:', error);
    res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 获取完整的计划数据（包括执行设置的详细信息）
app.get('/api/batch-osd/plans/:planId/full-data', async (req, res) => {
  const { planId } = req.params;
  console.log(`收到获取完整计划数据请求，计划ID: ${planId}`);

  try {
    // 模拟从数据库获取完整的计划数据
    const fullPlanData = {
      // 选中的考点数据
      selectedSites: [
        {
          id: '510104001',
          name: '成都市石室中学',
          parentName: '青羊区教育局',
          parentId: 'org_510105',
          address: '成都市青羊区文庙前街93号',
          roomCount: 30,
          isExamSite: true
        },
        {
          id: '510105001',
          name: '成都树德中学',
          parentName: '青羊区教育局',
          parentId: 'org_510105',
          address: '成都市青羊区光华大道一段1341号',
          roomCount: 25,
          isExamSite: true
        }
      ],

      // OSD执行列表数据
      osdExecutionList: generateOsdExecutionList(),

      // 房间通道绑定数据
      roomChannelBindings: generateRoomChannelBindings(),

      // 其他配置数据
      osdConfig: {
        ruleId: 1,
        ruleName: '标准OSD规则'
      },
      roomConfig: {
        roomCount: 30,
        backupRoomCount: 5
      },
      scheduleConfig: {
        executeMode: 'immediate',
        concurrency: 3
      },
      backupConfig: {
        enableBackup: true,
        backupStrategy: 'auto',
        retentionDays: 30
      }
    };

    res.json({
      code: 200,
      message: '获取成功',
      data: fullPlanData
    });
  } catch (error) {
    console.error('完整计划数据API错误:', error);
    res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 生成OSD执行列表数据
function generateOsdExecutionList() {
  const sites = [
    { id: '510104001', name: '成都市石室中学', roomCount: 30 },
    { id: '510105001', name: '成都树德中学', roomCount: 25 }
  ];

  const osdList = [];
  let idCounter = 1;

  sites.forEach(site => {
    for (let roomNum = 1; roomNum <= site.roomCount; roomNum++) {
      const roomNumber = roomNum.toString().padStart(3, '0');

      // 每个考场可能有1-2个摄像机通道
      const channelCount = Math.floor(Math.random() * 2) + 1;

      for (let channelNum = 1; channelNum <= channelCount; channelNum++) {
        osdList.push({
          id: `${site.id}_${roomNumber}_${channelNum}`,
          siteId: site.id,
          siteName: site.name,
          roomId: `${site.id}_room_${roomNumber}`,
          roomNumber: roomNumber,
          cameraName: `${site.name}-${roomNumber}考场-摄像机${channelNum}`,
          cameraUri: `rtsp://192.168.1.${Math.floor(Math.random() * 200 + 1)}:554/stream${channelNum}`,
          defaultOsd: `${site.name} 考场${roomNumber}`,
          targetOsd: `${site.name.replace('成都市', '').replace('成都', '')} ${roomNumber}考场`,
          currentOsd: `${site.name.replace('成都市', '').replace('成都', '')} ${roomNumber}考场`,
          status: Math.random() > 0.1 ? 'completed' : (Math.random() > 0.5 ? 'failed' : 'pending')
        });
        idCounter++;
      }
    }
  });

  return osdList;
}

// 生成房间通道绑定数据
function generateRoomChannelBindings() {
  const bindings = new Map();

  // 为成都市石室中学生成绑定
  for (let i = 1; i <= 30; i++) {
    const roomId = `510104001_room_${i.toString().padStart(3, '0')}`;
    const channelId = `510104001_channel_${i.toString().padStart(3, '0')}_1`;
    bindings.set(roomId, channelId);
  }

  // 为成都树德中学生成绑定
  for (let i = 1; i <= 25; i++) {
    const roomId = `510105001_room_${i.toString().padStart(3, '0')}`;
    const channelId = `510105001_channel_${i.toString().padStart(3, '0')}_1`;
    bindings.set(roomId, channelId);
  }

  return Array.from(bindings.entries()).map(([roomId, channelId]) => ({
    room_id: roomId,
    channel_id: channelId
  }));
}

// 404处理
app.use('*', (req, res) => {
  console.log('404请求:', req.originalUrl);
  res.status(404).json({
    code: 404,
    message: '接口不存在',
    path: req.originalUrl
  });
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  res.status(500).json({
    code: 500,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'development' ? error.message : '服务器错误'
  });
});

// 启动服务器
console.log('🚀 正在启动测试服务器...');

const server = app.listen(PORT, () => {
  console.log('✅ 测试服务器启动成功!');
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`🏥 健康检查: http://localhost:${PORT}/health`);
  console.log('📊 数据库状态: ❌ 测试模式（无数据库连接）');
  console.log('');
  console.log('📋 可用的测试API接口:');
  console.log('  - GET  /api/organization/tree          机构树数据');
  console.log('  - GET  /api/osd-rules                  OSD规则列表');
  console.log('  - GET  /api/batch-osd/plans            批量OSD计划列表');
  console.log('');
});

// 错误处理
server.on('error', (err) => {
  console.error('❌ 服务器启动失败:', err);
  if (err.code === 'EADDRINUSE') {
    console.error(`端口 ${PORT} 已被占用，请检查是否有其他服务在运行`);
  }
  process.exit(1);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...');
  server.close(() => {
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭服务器...');
  server.close(() => {
    process.exit(0);
  });
});

module.exports = app;
