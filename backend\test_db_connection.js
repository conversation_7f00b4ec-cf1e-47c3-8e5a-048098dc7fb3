// 测试数据库连接
const mysql = require('mysql2/promise');

async function testConnection() {
  console.log('🔍 测试数据库连接...\n');

  const config = {
    host: 'localhost',
    port: 3306,
    user: 'root',
    password: 'Aiwa75210!',
    database: 'patrol_system',
    charset: 'utf8mb4'
  };

  try {
    console.log('📝 连接配置:');
    console.log(`   主机: ${config.host}:${config.port}`);
    console.log(`   用户: ${config.user}`);
    console.log(`   数据库: ${config.database}`);
    console.log('');

    const connection = await mysql.createConnection(config);
    console.log('✅ 数据库连接成功!');

    // 测试查询
    const [versionRows] = await connection.execute('SELECT VERSION() as version');
    console.log(`📊 MySQL版本: ${versionRows[0].version}`);

    // 检查当前数据库
    const [dbRows] = await connection.execute('SELECT DATABASE() as current_db');
    console.log(`📊 当前数据库: ${dbRows[0].current_db}`);

    // 检查表是否存在
    const [tables] = await connection.execute('SHOW TABLES');
    console.log(`📊 数据库表数量: ${tables.length}`);
    
    if (tables.length > 0) {
      console.log('📋 现有表:');
      tables.forEach(table => {
        const tableName = Object.values(table)[0];
        console.log(`   - ${tableName}`);
      });
    }

    await connection.end();
    console.log('\n🎉 数据库连接测试成功!');
    return true;

  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n🔧 可能的解决方案:');
      console.log('1. 检查用户名和密码是否正确');
      console.log('2. 检查用户是否有访问权限');
      console.log('3. 尝试重置MySQL root密码');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('\n🔧 数据库不存在，需要创建数据库');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('\n🔧 MySQL服务未运行，请启动MySQL服务');
    }
    
    return false;
  }
}

// 运行测试
testConnection();
