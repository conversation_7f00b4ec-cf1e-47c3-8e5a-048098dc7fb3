const mysql = require('mysql2/promise');

async function testDatabaseConnection() {
  console.log('=== 数据库连接测试 ===\n');
  
  // 当前配置信息
  const configs = [
    {
      name: '后端配置 (database.js)',
      config: {
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: 'Aiwa75210！',
        database: 'patrol_system',
        charset: 'utf8mb4'
      }
    },
    {
      name: 'Mock服务器配置 (mock-server.js)',
      config: {
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: 'Aiwa75210！',
        database: 'patrol_system',
        charset: 'utf8mb4'
      }
    }
  ];
  
  for (const { name, config } of configs) {
    console.log(`🔍 测试配置: ${name}`);
    console.log(`   连接信息: ${config.user}@${config.host}:${config.port}/${config.database}`);
    console.log(`   密码: ${config.password}`);
    
    try {
      const connection = await mysql.createConnection(config);
      console.log('   ✅ 连接成功！');
      
      // 测试基本查询
      const [versionResult] = await connection.execute('SELECT VERSION() as version');
      console.log(`   📊 MySQL版本: ${versionResult[0].version}`);
      
      // 检查数据库
      const [databases] = await connection.execute('SHOW DATABASES');
      const dbNames = databases.map(db => Object.values(db)[0]);
      console.log(`   📁 可访问数据库: ${dbNames.join(', ')}`);
      
      if (dbNames.includes('patrol_system')) {
        console.log('   ✅ patrol_system数据库存在');
        
        // 切换到patrol_system并检查表
        await connection.execute('USE patrol_system');
        const [tables] = await connection.execute('SHOW TABLES');
        const tableNames = tables.map(t => Object.values(t)[0]);
        
        if (tableNames.length > 0) {
          console.log(`   📋 现有表: ${tableNames.join(', ')}`);
          
          // 检查关键业务表
          const keyTables = ['osd_rules', 'batch_osd_plans'];
          const missingTables = keyTables.filter(table => !tableNames.includes(table));
          
          if (missingTables.length === 0) {
            console.log('   ✅ 所有关键业务表都存在');
            
            // 检查数据
            for (const tableName of keyTables) {
              try {
                const [countResult] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName}`);
                console.log(`   📊 ${tableName}: ${countResult[0].count} 条记录`);
              } catch (error) {
                console.log(`   ❌ ${tableName}: 查询失败 - ${error.message}`);
              }
            }
          } else {
            console.log(`   ⚠️  缺少关键业务表: ${missingTables.join(', ')}`);
          }
        } else {
          console.log('   ⚠️  patrol_system数据库为空，需要初始化');
        }
      } else {
        console.log('   ⚠️  patrol_system数据库不存在，需要创建');
      }
      
      await connection.end();
      console.log('   🎉 数据库连接测试成功！\n');
      return true;
      
    } catch (error) {
      console.log(`   ❌ 连接失败: ${error.message}`);
      if (error.code) {
        console.log(`   🔍 错误代码: ${error.code}`);
      }
      console.log('');
    }
  }
  
  return false;
}

// 运行测试
testDatabaseConnection()
  .then(success => {
    if (success) {
      console.log('🎯 数据库连接正常，可以使用真实数据库');
    } else {
      console.log('⚠️  数据库连接失败，当前使用Mock数据模式');
      console.log('\n💡 解决方案：');
      console.log('   1. 检查MySQL服务是否启动');
      console.log('   2. 确认root用户密码是否为: Aiwa75210！');
      console.log('   3. 检查MySQL用户权限设置');
      console.log('   4. 运行数据库初始化脚本创建必要的表');
    }
  })
  .catch(error => {
    console.error('测试过程中发生错误:', error);
  });
