const mysql = require('mysql2/promise');

async function testFixedConnection() {
  console.log('=== 测试修正后的数据库连接 ===\n');
  
  const config = {
    host: 'localhost',
    port: 3306,
    user: 'root',
    password: 'Aiwa75210!',  // 修正后的密码（英文感叹号）
    database: 'patrol_system',
    charset: 'utf8mb4'
  };
  
  try {
    console.log('🔍 连接配置:');
    console.log(`   主机: ${config.host}:${config.port}`);
    console.log(`   用户: ${config.user}`);
    console.log(`   密码: ${config.password}`);
    console.log(`   数据库: ${config.database}`);
    
    const connection = await mysql.createConnection(config);
    console.log('✅ 数据库连接成功！');
    
    // 获取基本信息
    const [versionResult] = await connection.execute('SELECT VERSION() as version');
    console.log(`📊 MySQL版本: ${versionResult[0].version}`);
    
    // 检查当前数据库
    const [dbResult] = await connection.execute('SELECT DATABASE() as current_db');
    console.log(`📁 当前数据库: ${dbResult[0].current_db}`);
    
    // 检查表结构
    const [tables] = await connection.execute('SHOW TABLES');
    const tableNames = tables.map(t => Object.values(t)[0]);
    console.log(`📋 数据库表: ${tableNames.join(', ')}`);
    
    // 检查关键业务表
    const keyTables = ['osd_rules', 'batch_osd_plans'];
    console.log('\n🔍 检查关键业务表:');
    
    for (const tableName of keyTables) {
      if (tableNames.includes(tableName)) {
        try {
          const [countResult] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName}`);
          console.log(`   ✅ ${tableName}: ${countResult[0].count} 条记录`);
          
          // 显示表结构
          const [columns] = await connection.execute(`DESCRIBE ${tableName}`);
          const columnNames = columns.map(col => col.Field);
          console.log(`      字段: ${columnNames.join(', ')}`);
          
        } catch (error) {
          console.log(`   ❌ ${tableName}: 查询失败 - ${error.message}`);
        }
      } else {
        console.log(`   ⚠️  ${tableName}: 表不存在`);
      }
    }
    
    // 检查其他表
    const otherTables = ['administrative_divisions', 'exam_sites'];
    console.log('\n🔍 检查其他数据表:');
    
    for (const tableName of otherTables) {
      if (tableNames.includes(tableName)) {
        try {
          const [countResult] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName}`);
          console.log(`   ✅ ${tableName}: ${countResult[0].count} 条记录`);
        } catch (error) {
          console.log(`   ❌ ${tableName}: 查询失败 - ${error.message}`);
        }
      } else {
        console.log(`   ⚠️  ${tableName}: 表不存在`);
      }
    }
    
    await connection.end();
    
    console.log('\n🎉 数据库连接测试完成！');
    console.log('✅ 所有配置已修正，可以正常使用真实数据库了');
    
    return true;
    
  } catch (error) {
    console.log(`❌ 连接失败: ${error.message}`);
    console.log(`🔍 错误代码: ${error.code}`);
    return false;
  }
}

testFixedConnection();
