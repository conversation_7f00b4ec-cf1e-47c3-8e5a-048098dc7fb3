const mysql = require('mysql2/promise');

async function testConnection() {
  console.log('测试MySQL连接...\n');
  
  // 测试不同的连接配置
  const configs = [
    {
      name: '标准连接 - localhost',
      config: {
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: 'Aiwa75210！'
      }
    },
    {
      name: '标准连接 - 127.0.0.1',
      config: {
        host: '127.0.0.1',
        port: 3306,
        user: 'root',
        password: 'Aiwa75210！'
      }
    },
    {
      name: '禁用SSL连接',
      config: {
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: 'Aiwa75210！',
        ssl: false
      }
    },
    {
      name: '使用mysql_native_password',
      config: {
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: 'Aiwa75210！',
        authPlugins: {
          mysql_native_password: () => require('mysql2/lib/auth_plugins').mysql_native_password
        }
      }
    },
    {
      name: '空密码测试',
      config: {
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: ''
      }
    },
    {
      name: '默认密码测试',
      config: {
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: 'root'
      }
    }
  ];
  
  for (const { name, config } of configs) {
    console.log(`\n🔍 测试: ${name}`);
    console.log(`   连接参数: ${config.user}@${config.host}:${config.port}`);
    
    try {
      const connection = await mysql.createConnection(config);
      console.log('   ✅ 连接成功！');
      
      // 测试基本查询
      try {
        const [result] = await connection.execute('SELECT VERSION() as version');
        console.log(`   📊 MySQL版本: ${result[0].version}`);
        
        // 测试数据库列表
        const [databases] = await connection.execute('SHOW DATABASES');
        console.log(`   📁 可访问的数据库: ${databases.map(db => Object.values(db)[0]).join(', ')}`);
        
        // 检查patrol_system数据库是否存在
        const patrolSystemExists = databases.some(db => Object.values(db)[0] === 'patrol_system');
        if (patrolSystemExists) {
          console.log('   ✅ patrol_system数据库已存在');
          
          // 切换到patrol_system数据库并检查表
          await connection.execute('USE patrol_system');
          const [tables] = await connection.execute('SHOW TABLES');
          if (tables.length > 0) {
            console.log(`   📋 现有表: ${tables.map(t => Object.values(t)[0]).join(', ')}`);
          } else {
            console.log('   ⚠️  patrol_system数据库为空');
          }
        } else {
          console.log('   ⚠️  patrol_system数据库不存在');
        }
        
      } catch (queryError) {
        console.log(`   ❌ 查询失败: ${queryError.message}`);
      }
      
      await connection.end();
      console.log('   🎉 这个连接配置可以使用！');
      return config; // 返回成功的配置
      
    } catch (error) {
      console.log(`   ❌ 连接失败: ${error.message}`);
      if (error.code) {
        console.log(`   🔍 错误代码: ${error.code}`);
      }
    }
  }
  
  console.log('\n❌ 所有连接配置都失败了');
  console.log('\n💡 建议检查：');
  console.log('   1. MySQL服务是否正在运行');
  console.log('   2. root用户密码是否正确');
  console.log('   3. root用户是否有远程连接权限');
  console.log('   4. MySQL配置文件中的bind-address设置');
  console.log('   5. 防火墙是否阻止了3306端口');
  
  return null;
}

// 运行测试
testConnection()
  .then(successConfig => {
    if (successConfig) {
      console.log('\n🎯 推荐使用的连接配置:');
      console.log(JSON.stringify(successConfig, null, 2));
    }
  })
  .catch(error => {
    console.error('测试过程中发生错误:', error);
  });
