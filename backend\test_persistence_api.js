// 测试数据持久化API
const http = require('http');

const BASE_URL = 'http://localhost:8080/api/batch-osd';

// 简单的HTTP请求封装
function makeRequest(method, url, data = null) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    if (data) {
      const jsonData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(jsonData);
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({ data: parsedData, status: res.statusCode });
        } catch (error) {
          resolve({ data: responseData, status: res.statusCode });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testPersistenceAPI() {
  console.log('🚀 开始测试OSD批量设置数据持久化API...\n');

  try {
    // 1. 测试创建带数据持久化的批量OSD计划
    console.log('📋 测试创建带数据持久化的批量OSD计划...');
    const createResponse = await makeRequest('POST', `${BASE_URL}/plans-with-persistence`, {
      name: "测试数据持久化计划",
      description: "测试OSD批量设置的数据持久化功能",
      rule_id: 1,
      target_type: "custom",
      execute_mode: "immediate",
      concurrency: 3,
      selected_sites: [
        {
          id: "510104001",
          site_id: "510104001",
          site_name: "成都七中",
          site_code: "CD001",
          province_id: "51",
          province_name: "四川省",
          city_id: "5101",
          city_name: "成都市",
          district_id: "510104",
          district_name: "锦江区",
          address: "成都市锦江区林荫街1号",
          contact_person: "张老师",
          contact_phone: "028-12345678",
          room_count: 25
        },
        {
          id: "510105002",
          site_id: "510105002",
          site_name: "树德光华",
          site_code: "CD002",
          province_id: "51",
          province_name: "四川省",
          city_id: "5101",
          city_name: "成都市",
          district_id: "510105",
          district_name: "青羊区",
          address: "成都市青羊区光华大道一段1386号",
          contact_person: "李老师",
          contact_phone: "028-87654321",
          room_count: 30
        }
      ]
    });

    console.log('✅ 计划创建成功:', createResponse.data);
    const planId = createResponse.data.data.id;

    // 2. 测试获取计划选定的考点列表
    console.log('\n📋 测试获取计划选定的考点列表...');
    const sitesResponse = await makeRequest('GET', `${BASE_URL}/plans/${planId}/sites`);
    console.log('✅ 考点列表获取成功:', sitesResponse.data.data.length, '个考点');

    // 3. 测试获取考点的考场列表
    console.log('\n📋 测试获取考点的考场列表...');
    const roomsResponse = await makeRequest('GET', `${BASE_URL}/plans/${planId}/sites/510104001/rooms`);
    console.log('✅ 考场列表获取成功:', roomsResponse.data.data.length, '个考场');

    // 4. 测试获取计划详细信息（包含持久化数据）
    console.log('\n📋 测试获取计划详细信息...');
    const detailResponse = await makeRequest('GET', `${BASE_URL}/plans/${planId}/detail-with-persistence`);
    console.log('✅ 计划详情获取成功:');
    console.log('   - 计划名称:', detailResponse.data.data.name);
    console.log('   - 考点数量:', detailResponse.data.data.site_count);
    console.log('   - 考场总数:', detailResponse.data.data.room_count);

    // 5. 测试执行带数据持久化的批量OSD计划
    console.log('\n📋 测试执行带数据持久化的批量OSD计划...');
    const executeResponse = await makeRequest('POST', `${BASE_URL}/plans/${planId}/execute-with-persistence`);
    console.log('✅ 计划执行启动成功:', executeResponse.data.message);

    // 6. 等待一段时间后获取执行日志
    console.log('\n⏳ 等待3秒后获取执行日志...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    const logsResponse = await makeRequest('GET', `${BASE_URL}/plans/${planId}/execution-logs-with-filter`);
    console.log('✅ 执行日志获取成功:', logsResponse.data.data.length, '条日志');

    // 显示部分日志
    if (logsResponse.data.data.length > 0) {
      console.log('\n📊 执行日志示例:');
      logsResponse.data.data.slice(0, 3).forEach((log, index) => {
        console.log(`   ${index + 1}. ${log.site_name} - ${log.status} - ${log.message}`);
      });
    }

    // 7. 测试筛选功能
    console.log('\n📋 测试日志筛选功能...');
    const filteredLogsResponse = await makeRequest('GET', `${BASE_URL}/plans/${planId}/execution-logs-with-filter?status=success&limit=5`);
    console.log('✅ 筛选日志获取成功:', filteredLogsResponse.data.data.length, '条成功日志');

    console.log('\n🎉 所有数据持久化API测试完成！');
    console.log('\n📊 测试结果总结:');
    console.log('   ✅ 计划创建和数据持久化');
    console.log('   ✅ 考点数据保存');
    console.log('   ✅ 考场编号生成');
    console.log('   ✅ 考点考场绑定关系');
    console.log('   ✅ 执行结果日志记录');
    console.log('   ✅ 数据查询和筛选');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.data) {
      console.error('   错误详情:', JSON.stringify(error.data, null, 2));
    }
  }
}

// 运行测试
testPersistenceAPI();
