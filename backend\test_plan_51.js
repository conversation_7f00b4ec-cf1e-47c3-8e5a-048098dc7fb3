const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'Aiwa75210!',
  database: 'patrol_system',
  charset: 'utf8mb4'
};

async function testPlan51() {
  let connection;

  try {
    console.log('🔍 检查计划51的数据...');

    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    const planId = 51;
    
    // 1. 检查计划考点数据
    console.log('\n📍 检查计划考点数据:');
    const [planSites] = await connection.execute(`
      SELECT * FROM batch_osd_plan_sites WHERE plan_id = ?
    `, [planId]);
    console.log(`计划考点数据: ${planSites.length} 条`);
    planSites.forEach(site => {
      console.log(`  - 考点: ${site.site_name} (${site.site_id})`);
      console.log(`    地址: ${site.address || '未知'}`);
      console.log(`    状态: ${site.status || '未知'}`);
    });
    
    // 2. 检查房间通道绑定数据
    console.log('\n🔗 检查房间通道绑定数据:');
    const [bindings] = await connection.execute(`
      SELECT * FROM batch_osd_room_channel_bindings WHERE plan_id = ? LIMIT 10
    `, [planId]);
    console.log(`房间通道绑定数据: ${bindings.length} 条`);
    bindings.forEach((binding, index) => {
      console.log(`  ${index + 1}. 房间: ${binding.room_id}, 通道: ${binding.channel_id}`);
      console.log(`     考点: ${binding.site_name}`);
      console.log(`     原始OSD: ${binding.original_osd}`);
      console.log(`     目标OSD: ${binding.target_osd}`);
      console.log(`     状态: ${binding.status}`);
    });
    
    // 3. 检查生成的考场数据
    console.log('\n🏫 检查生成的考场数据:');
    const [rooms] = await connection.execute(`
      SELECT * FROM batch_osd_generated_rooms WHERE plan_id = ? LIMIT 10
    `, [planId]);
    console.log(`生成的考场数据: ${rooms.length} 条`);
    rooms.forEach((room, index) => {
      console.log(`  ${index + 1}. 考场: ${room.room_number}, 考点: ${room.site_id}`);
      console.log(`     考场ID: ${room.id}`);
    });

    console.log('\n✅ 检查完成！');

  } catch (error) {
    console.error('❌ 检查失败:', error);
    console.error('错误详情:', error.stack);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 运行测试
testPlan51();
