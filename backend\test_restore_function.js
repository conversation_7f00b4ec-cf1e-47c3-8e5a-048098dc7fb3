const { query } = require('./src/config/database');
const batchOsdService = require('./src/services/batchOsdService');

async function testRestoreFunction() {
  try {
    console.log('🧪 测试恢复功能...');

    // 1. 检查计划列表
    console.log('\n1. 获取计划列表...');
    const plans = await batchOsdService.getBatchOsdPlans({ page: 1, size: 10 });
    console.log(`找到 ${plans.records.length} 个计划`);

    // 2. 检查每个计划的备份状态
    for (const plan of plans.records) {
      console.log(`\n计划: ${plan.name} (ID: ${plan.id})`);
      console.log(`状态: ${plan.status}`);
      console.log(`hasBackup: ${plan.hasBackup}`);
      console.log(`has_backup_enabled: ${plan.has_backup_enabled}`);

      // 3. 检查备份数据
      const backupData = await query(`
        SELECT COUNT(*) as count 
        FROM batch_osd_backup_data 
        WHERE plan_id = ?
      `, [plan.id]);
      console.log(`备份数据数量: ${backupData[0].count}`);

      // 4. 检查配置数据
      const configs = await query(`
        SELECT config_type, config_data 
        FROM batch_osd_plan_configs 
        WHERE plan_id = ?
      `, [plan.id]);
      console.log(`配置数据:`, configs);

      // 5. 如果计划已完成且有备份，测试恢复功能
      if ((plan.status === 'completed' || plan.status === 'failed') && plan.hasBackup) {
        console.log(`\n🔧 测试恢复功能 (计划ID: ${plan.id})...`);
        try {
          const result = await batchOsdService.restoreOsdBackup(plan.id);
          console.log(`恢复结果: ${result}`);
        } catch (error) {
          console.error(`恢复失败: ${error.message}`);
        }
      }
    }

    console.log('\n✅ 测试完成');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
testRestoreFunction().then(() => {
  console.log('🎉 测试脚本执行完成');
  process.exit(0);
}).catch(error => {
  console.error('💥 测试脚本执行失败:', error);
  process.exit(1);
}); 