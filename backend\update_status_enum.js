const mysql = require('mysql2/promise');

async function updateStatusEnum() {
  let connection;
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'Aiwa75210!',
      database: 'patrol_system'
    });

    console.log('✅ 数据库连接成功');

    // 修改 batch_osd_plans 表的 status 字段
    const sql = `
      ALTER TABLE batch_osd_plans
      MODIFY COLUMN status ENUM('pending', 'running', 'paused', 'completed', 'failed', 'cancelled', 'restored') DEFAULT 'pending'
    `;

    await connection.execute(sql);
    console.log('✅ 状态枚举更新成功');

    // 验证修改结果
    const [result] = await connection.execute('DESCRIBE batch_osd_plans');
    const statusField = result.find(field => field.Field === 'status');
    console.log('状态字段定义:', statusField.Type);

  } catch (error) {
    console.error('❌ 更新失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

updateStatusEnum();
