// 更新考点URI格式的脚本
const mysql = require('mysql2/promise');

const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: 'Aiwa75210!',
  database: 'patrol_system',
  charset: 'utf8mb4'
};

// 地区代码映射
const codeMap = {
  // 省份
  '四川': 'sc', '广东': 'gd', '北京': 'bj', '上海': 'sh', '江苏': 'js', '山东': 'sd',
  // 地市
  '成都': 'cd', '绵阳': 'my', '广州': 'gz', '深圳': 'sz', '南京': 'nj', '苏州': 'sz',
  '济南': 'jn', '青岛': 'qd', '杭州': 'hz', '宁波': 'nb', '武汉': 'wh', '长沙': 'cs',
  // 区县
  '锦江': 'jj', '青羊': 'qy', '金牛': 'jn', '武侯': 'wh', '成华': 'ch',
  '天河': 'th', '越秀': 'yx', '荔湾': 'lw', '海珠': 'hz', '白云': 'by',
  '海淀': 'hd', '朝阳': 'cy', '西城': 'xc', '东城': 'dc', '丰台': 'ft',
  '浦东': 'pd', '黄浦': 'hp', '静安': 'ja', '徐汇': 'xh', '长宁': 'cn'
};

// 生成教育简拼
function generateEducationCode(name) {
  if (!name) return 'jy';

  for (const [key, value] of Object.entries(codeMap)) {
    if (name.includes(key)) {
      return value;
    }
  }

  // 默认使用拼音首字母生成
  return generatePinyinCode(name);
}

// 生成拼音简码
function generatePinyinCode(name) {
  const pinyinMap = {
    '安': 'a', '北': 'b', '成': 'c', '大': 'd', '二': 'e', '福': 'f', '广': 'g', '海': 'h',
    '江': 'j', '开': 'k', '辽': 'l', '美': 'm', '南': 'n', '欧': 'o', '平': 'p', '青': 'q',
    '人': 'r', '四': 's', '天': 't', '武': 'w', '西': 'x', '云': 'y', '浙': 'z',
    '中': 'z', '华': 'h', '师': 's', '范': 'f', '学': 'x', '校': 'x', '区': 'q', '县': 'x',
    '市': 's', '省': 's', '州': 'z', '港': 'g', '澳': 'a', '台': 't'
  };
  
  let code = '';
  for (let i = 0; i < Math.min(name.length, 2); i++) {
    const char = name[i];
    if (pinyinMap[char]) {
      code += pinyinMap[char];
    } else if (/[a-zA-Z]/.test(char)) {
      code += char.toLowerCase();
    }
  }
  
  return code || 'jy';
}

// 生成学校简拼
function generateSchoolCode(schoolName) {
  if (!schoolName) return '';

  // 移除常见后缀
  const cleanName = schoolName.replace(/(中学|小学|学校|幼儿园|附属|实验|外国语)$/g, '');

  // 提取关键字符生成简拼
  const chars = cleanName.split('');
  let code = '';

  for (const char of chars) {
    if (/[\u4e00-\u9fa5]/.test(char)) { // 中文字符
      const pinyinMap = {
        '第': 'd', '七': 'q', '中': 'z', '学': 'x', '盐': 'y', '道': 'd', '街': 'j',
        '华': 'h', '南': 'n', '师': 's', '范': 'f', '大': 'd', '附': 'f', '属': 's',
        '石': 's', '室': 's', '人': 'r', '民': 'm', '川': 'c', '四': 's', '电': 'd',
        '子': 'z', '科': 'k', '技': 'j', '成': 'c', '都': 'd', '外': 'w', '国': 'g',
        '语': 'y', '绵': 'm', '阳': 'y', '实': 's', '验': 'y', '广': 'g', '东': 'd',
        '北': 'b', '京': 'j', '上': 's', '海': 'h', '交': 'j', '通': 't', '复': 'f',
        '旦': 'd', '山': 's', '同': 't', '济': 'j'
      };
      if (pinyinMap[char]) {
        code += pinyinMap[char];
      }
    } else if (/[a-zA-Z0-9]/.test(char)) {
      code += char.toLowerCase();
    }
  }

  return code.substring(0, 6); // 限制长度
}

async function updateUriFormat() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功\n');

    // 获取所有行政区划数据
    const [adminRows] = await connection.execute(`
      SELECT id, name, short_name, level, parent_id FROM administrative_divisions
    `);
    
    const adminMap = {};
    adminRows.forEach(row => {
      adminMap[row.id] = row;
    });

    // 获取所有考点数据
    const [examRows] = await connection.execute(`
      SELECT id, name, short_name, parent_id, parent_level FROM exam_sites
    `);

    console.log('=== 开始更新考点URI ===');
    
    for (const exam of examRows) {
      const schoolCode = generateSchoolCode(exam.short_name || exam.name);
      if (!schoolCode) {
        console.log(`跳过 ${exam.name}: 无法生成学校代码`);
        continue;
      }

      let uriParts = [schoolCode];
      const parentNode = adminMap[exam.parent_id];
      
      if (!parentNode) {
        console.log(`跳过 ${exam.name}: 找不到上级机构 ${exam.parent_id}`);
        continue;
      }

      // 根据考点的上级机构类型生成不同的URI结构
      if (exam.parent_level === 'province') {
        // 省直属考点：学校简拼.省份教育简拼.cnjy
        const provinceCode = generateEducationCode(parentNode.short_name || parentNode.name);
        uriParts.push(`${provinceCode}jy`, 'cnjy');
      } else if (exam.parent_level === 'city') {
        // 市直属考点：学校简拼.地市教育简拼.省份教育简拼.cnjy
        const cityCode = generateEducationCode(parentNode.short_name || parentNode.name);
        const provinceNode = adminMap[parentNode.parent_id];
        const provinceCode = provinceNode ? generateEducationCode(provinceNode.short_name || provinceNode.name) : 'sf';
        uriParts.push(`${cityCode}jy`, `${provinceCode}jy`, 'cnjy');
      } else if (exam.parent_level === 'district') {
        // 区县考点：学校简拼.区县教育简拼.地市教育简拼.省份教育简拼.cnjy
        const districtCode = generateEducationCode(parentNode.short_name || parentNode.name);
        const cityNode = adminMap[parentNode.parent_id];
        const provinceNode = cityNode ? adminMap[cityNode.parent_id] : null;
        const cityCode = cityNode ? generateEducationCode(cityNode.short_name || cityNode.name) : 'ds';
        const provinceCode = provinceNode ? generateEducationCode(provinceNode.short_name || provinceNode.name) : 'sf';
        uriParts.push(`${districtCode}jy`, `${cityCode}jy`, `${provinceCode}jy`, 'cnjy');
      }

      const newUri = uriParts.join('.');
      
      // 更新数据库
      await connection.execute(`
        UPDATE exam_sites SET uri = ? WHERE id = ?
      `, [newUri, exam.id]);
      
      console.log(`✓ ${exam.name}: ${newUri}`);
    }

    console.log(`\n=== 更新完成 ===`);
    console.log(`共更新 ${examRows.length} 个考点的URI`);

  } catch (error) {
    console.error('更新失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行更新
updateUriFormat();
