-- 标考高清网上巡查管理平台测试数据
-- 插入时间: 2024-07-22

USE `patrol_system`;

-- 插入部门数据
INSERT INTO `sys_dept` (`id`, `parent_id`, `dept_name`, `dept_code`, `sort_order`, `leader`, `phone`, `email`, `status`) VALUES
(1, 0, '总公司', 'ROOT', 0, '系统管理员', '13800138000', '<EMAIL>', 1),
(2, 1, '技术部', 'TECH', 1, '技术总监', '13800138001', '<EMAIL>', 1),
(3, 1, '运维部', 'OPS', 2, '运维总监', '13800138002', '<EMAIL>', 1),
(4, 1, '安全部', 'SEC', 3, '安全总监', '13800138003', '<EMAIL>', 1);

-- 插入角色数据
INSERT INTO `sys_role` (`id`, `role_name`, `role_code`, `description`, `status`) VALUES
(1, '超级管理员', 'SUPER_ADMIN', '拥有系统所有权限', 1),
(2, '系统管理员', 'ADMIN', '拥有系统管理权限', 1),
(3, '操作员', 'OPERATOR', '拥有业务操作权限', 1),
(4, '观察员', 'OBSERVER', '只有查看权限', 1);

-- 插入用户数据（密码为123456的BCrypt加密）
INSERT INTO `sys_user` (`id`, `username`, `password`, `real_name`, `email`, `phone`, `status`, `user_type`, `dept_id`) VALUES
(1, 'admin', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTOQhQjMqVGG4iZBm', '系统管理员', '<EMAIL>', '13800138000', 1, 1, 1),
(2, 'operator', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTOQhQjMqVGG4iZBm', '操作员', '<EMAIL>', '13800138001', 1, 2, 2),
(3, 'observer', '$2a$10$7JB720yubVSOfvVWbfXCOOlWpZXGlDYDdTOWkTOQhQjMqVGG4iZBm', '观察员', '<EMAIL>', '13800138002', 1, 3, 3);

-- 插入用户角色关联数据
INSERT INTO `sys_user_role` (`user_id`, `role_id`) VALUES
(1, 1),
(2, 3),
(3, 4);

-- 插入菜单数据
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_code`, `menu_type`, `path`, `component`, `icon`, `sort_order`, `visible`, `status`, `perms`) VALUES
-- 一级菜单
(1, 0, '首页', 'HOME', 2, '/dashboard', 'Dashboard', 'el-icon-house', 1, 1, 1, 'dashboard:view'),
(2, 0, '巡查事务', 'PATROL', 1, '/patrol', '', 'el-icon-view', 2, 1, 1, ''),
(3, 0, '平台管理', 'PLATFORM', 1, '/platform', '', 'el-icon-setting', 3, 1, 1, ''),
(4, 0, '系统设定', 'SYSTEM', 1, '/system', '', 'el-icon-tools', 4, 1, 1, ''),

-- 巡查事务二级菜单
(11, 2, '巡查任务', 'PATROL_TASK', 2, '/patrol/task', 'patrol/Task', 'el-icon-document', 1, 1, 1, 'patrol:task:view'),
(12, 2, '实时监控', 'PATROL_MONITOR', 2, '/patrol/monitor', 'patrol/Monitor', 'el-icon-video-camera', 2, 1, 1, 'patrol:monitor:view'),
(13, 2, '报警管理', 'PATROL_ALARM', 2, '/patrol/alarm', 'patrol/Alarm', 'el-icon-warning', 3, 1, 1, 'patrol:alarm:view'),

-- 平台管理二级菜单
(21, 3, '资源配置管理', 'RESOURCE_CONFIG', 1, '/platform/resource', '', 'el-icon-cpu', 1, 1, 1, ''),
(22, 3, '参数设置', 'PARAM_SETTING', 2, '/platform/param', 'platform/Param', 'el-icon-setting', 2, 1, 1, 'platform:param:view'),
(23, 3, '统计报表', 'STATISTICS', 2, '/platform/statistics', 'platform/Statistics', 'el-icon-data-line', 3, 1, 1, 'platform:statistics:view'),
(24, 3, '基本运维', 'MAINTENANCE', 2, '/platform/maintenance', 'platform/Maintenance', 'el-icon-tools', 4, 1, 1, 'platform:maintenance:view'),

-- 资源配置管理三级菜单
(211, 21, '源逻辑拓扑', 'SOURCE_TOPOLOGY', 2, '/platform/resource/topology', 'platform/resource/Topology', 'el-icon-share', 1, 1, 1, 'resource:topology:view'),
(212, 21, '资源列表', 'RESOURCE_LIST', 2, '/platform/resource/list', 'platform/resource/List', 'el-icon-menu', 2, 1, 1, 'resource:list:view'),
(213, 21, '多级注册', 'MULTI_REGISTER', 2, '/platform/resource/register', 'platform/resource/Register', 'el-icon-connection', 3, 1, 1, 'resource:register:view'),
(214, 21, 'OSD批量设置', 'OSD_BATCH', 2, '/platform/resource/osd', 'platform/resource/Osd', 'el-icon-edit', 4, 1, 1, 'resource:osd:view'),

-- 系统设定二级菜单
(31, 4, '用户管理', 'USER_MANAGE', 2, '/system/user', 'system/User', 'el-icon-user', 1, 1, 1, 'system:user:view'),
(32, 4, '角色管理', 'ROLE_MANAGE', 2, '/system/role', 'system/Role', 'el-icon-s-custom', 2, 1, 1, 'system:role:view'),
(33, 4, '菜单管理', 'MENU_MANAGE', 2, '/system/menu', 'system/Menu', 'el-icon-menu', 3, 1, 1, 'system:menu:view'),
(34, 4, '部门管理', 'DEPT_MANAGE', 2, '/system/dept', 'system/Dept', 'el-icon-office-building', 4, 1, 1, 'system:dept:view'),
(35, 4, '系统配置', 'CONFIG_MANAGE', 2, '/system/config', 'system/Config', 'el-icon-setting', 5, 1, 1, 'system:config:view'),
(36, 4, '操作日志', 'LOG_MANAGE', 2, '/system/log', 'system/Log', 'el-icon-document', 6, 1, 1, 'system:log:view');

-- 插入角色菜单关联数据（超级管理员拥有所有权限）
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) 
SELECT 1, id FROM `sys_menu` WHERE `status` = 1;

-- 插入系统配置数据
INSERT INTO `sys_config` (`config_name`, `config_key`, `config_value`, `config_type`, `description`, `status`) VALUES
('系统名称', 'system.name', '标考高清网上巡查管理平台', 1, '系统名称配置', 1),
('系统版本', 'system.version', '3.0.0', 1, '系统版本号', 1),
('默认主题', 'system.theme', 'light', 1, '系统默认主题（light/dark）', 1),
('会话超时时间', 'system.session.timeout', '1800', 1, '会话超时时间（秒）', 1),
('文件上传大小限制', 'system.upload.maxSize', '10485760', 1, '文件上传大小限制（字节）', 1),
('密码最小长度', 'system.password.minLength', '6', 1, '用户密码最小长度', 1),
('登录失败锁定次数', 'system.login.maxRetry', '5', 1, '登录失败锁定账户的次数', 1),
('心跳检测间隔', 'patrol.heartbeat.interval', '30', 2, '设备心跳检测间隔（秒）', 1),
('录像保存天数', 'patrol.video.keepDays', '30', 2, '录像文件保存天数', 1),
('报警推送开关', 'patrol.alarm.push', 'true', 2, '报警信息推送开关', 1);
