-- 考点测试数据（包含省直属、市直属和区县考点）
-- 执行时间: 2024-07-24

USE `patrol_system`;

-- 添加更多省直属考点（具体学校名称）
INSERT INTO exam_sites (id, parent_id, parent_level, name, short_name, uri, address, contact_person, contact_phone, capacity, sort_order) VALUES
-- 四川省直属考点
('51002', '51', 'province', '成都市第四中学', '成都四中', 'http://exam.cd4z.net', '成都市青羊区西胜街6号', '孙校长', '028-87766215', 4500, 2),
('51003', '51', 'province', '四川大学附属中学', '川大附中', 'http://exam.scudfz.edu.cn', '成都市一环路南一段24号', '川大附中', '028-85405021', 6000, 3),
('51004', '51', 'province', '电子科技大学实验中学', '电子科大实验', 'http://exam.uestcsy.edu.cn', '成都市建设北路二段4号', '电子科大实验', '028-83202711', 5500, 4),

-- 北京市直属考点
('11002', '11', 'province', '北京师范大学第二附属中学', '北师大二附中', 'http://exam.bn2z.cn', '北京市西城区新街口外大街12号', '北师大二附中', '010-62354504', 5000, 2),
('11003', '11', 'province', '中国人民大学附属中学分校', '人大附中分校', 'http://exam.rdfzfx.cn', '北京市海淀区双榆树南里二区3号', '人大附中分校', '010-62512094', 4800, 3),

-- 上海市直属考点
('31002', '31', 'province', '复旦大学附属中学', '复旦附中', 'http://exam.fdfz.cn', '上海市杨浦区国权路383号', '复旦附中', '021-65640560', 5200, 2),
('31003', '31', 'province', '上海交通大学附属中学', '交大附中', 'http://exam.jdfz.edu.cn', '上海市宝山区殷高西路28号', '交大附中', '021-65911010', 5500, 3),

-- 广东省直属考点
('44002', '44', 'province', '中山大学附属中学', '中大附中', 'http://exam.sysudfz.cn', '广州市海珠区新港西路135号', '中大附中', '020-84037714', 6000, 2),
('44003', '44', 'province', '华南理工大学附属中学', '华工附中', 'http://exam.scut-sz.cn', '广州市天河区五山路华南理工大学内', '华工附中', '020-87110737', 5800, 3);

-- 添加更多地市直属考点（具体学校名称）
INSERT INTO exam_sites (id, parent_id, parent_level, name, short_name, uri, address, contact_person, contact_phone, capacity, sort_order) VALUES
-- 成都市直属考点
('5101002', '5101', 'city', '成都市实验外国语学校', '成实外', 'http://exam.cdsywy.cn', '成都市温江区花都大道东段489号', '成实外', '028-87909570', 3200, 2),
('5101003', '5101', 'city', '成都七中嘉祥外国语学校', '嘉祥外语', 'http://exam.jxfls.com', '成都市锦江区晨辉北路6号', '嘉祥外语', '028-66879779', 2800, 3),

-- 绵阳市直属考点
('5107001', '5107', 'city', '绵阳南山中学', '绵阳南山', 'http://exam.mynsms.com', '绵阳市涪城区南塔路3号', '绵阳南山', '0816-8073777', 2500, 1),
('5107002', '5107', 'city', '绵阳东辰国际学校', '绵阳东辰', 'http://exam.mydcis.net', '绵阳市涪城区剑门路西段科教园区', '绵阳东辰', '0816-2312069', 3000, 2),

-- 广州市直属考点
('4401002', '4401', 'city', '广州市执信中学', '执信中学', 'http://exam.zhixin.edu.cn', '广州市越秀区执信南路152号', '执信中学', '020-87765016', 3500, 2),
('4401003', '4401', 'city', '广州大学附属中学', '广大附中', 'http://exam.gzufz.edu.cn', '广州市大学城中六路', '广大附中', '020-39366232', 4000, 3),

-- 深圳市直属考点
('4403001', '4403', 'city', '深圳中学', '深圳中学', 'http://exam.shenzhong.net', '深圳市福田区人民北路深中街18号', '深圳中学', '0755-82222572', 4200, 1),
('4403002', '4403', 'city', '深圳外国语学校', '深外', 'http://exam.sfls.net.cn', '深圳市盐田区盐田路1号', '深外', '0755-25282036', 4500, 2),

-- 南京市直属考点
('3201002', '3201', 'city', '南京师范大学附属中学', '南师附中', 'http://exam.nsfz.net', '南京市鼓楼区察哈尔路37号', '南师附中', '025-83236404', 4800, 2),
('3201003', '3201', 'city', '南京外国语学校', '南外', 'http://exam.nfls.com.cn', '南京市玄武区北京东路30号', '南外', '025-83282300', 4200, 3),

-- 苏州市直属考点
('3205002', '3205', 'city', '苏州中学', '苏州中学', 'http://exam.szzx1000.com.cn', '苏州市姑苏区人民路699号', '苏州中学', '0512-65194261', 3800, 2),

-- 济南市直属考点
('3701002', '3701', 'city', '山东省实验中学', '省实验', 'http://exam.sdshiyan.cn', '济南市历下区经十路4515号', '省实验', '0531-87068188', 5000, 2),
('3701003', '3701', 'city', '济南外国语学校', '济外', 'http://exam.jnfls.com', '济南市天桥区师范路23号', '济外', '0531-86038036', 3600, 3);

-- 添加一些特殊类型的考点
INSERT INTO exam_sites (id, parent_id, parent_level, name, short_name, uri, address, contact_person, contact_phone, capacity, sort_order) VALUES
-- 军事院校考点（省直属）
('51005', '51', 'province', '中国人民解放军西部战区考试中心', '西部战区考试中心', 'http://exam.military.cn/west', '成都市金牛区蜀汉路256号', '军考中心', '028-87654321', 2000, 5),
('11004', '11', 'province', '中国人民解放军国防大学考试中心', '国防大学考试中心', 'http://exam.ndu.edu.cn', '北京市海淀区红山口甲3号', '国防大学', '010-66769999', 1500, 4),

-- 特殊教育考点（市直属）
('5101004', '5101', 'city', '成都市特殊教育考试中心', '特教考试中心', 'http://exam.cdtj.edu.cn', '成都市青羊区一环路西三段259号', '特教中心', '028-87661234', 500, 4),
('4401004', '4401', 'city', '广州市盲人学校考试中心', '盲校考试中心', 'http://exam.gzmx.edu.cn', '广州市天河区龙洞街245号', '盲校考试中心', '020-87654321', 300, 4),

-- 职业技术学院考点（市直属）
('5101005', '5101', 'city', '成都职业技术学院考试中心', '成职院考试中心', 'http://exam.cdp.edu.cn', '成都市高新区天益街83号', '成职院考试中心', '028-85327933', 2200, 5),
('4401005', '4401', 'city', '广州番禺职业技术学院考试中心', '番职院考试中心', 'http://exam.gzpyp.edu.cn', '广州市番禺区市良路1342号', '番职院考试中心', '020-84731234', 2500, 5);

-- 验证插入的数据
SELECT 
    parent_level,
    COUNT(*) as count,
    GROUP_CONCAT(DISTINCT LEFT(parent_id, 2)) as provinces_or_cities
FROM exam_sites 
GROUP BY parent_level 
ORDER BY 
    CASE parent_level 
        WHEN 'province' THEN 1 
        WHEN 'city' THEN 2 
        WHEN 'district' THEN 3 
    END;
