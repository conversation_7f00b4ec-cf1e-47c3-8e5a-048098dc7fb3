import{_ as m}from"./_plugin-vue_export-helper-fs8hP-CV.js";/* empty css                  */import{b as f,H as v,i as r,j as g,o as k,q as s,l as t,w as e,p as B,M as c,a5 as x}from"./index-DKnB9mwy.js";const C={class:"error-page"},H={class:"error-content"},b={class:"error-image"},E={class:"error-icon"},N={class:"error-info"},V={class:"error-actions"},w=f({__name:"404",setup(y){const a=v(),i=()=>{a.push("/")},l=()=>{a.go(-1)};return(I,o)=>{const d=r("Warning"),n=B,p=r("House"),_=x,u=r("Back");return k(),g("div",C,[s("div",H,[s("div",b,[s("div",E,[t(n,{size:120},{default:e(()=>[t(d)]),_:1})])]),s("div",N,[o[2]||(o[2]=s("h1",{class:"error-title"},"404",-1)),o[3]||(o[3]=s("h2",{class:"error-subtitle"},"页面未找到",-1)),o[4]||(o[4]=s("p",{class:"error-description"}," 抱歉，您访问的页面不存在或已被删除。 ",-1)),s("div",V,[t(_,{type:"primary",onClick:i},{default:e(()=>[t(n,null,{default:e(()=>[t(p)]),_:1}),o[0]||(o[0]=c(" 返回首页 ",-1))]),_:1,__:[0]}),t(_,{onClick:l},{default:e(()=>[t(n,null,{default:e(()=>[t(u)]),_:1}),o[1]||(o[1]=c(" 返回上页 ",-1))]),_:1,__:[1]})])])])])}}}),q=m(w,[["__scopeId","data-v-0c277d9e"]]);export{q as default};
