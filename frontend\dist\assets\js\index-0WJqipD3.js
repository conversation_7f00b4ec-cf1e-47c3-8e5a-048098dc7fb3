var _e=Object.defineProperty;var q=Object.getOwnPropertySymbols;var ve=Object.prototype.hasOwnProperty,fe=Object.prototype.propertyIsEnumerable;var j=(p,u,i)=>u in p?_e(p,u,{enumerable:!0,configurable:!0,writable:!0,value:i}):p[u]=i,M=(p,u)=>{for(var i in u||(u={}))ve.call(u,i)&&j(p,i,u[i]);if(q)for(var i of q(u))fe.call(u,i)&&j(p,i,u[i]);return p};var x=(p,u,i)=>new Promise((h,_)=>{var o=c=>{try{f(i.next(c))}catch(d){_(d)}},b=c=>{try{f(i.throw(c))}catch(d){_(d)}},f=c=>c.done?h(c.value):Promise.resolve(c.value).then(o,b);f((i=i.apply(p,u)).next())});import{_ as L}from"./_plugin-vue_export-helper-fs8hP-CV.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                     *//* empty css                  */import{b as O,h as y,_ as Q,af as ge,j as K,o as z,l as e,q as D,a6 as G,u as a,w as t,$ as X,a0 as J,ag as W,ah as Y,a5 as Z,M as m,G as V,C as ye,Q as be,a2 as Ce,e as R,ai as Ve,aj as he,ad as we,t as F,ak as Se,al as Te,a9 as Ne,k as xe,X as ke,am as Ee,N as H}from"./index-DKnB9mwy.js";const ze={class:"device-form"},De={class:"form-actions"},Ae=O({__name:"DeviceForm",props:{device:{default:null},mode:{default:"add"}},emits:["submit","cancel"],setup(p,{emit:u}){const i=p,h=u,_=y(),o=Q({deviceName:"",deviceCode:"",deviceType:null,ipAddress:"",location:""}),b={deviceName:[{required:!0,message:"请输入设备名称",trigger:"blur"}],deviceCode:[{required:!0,message:"请输入设备编码",trigger:"blur"}],deviceType:[{required:!0,message:"请选择设备类型",trigger:"change"}],ipAddress:[{required:!0,message:"请输入IP地址",trigger:"blur"}]},f=()=>x(this,null,function*(){if(_.value)try{yield _.value.validate(),V.success("保存成功"),h("submit")}catch(d){V.error("请检查表单数据")}}),c=()=>{h("cancel")};return ge(()=>{i.device&&Object.assign(o,i.device)}),(d,n)=>{const w=J,S=X,T=Y,A=W,B=G,k=Z;return z(),K("div",ze,[e(B,{ref_key:"formRef",ref:_,model:a(o),rules:b,"label-width":"120px"},{default:t(()=>[e(S,{label:"设备名称",prop:"deviceName"},{default:t(()=>[e(w,{modelValue:a(o).deviceName,"onUpdate:modelValue":n[0]||(n[0]=v=>a(o).deviceName=v),placeholder:"请输入设备名称"},null,8,["modelValue"])]),_:1}),e(S,{label:"设备编码",prop:"deviceCode"},{default:t(()=>[e(w,{modelValue:a(o).deviceCode,"onUpdate:modelValue":n[1]||(n[1]=v=>a(o).deviceCode=v),placeholder:"请输入设备编码"},null,8,["modelValue"])]),_:1}),e(S,{label:"设备类型",prop:"deviceType"},{default:t(()=>[e(A,{modelValue:a(o).deviceType,"onUpdate:modelValue":n[2]||(n[2]=v=>a(o).deviceType=v),placeholder:"请选择设备类型"},{default:t(()=>[e(T,{label:"摄像头",value:1}),e(T,{label:"录像机",value:2}),e(T,{label:"存储设备",value:3})]),_:1},8,["modelValue"])]),_:1}),e(S,{label:"IP地址",prop:"ipAddress"},{default:t(()=>[e(w,{modelValue:a(o).ipAddress,"onUpdate:modelValue":n[3]||(n[3]=v=>a(o).ipAddress=v),placeholder:"请输入IP地址"},null,8,["modelValue"])]),_:1}),e(S,{label:"安装位置",prop:"location"},{default:t(()=>[e(w,{modelValue:a(o).location,"onUpdate:modelValue":n[4]||(n[4]=v=>a(o).location=v),placeholder:"请输入安装位置"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),D("div",De,[e(k,{onClick:c},{default:t(()=>n[5]||(n[5]=[m("取消",-1)])),_:1,__:[5]}),e(k,{type:"primary",onClick:f},{default:t(()=>n[6]||(n[6]=[m("确定",-1)])),_:1,__:[6]})])])}}}),Be=L(Ae,[["__scopeId","data-v-70c1f677"]]),Me={class:"app-container"},Ue={class:"filter-container"},Ie={class:"toolbar-container"},Pe={class:"pagination-container"},$e=O({__name:"index",setup(p){const u=y(!1),i=y([]),h=y(0),_=y([]),o=y(!1),b=y(""),f=y("add"),c=y(null),d=Q({pageNum:1,pageSize:20,deviceName:"",deviceType:null,onlineStatus:null}),n=()=>x(this,null,function*(){u.value=!0;try{i.value=[{id:1,deviceName:"前门摄像头",deviceCode:"CAM-001",deviceType:1,ipAddress:"*************",location:"大门入口",onlineStatus:1,lastHeartbeat:"2024-07-22 18:30:00"},{id:2,deviceName:"后门摄像头",deviceCode:"CAM-002",deviceType:1,ipAddress:"*************",location:"后门出口",onlineStatus:0,lastHeartbeat:"2024-07-22 17:45:00"}],h.value=2}catch(r){V.error("获取设备列表失败")}finally{u.value=!1}}),w=()=>{d.pageNum=1,n()},S=()=>{Object.assign(d,{pageNum:1,pageSize:20,deviceName:"",deviceType:null,onlineStatus:null}),n()},T=()=>{b.value="新增设备",f.value="add",c.value=null,o.value=!0},A=r=>{b.value="编辑设备",f.value="edit",c.value=M({},r),o.value=!0},B=r=>{b.value="设备详情",f.value="view",c.value=M({},r),o.value=!0},k=r=>x(this,null,function*(){try{yield H.confirm("确定要删除该设备吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),V.success("删除成功"),n()}catch(l){l!=="cancel"&&V.error("删除失败")}}),v=()=>x(this,null,function*(){try{yield H.confirm(`确定要删除选中的 ${_.value.length} 个设备吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const r=_.value.map(l=>l.id);V.success("批量删除成功"),n()}catch(r){r!=="cancel"&&V.error("批量删除失败")}}),ee=()=>{V.info("导出功能开发中...")},le=r=>{_.value=r},te=r=>{d.pageSize=r,n()},ae=r=>{d.pageNum=r,n()},U=()=>{o.value=!1,c.value=null},oe=()=>{o.value=!1,n()},ne=r=>({1:"摄像头",2:"录像机",3:"存储设备"})[r]||"未知",ie=r=>({1:"primary",2:"success",3:"warning"})[r]||"info";return ye(()=>{n()}),(r,l)=>{const de=J,E=X,N=Y,I=W,C=Z,se=G,g=he,P=we,re=Ve,ue=Te,ce=Ne,pe=Ee,me=Se;return z(),K("div",Me,[e(ce,{shadow:"hover"},{default:t(()=>[D("div",Ue,[e(se,{inline:!0,model:a(d),class:"demo-form-inline"},{default:t(()=>[e(E,{label:"设备名称"},{default:t(()=>[e(de,{modelValue:a(d).deviceName,"onUpdate:modelValue":l[0]||(l[0]=s=>a(d).deviceName=s),placeholder:"请输入设备名称",clearable:"",style:{width:"200px"},onKeyup:Ce(w,["enter"])},null,8,["modelValue"])]),_:1}),e(E,{label:"设备类型"},{default:t(()=>[e(I,{modelValue:a(d).deviceType,"onUpdate:modelValue":l[1]||(l[1]=s=>a(d).deviceType=s),placeholder:"请选择设备类型",clearable:"",style:{width:"150px"}},{default:t(()=>[e(N,{label:"摄像头",value:1}),e(N,{label:"录像机",value:2}),e(N,{label:"存储设备",value:3})]),_:1},8,["modelValue"])]),_:1}),e(E,{label:"在线状态"},{default:t(()=>[e(I,{modelValue:a(d).onlineStatus,"onUpdate:modelValue":l[2]||(l[2]=s=>a(d).onlineStatus=s),placeholder:"请选择状态",clearable:"",style:{width:"120px"}},{default:t(()=>[e(N,{label:"在线",value:1}),e(N,{label:"离线",value:0})]),_:1},8,["modelValue"])]),_:1}),e(E,null,{default:t(()=>[e(C,{type:"primary",icon:"Search",onClick:w},{default:t(()=>l[6]||(l[6]=[m("搜索",-1)])),_:1,__:[6]}),e(C,{icon:"Refresh",onClick:S},{default:t(()=>l[7]||(l[7]=[m("重置",-1)])),_:1,__:[7]})]),_:1})]),_:1},8,["model"])]),D("div",Ie,[e(C,{type:"primary",icon:"Plus",onClick:T},{default:t(()=>l[8]||(l[8]=[m("新增设备",-1)])),_:1,__:[8]}),e(C,{type:"success",icon:"Download",onClick:ee},{default:t(()=>l[9]||(l[9]=[m("导出",-1)])),_:1,__:[9]}),e(C,{type:"danger",icon:"Delete",disabled:!a(_).length,onClick:v},{default:t(()=>l[10]||(l[10]=[m(" 批量删除 ",-1)])),_:1,__:[10]},8,["disabled"])]),be((z(),R(re,{data:a(i),onSelectionChange:le,style:{width:"100%"}},{default:t(()=>[e(g,{type:"selection",width:"55"}),e(g,{prop:"deviceName",label:"设备名称","min-width":"120"}),e(g,{prop:"deviceCode",label:"设备编码","min-width":"120"}),e(g,{prop:"deviceType",label:"设备类型",width:"100"},{default:t(({row:s})=>[e(P,{type:ie(s.deviceType)},{default:t(()=>[m(F(ne(s.deviceType)),1)]),_:2},1032,["type"])]),_:1}),e(g,{prop:"ipAddress",label:"IP地址",width:"120"}),e(g,{prop:"location",label:"安装位置","min-width":"150"}),e(g,{prop:"onlineStatus",label:"在线状态",width:"100"},{default:t(({row:s})=>[e(P,{type:s.onlineStatus===1?"success":"danger"},{default:t(()=>[m(F(s.onlineStatus===1?"在线":"离线"),1)]),_:2},1032,["type"])]),_:1}),e(g,{prop:"lastHeartbeat",label:"最后心跳",width:"160"}),e(g,{label:"操作",width:"200",fixed:"right"},{default:t(({row:s})=>[e(C,{type:"primary",size:"small",onClick:$=>A(s)},{default:t(()=>l[11]||(l[11]=[m("编辑",-1)])),_:2,__:[11]},1032,["onClick"]),e(C,{type:"success",size:"small",onClick:$=>B(s)},{default:t(()=>l[12]||(l[12]=[m("查看",-1)])),_:2,__:[12]},1032,["onClick"]),e(C,{type:"danger",size:"small",onClick:$=>k(s)},{default:t(()=>l[13]||(l[13]=[m("删除",-1)])),_:2,__:[13]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[me,a(u)]]),D("div",Pe,[e(ue,{"current-page":a(d).pageNum,"onUpdate:currentPage":l[3]||(l[3]=s=>a(d).pageNum=s),"page-size":a(d).pageSize,"onUpdate:pageSize":l[4]||(l[4]=s=>a(d).pageSize=s),total:a(h),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:te,onCurrentChange:ae},null,8,["current-page","page-size","total"])])]),_:1}),e(pe,{modelValue:a(o),"onUpdate:modelValue":l[5]||(l[5]=s=>ke(o)?o.value=s:null),title:a(b),width:"800px",onClose:U},{default:t(()=>[a(o)?(z(),R(Be,{key:0,device:a(c),mode:a(f),onSubmit:oe,onCancel:U},null,8,["device","mode"])):xe("",!0)]),_:1},8,["modelValue","title"])])}}}),al=L($e,[["__scopeId","data-v-cb49433c"]]);export{al as default};
