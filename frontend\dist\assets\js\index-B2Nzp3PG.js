import{_ as r}from"./_plugin-vue_export-helper-fs8hP-CV.js";/* empty css                *//* empty css                 */import{b as c,j as _,o as d,l as a,w as o,q as e,ae as p,a9 as i}from"./index-DKnB9mwy.js";const l={class:"app-container"},m={class:"page-content"},f=c({__name:"index",setup(h){return(u,t)=>{const s=p,n=i;return d(),_("div",l,[a(n,{shadow:"hover"},{header:o(()=>t[0]||(t[0]=[e("div",{class:"card-header"},[e("span",null,"操作日志")],-1)])),default:o(()=>[e("div",m,[a(s,{description:"操作日志功能开发中..."})])]),_:1})])}}}),B=r(f,[["__scopeId","data-v-0a5ef0b2"]]);export{B as default};
