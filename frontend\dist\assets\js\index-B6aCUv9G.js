var U=(h,_,i)=>new Promise((g,b)=>{var u=s=>{try{m(i.next(s))}catch(v){b(v)}},d=s=>{try{m(i.throw(s))}catch(v){b(v)}},m=s=>s.done?g(s.value):Promise.resolve(s.value).then(u,d);m((i=i.apply(h,_)).next())});import{_ as G}from"./_plugin-vue_export-helper-fs8hP-CV.js";/* empty css                   *//* empty css               *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                *//* empty css                  *//* empty css                *//* empty css                    */import{b as O,h as E,_ as w,C as Q,i as R,j as V,o as y,l,w as a,a8 as $,a9 as A,an as H,u as t,q as n,p as J,t as N,Q as X,k as x,a6 as Y,$ as Z,a0 as ee,ao as le,W as te,ag as oe,ah as ae,ae as ne,ak as se,a5 as re,M as ie,a7 as ue,G as c}from"./index-DKnB9mwy.js";const de={class:"app-container"},me={class:"custom-tree-node"},pe={style:{"margin-left":"8px"}},fe={class:"card-header"},_e={class:"param-content"},Ve={key:0,class:"param-section"},ye={key:1,class:"param-section"},ge={key:2,class:"param-section"},ve={key:3,class:"param-section"},xe={key:4,class:"empty-content"},be=O({__name:"index",setup(h){const _=E(!1),i=E({}),g=E([{id:1,label:"系统参数",key:"system",children:[]},{id:2,label:"巡查参数",key:"patrol",children:[]},{id:3,label:"网络参数",key:"network",children:[]},{id:4,label:"存储参数",key:"storage",children:[]}]),b={children:"children",label:"label"},u=w({systemName:"标考高清网上巡查管理平台",systemVersion:"3.0.0",sessionTimeout:1800,uploadMaxSize:10,passwordMinLength:6}),d=w({heartbeatInterval:30,videoKeepDays:30,alarmPush:!0,autoPatrolInterval:300,offlineThreshold:60}),m=w({connectTimeout:5e3,readTimeout:1e4,retryCount:3,retryInterval:2e3}),s=w({storagePath:"/data/patrol",diskWarningThreshold:80,autoCleanup:!0,cleanupStrategy:"time"}),v=f=>{i.value=f,P(f.key)},P=f=>U(this,null,function*(){_.value=!0;try{yield new Promise(e=>setTimeout(e,500))}catch(e){c.error("加载参数失败")}finally{_.value=!1}}),z=()=>U(this,null,function*(){try{_.value=!0;let f={};switch(i.value.key){case"system":f=u;break;case"patrol":f=d;break;case"network":f=m;break;case"storage":f=s;break;default:c.warning("请先选择参数分类");return}c.success("参数保存成功")}catch(f){c.error("参数保存失败")}finally{_.value=!1}});return Q(()=>{g.value.length>0&&(i.value=g.value[0],P(i.value.key))}),(f,e)=>{const B=R("Setting"),D=J,K=H,I=A,S=$,L=re,C=ee,r=Z,p=le,k=Y,M=te,T=ae,W=oe,F=ne,j=ue,q=se;return y(),V("div",de,[l(j,{gutter:20},{default:a(()=>[l(S,{span:6},{default:a(()=>[l(I,{shadow:"hover"},{header:a(()=>e[18]||(e[18]=[n("div",{class:"card-header"},[n("span",null,"参数分类")],-1)])),default:a(()=>[l(K,{data:t(g),props:b,"node-key":"id","default-expand-all":!0,"highlight-current":!0,onNodeClick:v},{default:a(({node:o,data:ke})=>[n("span",me,[l(D,null,{default:a(()=>[l(B)]),_:1}),n("span",pe,N(o.label),1)])]),_:1},8,["data"])]),_:1})]),_:1}),l(S,{span:18},{default:a(()=>[l(I,{shadow:"hover"},{header:a(()=>[n("div",fe,[n("span",null,N(t(i).name||"参数配置"),1),l(L,{type:"primary",size:"small",onClick:z},{default:a(()=>e[19]||(e[19]=[ie("保存配置",-1)])),_:1,__:[19]})])]),default:a(()=>[X((y(),V("div",_e,[t(i).key==="system"?(y(),V("div",Ve,[e[23]||(e[23]=n("h3",null,"系统基础参数",-1)),l(k,{model:t(u),"label-width":"150px"},{default:a(()=>[l(r,{label:"系统名称"},{default:a(()=>[l(C,{modelValue:t(u).systemName,"onUpdate:modelValue":e[0]||(e[0]=o=>t(u).systemName=o),placeholder:"请输入系统名称"},null,8,["modelValue"])]),_:1}),l(r,{label:"系统版本"},{default:a(()=>[l(C,{modelValue:t(u).systemVersion,"onUpdate:modelValue":e[1]||(e[1]=o=>t(u).systemVersion=o),placeholder:"请输入系统版本",readonly:""},null,8,["modelValue"])]),_:1}),l(r,{label:"会话超时时间"},{default:a(()=>[l(p,{modelValue:t(u).sessionTimeout,"onUpdate:modelValue":e[2]||(e[2]=o=>t(u).sessionTimeout=o),min:300,max:7200,step:60,"controls-position":"right"},null,8,["modelValue"]),e[20]||(e[20]=n("span",{class:"input-suffix"},"秒",-1))]),_:1,__:[20]}),l(r,{label:"文件上传大小限制"},{default:a(()=>[l(p,{modelValue:t(u).uploadMaxSize,"onUpdate:modelValue":e[3]||(e[3]=o=>t(u).uploadMaxSize=o),min:1,max:100,"controls-position":"right"},null,8,["modelValue"]),e[21]||(e[21]=n("span",{class:"input-suffix"},"MB",-1))]),_:1,__:[21]}),l(r,{label:"密码最小长度"},{default:a(()=>[l(p,{modelValue:t(u).passwordMinLength,"onUpdate:modelValue":e[4]||(e[4]=o=>t(u).passwordMinLength=o),min:6,max:20,"controls-position":"right"},null,8,["modelValue"]),e[22]||(e[22]=n("span",{class:"input-suffix"},"位",-1))]),_:1,__:[22]})]),_:1},8,["model"])])):x("",!0),t(i).key==="patrol"?(y(),V("div",ye,[e[28]||(e[28]=n("h3",null,"巡查业务参数",-1)),l(k,{model:t(d),"label-width":"150px"},{default:a(()=>[l(r,{label:"心跳检测间隔"},{default:a(()=>[l(p,{modelValue:t(d).heartbeatInterval,"onUpdate:modelValue":e[5]||(e[5]=o=>t(d).heartbeatInterval=o),min:10,max:300,step:10,"controls-position":"right"},null,8,["modelValue"]),e[24]||(e[24]=n("span",{class:"input-suffix"},"秒",-1))]),_:1,__:[24]}),l(r,{label:"录像保存天数"},{default:a(()=>[l(p,{modelValue:t(d).videoKeepDays,"onUpdate:modelValue":e[6]||(e[6]=o=>t(d).videoKeepDays=o),min:1,max:365,"controls-position":"right"},null,8,["modelValue"]),e[25]||(e[25]=n("span",{class:"input-suffix"},"天",-1))]),_:1,__:[25]}),l(r,{label:"报警推送开关"},{default:a(()=>[l(M,{modelValue:t(d).alarmPush,"onUpdate:modelValue":e[7]||(e[7]=o=>t(d).alarmPush=o),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1}),l(r,{label:"自动巡查间隔"},{default:a(()=>[l(p,{modelValue:t(d).autoPatrolInterval,"onUpdate:modelValue":e[8]||(e[8]=o=>t(d).autoPatrolInterval=o),min:60,max:3600,step:60,"controls-position":"right"},null,8,["modelValue"]),e[26]||(e[26]=n("span",{class:"input-suffix"},"秒",-1))]),_:1,__:[26]}),l(r,{label:"设备离线阈值"},{default:a(()=>[l(p,{modelValue:t(d).offlineThreshold,"onUpdate:modelValue":e[9]||(e[9]=o=>t(d).offlineThreshold=o),min:30,max:600,step:30,"controls-position":"right"},null,8,["modelValue"]),e[27]||(e[27]=n("span",{class:"input-suffix"},"秒",-1))]),_:1,__:[27]})]),_:1},8,["model"])])):x("",!0),t(i).key==="network"?(y(),V("div",ge,[e[33]||(e[33]=n("h3",null,"网络连接参数",-1)),l(k,{model:t(m),"label-width":"150px"},{default:a(()=>[l(r,{label:"连接超时时间"},{default:a(()=>[l(p,{modelValue:t(m).connectTimeout,"onUpdate:modelValue":e[10]||(e[10]=o=>t(m).connectTimeout=o),min:1e3,max:3e4,step:1e3,"controls-position":"right"},null,8,["modelValue"]),e[29]||(e[29]=n("span",{class:"input-suffix"},"毫秒",-1))]),_:1,__:[29]}),l(r,{label:"读取超时时间"},{default:a(()=>[l(p,{modelValue:t(m).readTimeout,"onUpdate:modelValue":e[11]||(e[11]=o=>t(m).readTimeout=o),min:1e3,max:6e4,step:1e3,"controls-position":"right"},null,8,["modelValue"]),e[30]||(e[30]=n("span",{class:"input-suffix"},"毫秒",-1))]),_:1,__:[30]}),l(r,{label:"重试次数"},{default:a(()=>[l(p,{modelValue:t(m).retryCount,"onUpdate:modelValue":e[12]||(e[12]=o=>t(m).retryCount=o),min:0,max:10,"controls-position":"right"},null,8,["modelValue"]),e[31]||(e[31]=n("span",{class:"input-suffix"},"次",-1))]),_:1,__:[31]}),l(r,{label:"重试间隔"},{default:a(()=>[l(p,{modelValue:t(m).retryInterval,"onUpdate:modelValue":e[13]||(e[13]=o=>t(m).retryInterval=o),min:1e3,max:1e4,step:1e3,"controls-position":"right"},null,8,["modelValue"]),e[32]||(e[32]=n("span",{class:"input-suffix"},"毫秒",-1))]),_:1,__:[32]})]),_:1},8,["model"])])):x("",!0),t(i).key==="storage"?(y(),V("div",ve,[e[35]||(e[35]=n("h3",null,"存储管理参数",-1)),l(k,{model:t(s),"label-width":"150px"},{default:a(()=>[l(r,{label:"存储路径"},{default:a(()=>[l(C,{modelValue:t(s).storagePath,"onUpdate:modelValue":e[14]||(e[14]=o=>t(s).storagePath=o),placeholder:"请输入存储路径"},null,8,["modelValue"])]),_:1}),l(r,{label:"磁盘空间警告阈值"},{default:a(()=>[l(p,{modelValue:t(s).diskWarningThreshold,"onUpdate:modelValue":e[15]||(e[15]=o=>t(s).diskWarningThreshold=o),min:10,max:95,step:5,"controls-position":"right"},null,8,["modelValue"]),e[34]||(e[34]=n("span",{class:"input-suffix"},"%",-1))]),_:1,__:[34]}),l(r,{label:"自动清理开关"},{default:a(()=>[l(M,{modelValue:t(s).autoCleanup,"onUpdate:modelValue":e[16]||(e[16]=o=>t(s).autoCleanup=o),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1}),l(r,{label:"清理策略"},{default:a(()=>[l(W,{modelValue:t(s).cleanupStrategy,"onUpdate:modelValue":e[17]||(e[17]=o=>t(s).cleanupStrategy=o),placeholder:"请选择清理策略"},{default:a(()=>[l(T,{label:"按时间清理",value:"time"}),l(T,{label:"按大小清理",value:"size"}),l(T,{label:"按数量清理",value:"count"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])])):x("",!0),t(i).key?x("",!0):(y(),V("div",xe,[l(F,{description:"请选择左侧参数分类进行配置"})]))])),[[q,t(_)]])]),_:1})]),_:1})]),_:1})])}}}),We=G(be,[["__scopeId","data-v-729e54af"]]);export{We as default};
