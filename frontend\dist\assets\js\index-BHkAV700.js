import{_ as M}from"./_plugin-vue_export-helper-fs8hP-CV.js";/* empty css                  *//* empty css                *//* empty css               */import{u as O,H as k,i as z,a as H,b as L,c as R,d as W,e as j,f as K}from"./index.esm.min-R8FKBtQi.js";import{b as Q,H as X,z as G,h as J,c as P,_ as Y,C as Z,i as m,j as p,o as r,q as t,l as s,w as e,t as n,u as l,a7 as tt,a8 as st,p as et,a9 as ot,F as f,s as h,e as at,r as nt,aa as it,ab as lt,M as C,a5 as ct}from"./index-DKnB9mwy.js";const dt={class:"dashboard-container"},rt={class:"welcome-card"},_t={class:"welcome-content"},ut={class:"welcome-text"},mt={class:"welcome-stats"},pt={class:"stat-item"},vt={class:"stat-info"},ft={class:"stat-value"},ht={class:"stat-item"},Ct={class:"stat-info"},gt={class:"stat-value"},yt={class:"stat-item"},wt={class:"stat-info"},kt={class:"stat-value"},xt={class:"stat-item"},Et={class:"stat-info"},bt={class:"stat-value"},At={class:"quick-actions"},St={class:"action-content"},$t={class:"action-info"},Dt={class:"action-name"},Ft={class:"action-desc"},Tt={class:"chart-container"},Bt={class:"chart-container"},Nt={class:"card-header"},Vt={class:"card-header"},Ut={class:"notice-list"},It={class:"notice-title"},qt={class:"notice-time"},Mt=Q({__name:"index",setup(Ot){O([z,H,L,R,W,j,K]);const x=X(),E=G(),g=J(""),y=()=>{g.value=new Date().toLocaleString("zh-CN")},b=P(()=>E.userName),_=Y({onlineDevices:156,todayTasks:23,alarms:5,onlineUsers:12}),A=[{name:"实时监控",description:"查看设备实时状态",icon:"VideoCamera",color:"#409EFF",path:"/patrol/monitor"},{name:"巡查任务",description:"管理巡查任务",icon:"Document",color:"#67C23A",path:"/patrol/task"},{name:"设备管理",description:"管理监控设备",icon:"Cpu",color:"#E6A23C",path:"/platform/resource/list"},{name:"系统设置",description:"系统参数配置",icon:"Setting",color:"#F56C6C",path:"/system/config"}],S={tooltip:{trigger:"item"},legend:{orient:"vertical",left:"left"},series:[{name:"设备状态",type:"pie",radius:"50%",data:[{value:156,name:"在线"},{value:12,name:"离线"},{value:3,name:"故障"}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]},$={tooltip:{trigger:"axis"},xAxis:{type:"category",data:["周一","周二","周三","周四","周五","周六","周日"]},yAxis:{type:"value"},series:[{data:[23,34,28,45,32,29,38],type:"line",smooth:!0}]},D=[{id:1,message:"设备 CAM-001 离线",time:"2024-07-22 14:30:00",type:"danger"},{id:2,message:"存储空间不足警告",time:"2024-07-22 13:15:00",type:"warning"},{id:3,message:"巡查任务 TASK-001 完成",time:"2024-07-22 12:00:00",type:"success"}],F=[{id:1,title:"系统维护通知",time:"2024-07-22"},{id:2,title:"新功能上线公告",time:"2024-07-21"},{id:3,title:"安全更新提醒",time:"2024-07-20"}],T=v=>{x.push(v.path)};return Z(()=>{y(),setInterval(y,1e3)}),(v,o)=>{const B=m("VideoCamera"),d=et,i=st,N=m("Document"),V=m("Warning"),U=m("User"),u=tt,c=ot,w=ct,I=lt,q=it;return r(),p("div",dt,[t("div",rt,[s(c,{shadow:"hover"},{default:e(()=>[t("div",_t,[t("div",ut,[o[1]||(o[1]=t("h2",null,"欢迎使用标考高清网上巡查管理平台",-1)),t("p",null,"当前时间："+n(l(g)),1),t("p",null,"登录用户："+n(l(b)),1)]),t("div",mt,[s(u,{gutter:20},{default:e(()=>[s(i,{span:6},{default:e(()=>[t("div",pt,[s(d,{class:"stat-icon",color:"#409EFF"},{default:e(()=>[s(B)]),_:1}),t("div",vt,[t("div",ft,n(l(_).onlineDevices),1),o[2]||(o[2]=t("div",{class:"stat-label"},"在线设备",-1))])])]),_:1}),s(i,{span:6},{default:e(()=>[t("div",ht,[s(d,{class:"stat-icon",color:"#67C23A"},{default:e(()=>[s(N)]),_:1}),t("div",Ct,[t("div",gt,n(l(_).todayTasks),1),o[3]||(o[3]=t("div",{class:"stat-label"},"今日任务",-1))])])]),_:1}),s(i,{span:6},{default:e(()=>[t("div",yt,[s(d,{class:"stat-icon",color:"#E6A23C"},{default:e(()=>[s(V)]),_:1}),t("div",wt,[t("div",kt,n(l(_).alarms),1),o[4]||(o[4]=t("div",{class:"stat-label"},"报警信息",-1))])])]),_:1}),s(i,{span:6},{default:e(()=>[t("div",xt,[s(d,{class:"stat-icon",color:"#F56C6C"},{default:e(()=>[s(U)]),_:1}),t("div",Et,[t("div",bt,n(l(_).onlineUsers),1),o[5]||(o[5]=t("div",{class:"stat-label"},"在线用户",-1))])])]),_:1})]),_:1})])])]),_:1})]),t("div",At,[s(u,{gutter:20},{default:e(()=>[(r(),p(f,null,h(A,a=>s(i,{span:6,key:a.name},{default:e(()=>[s(c,{shadow:"hover",class:"action-card",onClick:zt=>T(a)},{default:e(()=>[t("div",St,[s(d,{class:"action-icon",color:a.color},{default:e(()=>[(r(),at(nt(a.icon)))]),_:2},1032,["color"]),t("div",$t,[t("div",Dt,n(a.name),1),t("div",Ft,n(a.description),1)])])]),_:2},1032,["onClick"])]),_:2},1024)),64))]),_:1})]),s(u,{gutter:20,class:"monitor-section"},{default:e(()=>[s(i,{span:12},{default:e(()=>[s(c,{shadow:"hover"},{header:e(()=>o[6]||(o[6]=[t("div",{class:"card-header"},[t("span",null,"设备状态分布")],-1)])),default:e(()=>[t("div",Tt,[s(l(k),{class:"chart",option:S})])]),_:1})]),_:1}),s(i,{span:12},{default:e(()=>[s(c,{shadow:"hover"},{header:e(()=>o[7]||(o[7]=[t("div",{class:"card-header"},[t("span",null,"最近7天巡查统计")],-1)])),default:e(()=>[t("div",Bt,[s(l(k),{class:"chart",option:$})])]),_:1})]),_:1})]),_:1}),s(u,{gutter:20,class:"activity-section"},{default:e(()=>[s(i,{span:12},{default:e(()=>[s(c,{shadow:"hover"},{header:e(()=>[t("div",Nt,[o[9]||(o[9]=t("span",null,"最新报警",-1)),s(w,{type:"text",onClick:o[0]||(o[0]=a=>v.$router.push("/patrol/alarm"))},{default:e(()=>o[8]||(o[8]=[C("查看更多",-1)])),_:1,__:[8]})])]),default:e(()=>[s(q,null,{default:e(()=>[(r(),p(f,null,h(D,a=>s(I,{key:a.id,timestamp:a.time,type:a.type},{default:e(()=>[C(n(a.message),1)]),_:2},1032,["timestamp","type"])),64))]),_:1})]),_:1})]),_:1}),s(i,{span:12},{default:e(()=>[s(c,{shadow:"hover"},{header:e(()=>[t("div",Vt,[o[11]||(o[11]=t("span",null,"系统公告",-1)),s(w,{type:"text"},{default:e(()=>o[10]||(o[10]=[C("查看更多",-1)])),_:1,__:[10]})])]),default:e(()=>[t("div",Ut,[(r(),p(f,null,h(F,a=>t("div",{key:a.id,class:"notice-item"},[t("div",It,n(a.title),1),t("div",qt,n(a.time),1)])),64))])]),_:1})]),_:1})]),_:1})])}}}),Qt=M(Mt,[["__scopeId","data-v-408b5704"]]);export{Qt as default};
