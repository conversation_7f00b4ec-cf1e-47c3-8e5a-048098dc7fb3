var q=(F,C,h)=>new Promise((_,S)=>{var b=i=>{try{p(h.next(i))}catch(l){S(l)}},V=i=>{try{p(h.throw(i))}catch(l){S(l)}},p=i=>i.done?_(i.value):Promise.resolve(i.value).then(b,V);p((h=h.apply(F,C)).next())});import{_ as K}from"./_plugin-vue_export-helper-fs8hP-CV.js";/* empty css                     *//* empty css                  *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                 */import{b as Y,h as t,C as W,i as T,j as Z,o as I,q as s,u as a,U as A,l as o,p as J,w as r,m as ne,t as re,V as j,H as ie,x as ue,z as ce,y as de,_ as me,O as pe,$ as fe,a0 as ve,a1 as _e,X as ge,e as H,a2 as he,a3 as ze,M as O,a4 as ye,a5 as ke,R as we,a6 as Re,G as P}from"./index-DKnB9mwy.js";import{T as be}from"./index-DrnBe4fN.js";const xe={class:"puzzle-captcha"},Me={class:"puzzle-container"},Ee=["src"],Te={class:"slider-container"},$=300,L=150,G=42,Ce=5,Se=Y({__name:"index",emits:["success","fail"],setup(F,{emit:C}){const h=C,_=t(),S=t(),b=t(),V=t(),p=t(""),i=t(0),l=t(0),z=t(0),X=t(0),x=t(0),g=t(!1),m=t(!1),f=t(!1),w=t("向右滑动完成验证"),N=()=>{const c=document.createElement("canvas");c.width=$,c.height=L;const u=c.getContext("2d"),n=u.createLinearGradient(0,0,$,L);n.addColorStop(0,`hsl(${Math.random()*360}, 70%, 80%)`),n.addColorStop(.5,`hsl(${Math.random()*360}, 60%, 70%)`),n.addColorStop(1,`hsl(${Math.random()*360}, 50%, 60%)`),u.fillStyle=n,u.fillRect(0,0,$,L);for(let e=0;e<20;e++)u.fillStyle=`hsla(${Math.random()*360}, 50%, 50%, 0.3)`,u.beginPath(),u.arc(Math.random()*$,Math.random()*L,Math.random()*20+5,0,Math.PI*2),u.fill();return c.toDataURL()},B=()=>{i.value=Math.random()*($-G-80)+60,l.value=Math.random()*(L-G-20)+10,z.value=0,X.value=l.value,x.value=0,m.value=!1,f.value=!1,g.value=!1,w.value="向右滑动完成验证",console.log("拼图初始化完成，目标位置:",i.value)},U=()=>{p.value=N(),j(()=>{B()})},D=c=>{if(m.value)return;console.log("开始拖拽"),c.preventDefault(),g.value=!0,f.value=!1;const u="touches"in c?c.touches[0].clientX:c.clientX,n=x.value,e=k=>{if(!g.value)return;k.preventDefault();const M=("touches"in k?k.touches[0].clientX:k.clientX)-u,E=Math.max(0,Math.min(n+M,$-40));x.value=E,z.value=E,console.log("拖拽中:",E,"目标:",i.value,"差距:",Math.abs(E-i.value)),Math.abs(E-i.value)<Ce&&(console.log("验证成功!"),m.value=!0,g.value=!1,w.value="验证成功",y(),setTimeout(()=>{h("success")},500))},d=()=>{g.value&&(g.value=!1,m.value||(f.value=!0,w.value="验证失败，请重试",setTimeout(()=>{x.value=0,z.value=0,f.value=!1,w.value="向右滑动完成验证",h("fail")},1e3)),y())},y=()=>{document.removeEventListener("mousemove",e),document.removeEventListener("mouseup",d),document.removeEventListener("touchmove",e),document.removeEventListener("touchend",d)};document.addEventListener("mousemove",e,{passive:!1}),document.addEventListener("mouseup",d),document.addEventListener("touchmove",e,{passive:!1}),document.addEventListener("touchend",d)};return W(()=>{U()}),(c,u)=>{const n=T("Right"),e=J,d=T("Refresh");return I(),Z("div",xe,[s("div",Me,[s("div",{class:"puzzle-bg",ref_key:"puzzleBgRef",ref:_},[s("img",{src:a(p),alt:"背景图",onLoad:B},null,40,Ee),s("div",{class:"puzzle-missing",style:A({left:a(i)+"px",top:a(l)+"px"})},u[0]||(u[0]=[s("div",{class:"missing-shape"},null,-1)]),4)],512),s("div",{class:"puzzle-piece",ref_key:"puzzlePieceRef",ref:S,style:A({left:a(z)+"px",top:a(X)+"px",backgroundImage:`url(${a(p)})`,backgroundPosition:`-${a(i)}px -${a(l)}px`})},u[1]||(u[1]=[s("div",{class:"piece-shape"},null,-1)]),4)]),s("div",Te,[s("div",{class:"slider-track",ref_key:"sliderTrackRef",ref:b},[s("div",{class:"slider-button",ref_key:"sliderButtonRef",ref:V,style:A({left:a(x)+"px"}),onMousedown:D,onTouchstart:D},[o(e,null,{default:r(()=>[o(n)]),_:1})],36),s("div",{class:ne(["slider-text",{success:a(m),error:a(f)}])},re(a(w)),3)],512)]),s("div",{class:"refresh-btn",onClick:U},[o(e,null,{default:r(()=>[o(d)]),_:1})])])}}}),Ve=K(Se,[["__scopeId","data-v-5a7d51cd"]]),Pe={class:"login-container"},$e={class:"login-left"},Le={class:"system-info"},Ie={class:"logo"},Xe={class:"logo-placeholder"},Be={class:"login-right"},Ue={class:"login-form"},De={class:"puzzle-captcha"},Fe={class:"login-options"},Ne={class:"demo-accounts"},qe={class:"theme-switch"},Ae=Y({__name:"index",setup(F){const C=ie(),h=ue(),_=ce(),S=de(),b=t(),V=t(),p=t(),i=t(),l=me({username:"",password:"",rememberMe:!1}),z=t(!1),X=t(!0),x={username:[{required:!0,trigger:"blur",message:"请输入用户名"}],password:[{required:!0,trigger:"blur",message:"请输入密码"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}]},g=t(!1),m=t("password"),f=t(!1),w=t();pe(h,n=>{w.value=n.query&&n.query.redirect||"/"},{immediate:!0});const N=()=>{m.value==="password"?m.value="":m.value="password",j(()=>{p.value.focus()})},B=n=>{const{key:e}=n;f.value=e&&e.length===1&&e>="A"&&e<="Z"},U=()=>{z.value=!0,X.value=!1,P.success("拼图验证成功"),console.log("拼图验证成功，puzzleVerified:",z.value)},D=()=>{z.value=!1,P.error("拼图验证失败，请重试")},c=(n,e)=>{l.username=n,l.password=e},u=()=>q(this,null,function*(){if(b.value)try{if(!(yield b.value.validate()))return;if(!z.value){P.error("请完成拼图验证");return}g.value=!0;const d={admin:{id:1,username:"admin",realName:"系统管理员",email:"<EMAIL>",phone:"13800138000",avatar:"/default-avatar.png",status:1,userType:1,deptId:1,roles:["SUPER_ADMIN"],permissions:["*:*:*"]},operator:{id:2,username:"operator",realName:"操作员",email:"<EMAIL>",phone:"13800138001",avatar:"/default-avatar.png",status:1,userType:2,deptId:2,roles:["OPERATOR"],permissions:["patrol:*:*","platform:view:*"]},observer:{id:3,username:"observer",realName:"观察员",email:"<EMAIL>",phone:"13800138002",avatar:"/default-avatar.png",status:1,userType:3,deptId:3,roles:["OBSERVER"],permissions:["*:view:*"]}}[l.username];if(d&&l.password==="123456"){const y="mock-jwt-token-"+Date.now();_.setToken(y),_.userInfo=d,_.roles=d.roles,_.permissions=d.permissions;const k=yield _.generateRoutes();console.log("登录成功，生成的访问路由:",k),console.log("用户store中的所有路由:",_.routes),k.forEach(R=>{C.addRoute(R),console.log("添加路由到router:",R.path)}),P.success("登录成功"),setTimeout(()=>q(this,null,function*(){try{yield C.push({path:w.value||"/"})}catch(R){console.error("路由跳转失败:",R)}}),500)}else P.error("用户名或密码错误")}catch(n){P.error(n.message||"登录失败")}finally{g.value=!1}});return W(()=>{S.initTheme(),j(()=>{var n,e;l.username===""?(n=V.value)==null||n.focus():l.password===""&&((e=p.value)==null||e.focus())})}),(n,e)=>{const d=T("Monitor"),y=J,k=T("User"),R=ve,M=fe,E=T("Lock"),Q=T("View"),ee=T("Hide"),se=_e,oe=ze,te=ye,ae=ke,le=Re;return I(),Z("div",Pe,[s("div",$e,[s("div",Le,[s("div",Ie,[s("div",Xe,[o(y,{size:"60"},{default:r(()=>[o(d)]),_:1})])]),e[8]||(e[8]=s("h1",{class:"system-title"},"标考高清网上巡查管理平台",-1)),e[9]||(e[9]=s("p",{class:"system-subtitle"},"Patrol System Management Platform V3.0",-1))])]),s("div",Be,[s("div",Ue,[e[17]||(e[17]=s("div",{class:"form-header"},[s("h2",null,"用户登录"),s("p",null,"请输入您的账号和密码")],-1)),o(le,{ref_key:"loginFormRef",ref:b,model:a(l),rules:x,class:"login-form-content",autocomplete:"on","label-position":"left"},{default:r(()=>[o(M,{prop:"username"},{default:r(()=>[o(R,{ref_key:"usernameRef",ref:V,modelValue:a(l).username,"onUpdate:modelValue":e[0]||(e[0]=v=>a(l).username=v),placeholder:"请输入用户名",name:"username",type:"text",tabindex:"1",autocomplete:"on",size:"large"},{prefix:r(()=>[o(y,null,{default:r(()=>[o(k)]),_:1})]),_:1},8,["modelValue"])]),_:1}),o(se,{visible:a(f),"onUpdate:visible":e[3]||(e[3]=v=>ge(f)?f.value=v:null),content:"大写锁定已开启",placement:"right",manual:""},{default:r(()=>[o(M,{prop:"password"},{default:r(()=>[(I(),H(R,{key:a(m),ref_key:"passwordRef",ref:p,modelValue:a(l).password,"onUpdate:modelValue":e[1]||(e[1]=v=>a(l).password=v),type:a(m),placeholder:"请输入密码",name:"password",tabindex:"2",autocomplete:"on",size:"large",onKeyup:[B,he(u,["enter"])],onBlur:e[2]||(e[2]=v=>f.value=!1)},{prefix:r(()=>[o(y,null,{default:r(()=>[o(E)]),_:1})]),suffix:r(()=>[o(y,{class:"show-pwd",onClick:N},{default:r(()=>[a(m)==="password"?(I(),H(Q,{key:0})):(I(),H(ee,{key:1}))]),_:1})]),_:1},8,["modelValue","type"]))]),_:1})]),_:1},8,["visible"]),o(M,{prop:"puzzleVerify"},{default:r(()=>[s("div",De,[o(Ve,{ref_key:"puzzleCaptchaRef",ref:i,onSuccess:U,onFail:D},null,512)])]),_:1}),o(M,null,{default:r(()=>[s("div",Fe,[o(oe,{modelValue:a(l).rememberMe,"onUpdate:modelValue":e[4]||(e[4]=v=>a(l).rememberMe=v)},{default:r(()=>e[10]||(e[10]=[O("记住我",-1)])),_:1,__:[10]},8,["modelValue"]),o(te,{type:"primary",underline:!1},{default:r(()=>e[11]||(e[11]=[O("忘记密码？",-1)])),_:1,__:[11]})])]),_:1}),o(M,null,{default:r(()=>[o(ae,{loading:a(g),type:"primary",size:"large",style:{width:"100%"},onClick:we(u,["prevent"])},{default:r(()=>e[12]||(e[12]=[O(" 登录 ",-1)])),_:1,__:[12]},8,["loading"])]),_:1})]),_:1},8,["model"]),s("div",Ne,[e[16]||(e[16]=s("div",{class:"demo-title"},"演示账户（与数据库一致）",-1)),s("div",{class:"demo-item",onClick:e[5]||(e[5]=v=>c("admin","123456"))},e[13]||(e[13]=[s("span",{class:"demo-role"},"超级管理员",-1),s("span",{class:"demo-account"},"admin / 123456",-1)])),s("div",{class:"demo-item",onClick:e[6]||(e[6]=v=>c("operator","123456"))},e[14]||(e[14]=[s("span",{class:"demo-role"},"操作员",-1),s("span",{class:"demo-account"},"operator / 123456",-1)])),s("div",{class:"demo-item",onClick:e[7]||(e[7]=v=>c("observer","123456"))},e[15]||(e[15]=[s("span",{class:"demo-role"},"观察员",-1),s("span",{class:"demo-account"},"observer / 123456",-1)]))])])]),s("div",qe,[o(be)])])}}}),es=K(Ae,[["__scopeId","data-v-78ffaba7"]]);export{es as default};
