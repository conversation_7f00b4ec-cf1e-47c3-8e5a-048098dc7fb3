var Ve=Object.defineProperty,be=Object.defineProperties;var Se=Object.getOwnPropertyDescriptors;var oe=Object.getOwnPropertySymbols;var xe=Object.prototype.hasOwnProperty,ke=Object.prototype.propertyIsEnumerable;var ae=(e,t,s)=>t in e?Ve(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,j=(e,t)=>{for(var s in t||(t={}))xe.call(t,s)&&ae(e,s,t[s]);if(oe)for(var s of oe(t))ke.call(t,s)&&ae(e,s,t[s]);return e},re=(e,t)=>be(e,Se(t));var le=(e,t,s)=>new Promise((o,l)=>{var g=a=>{try{d(s.next(a))}catch(n){l(n)}},i=a=>{try{d(s.throw(a))}catch(n){l(n)}},d=a=>a.done?o(a.value):Promise.resolve(a.value).then(g,i);d((s=s.apply(e,t)).next())});import{_ as A}from"./_plugin-vue_export-helper-fs8hP-CV.js";import{d as ue,a as K,b as $,c as b,e as V,o as h,r as X,u as c,n as Ce,g as ye,w as p,f as Ee,h as B,i as F,j as y,k as P,F as q,l as u,E as Fe,m as Z,p as N,q as m,t as Y,s as ee,v as Te,x as te,y as se,z as he,A as me,B as Me,C as ie,D as $e,G as Le,H as fe,I as Pe,J as Ae,K as Be,L as He,M as pe,N as Oe,O as G,T as Ne,P as ze,Q as Ie,R as J,S as qe,U as De,V as Re,W as Ue,X as I,Y as We,Z as je}from"./index-DKnB9mwy.js";/* empty css                     */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                  *//* empty css                        *//* empty css                 */import{T as we}from"./index-DrnBe4fN.js";/* empty css                   *//* empty css                  */const z=ue("settings",{state:()=>({showSettings:!1,tagsView:!1,fixedHeader:!0,sidebarLogo:!0,device:"desktop",withoutAnimation:!1,cachedViews:[],showFooter:!0,showBreadcrumb:!0,showHamburger:!0,showScreenfull:!0,showThemeSwitch:!0,showNotification:!0}),getters:{isMobile:e=>e.device==="mobile",allSettings:e=>({showSettings:e.showSettings,tagsView:e.tagsView,fixedHeader:e.fixedHeader,sidebarLogo:e.sidebarLogo,device:e.device,withoutAnimation:e.withoutAnimation,showFooter:e.showFooter,showBreadcrumb:e.showBreadcrumb,showHamburger:e.showHamburger,showScreenfull:e.showScreenfull,showThemeSwitch:e.showThemeSwitch,showNotification:e.showNotification})},actions:{toggleSettings(){this.showSettings=!this.showSettings},setShowSettings(e){this.showSettings=e},toggleTagsView(){this.tagsView=!this.tagsView,this.saveSettings()},setTagsView(e){this.tagsView=e,this.saveSettings()},toggleFixedHeader(){this.fixedHeader=!this.fixedHeader,this.saveSettings()},setFixedHeader(e){this.fixedHeader=e,this.saveSettings()},toggleSidebarLogo(){this.sidebarLogo=!this.sidebarLogo,this.saveSettings()},setSidebarLogo(e){this.sidebarLogo=e,this.saveSettings()},toggleDevice(e){this.device=e||(this.device==="desktop"?"mobile":"desktop"),this.saveSettings()},setWithoutAnimation(e){this.withoutAnimation=e},addCachedView(e){this.cachedViews.includes(e)||this.cachedViews.push(e)},delCachedView(e){const t=this.cachedViews.indexOf(e);t>-1&&this.cachedViews.splice(t,1)},delAllCachedViews(){this.cachedViews=[]},updateSettings(e){Object.assign(this,e),this.saveSettings()},saveSettings(){const e={tagsView:this.tagsView,fixedHeader:this.fixedHeader,sidebarLogo:this.sidebarLogo,device:this.device,showFooter:this.showFooter,showBreadcrumb:this.showBreadcrumb,showHamburger:this.showHamburger,showScreenfull:this.showScreenfull,showThemeSwitch:this.showThemeSwitch,showNotification:this.showNotification};K.set("settings",JSON.stringify(e),{expires:365})},loadSettings(){const e=K.get("settings");if(e)try{const t=JSON.parse(e);Object.assign(this,t)}catch(t){console.error("Failed to parse settings from cookie:",t)}},resetSettings(){this.showSettings=!1,this.tagsView=!1,this.fixedHeader=!0,this.sidebarLogo=!0,this.device="desktop",this.withoutAnimation=!1,this.showFooter=!0,this.showBreadcrumb=!0,this.showHamburger=!0,this.showScreenfull=!0,this.showThemeSwitch=!0,this.showNotification=!0,this.saveSettings()},initSettings(){this.loadSettings()}}});function Q(e){return/^(https?:|mailto:|tel:)/.test(e)}const Je=$({__name:"Link",props:{to:{}},setup(e){const t=e,s=b(()=>Q(t.to)?{is:"a",href:t.to,target:"_blank",rel:"noopener"}:{is:"router-link",to:t.to});return(o,l)=>(h(),V(X(c(s).is),Ce(ye(c(s))),{default:p(()=>[Ee(o.$slots,"default")]),_:3},16))}}),Ke={key:0},Xe={class:"menu-title"},Ye={class:"menu-title"},Ge=$({__name:"SidebarItem",props:{item:{},isNest:{type:Boolean,default:!1},basePath:{default:""}},setup(e){const t=e,s=B({}),o=(i=[],d)=>{const a=i.filter(n=>{var r;return(r=n.meta)!=null&&r.hidden?!1:(s.value=n,!0)});return a.length===1?!0:a.length===0?(s.value=re(j({},d),{path:"",noShowingChildren:!0}),!0):!1},l=i=>Q(i)?i:Q(t.basePath)?t.basePath:g.resolve(t.basePath,i),g={resolve:(i,d)=>d.startsWith("/")?d:`${i}/${d}`.replace(/\/+/g,"/")};return(i,d)=>{var T,x;const a=N,n=Fe,r=F("sidebar-item",!0),E=Te;return(T=i.item.meta)!=null&&T.hidden?P("",!0):(h(),y("div",Ke,[o(i.item.children,i.item)&&(!c(s).children||c(s).noShowingChildren)&&!((x=i.item.meta)!=null&&x.alwaysShow)?(h(),y(q,{key:0},[c(s).meta?(h(),V(Je,{key:0,to:l(c(s).path)},{default:p(()=>[u(n,{index:l(c(s).path),class:Z({"submenu-title-noDropdown":!i.isNest})},{title:p(()=>[m("span",Xe,Y(c(s).meta.title),1)]),default:p(()=>[c(s).meta.icon?(h(),V(a,{key:0},{default:p(()=>[(h(),V(X(c(s).meta.icon)))]),_:1})):P("",!0)]),_:1},8,["index","class"])]),_:1},8,["to"])):P("",!0)],64)):(h(),V(E,{key:1,ref:"subMenu",index:l(i.item.path),teleported:""},{title:p(()=>{var C,L;return[(C=i.item.meta)!=null&&C.icon?(h(),V(a,{key:0},{default:p(()=>[(h(),V(X(i.item.meta.icon)))]),_:1})):P("",!0),m("span",Ye,Y((L=i.item.meta)==null?void 0:L.title),1)]}),default:p(()=>[(h(!0),y(q,null,ee(i.item.children,C=>(h(),V(r,{key:C.path,"is-nest":!0,item:C,"base-path":l(C.path),class:"nest-menu"},null,8,["item","base-path"]))),128))]),_:1},8,["index"]))]))}}}),Qe=A(Ge,[["__scopeId","data-v-e0b5ea01"]]),Ze={class:"sidebar-wrapper"},et=$({__name:"index",setup(e){const t=te();se();const s=he(),o=b(()=>!1),l=b(()=>s.routes),g=b(()=>{const{meta:i,path:d}=t;return i!=null&&i.activeMenu?i.activeMenu:d});return(i,d)=>{const a=Me,n=me;return h(),y("div",Ze,[u(n,{"wrap-class":"scrollbar-wrapper"},{default:p(()=>[u(a,{"default-active":c(g),collapse:c(o),"unique-opened":!1,"collapse-transition":!1,mode:"vertical","background-color":"var(--el-menu-bg-color)","text-color":"var(--el-menu-text-color)","active-text-color":"var(--el-menu-active-color)"},{default:p(()=>[(h(!0),y(q,null,ee(c(l),r=>(h(),V(Qe,{key:r.path,item:r,"base-path":r.path},null,8,["item","base-path"]))),128))]),_:1},8,["default-active","collapse"])]),_:1})])}}}),tt=A(et,[["__scopeId","data-v-b05a67da"]]),ce=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],M=(()=>{if(typeof document=="undefined")return!1;const e=ce[0],t={};for(const s of ce)if((s==null?void 0:s[1])in document){for(const[l,g]of s.entries())t[e[l]]=g;return t}return!1})(),de={change:M.fullscreenchange,error:M.fullscreenerror};let v={request(e=document.documentElement,t){return new Promise((s,o)=>{const l=()=>{v.off("change",l),s()};v.on("change",l);const g=e[M.requestFullscreen](t);g instanceof Promise&&g.then(l).catch(o)})},exit(){return new Promise((e,t)=>{if(!v.isFullscreen){e();return}const s=()=>{v.off("change",s),e()};v.on("change",s);const o=document[M.exitFullscreen]();o instanceof Promise&&o.then(s).catch(t)})},toggle(e,t){return v.isFullscreen?v.exit():v.request(e,t)},onchange(e){v.on("change",e)},onerror(e){v.on("error",e)},on(e,t){const s=de[e];s&&document.addEventListener(s,t,!1)},off(e,t){const s=de[e];s&&document.removeEventListener(s,t,!1)},raw:M};Object.defineProperties(v,{isFullscreen:{get:()=>!!document[M.fullscreenElement]},element:{enumerable:!0,get:()=>{var e;return(e=document[M.fullscreenElement])!=null?e:void 0}},isEnabled:{enumerable:!0,get:()=>!!document[M.fullscreenEnabled]}});M||(v={isEnabled:!1});const st=$({__name:"index",setup(e){const t=B(!1),s=()=>{if(!v.isEnabled)return Le({message:"您的浏览器不支持全屏功能",type:"warning"}),!1;v.toggle()},o=()=>{t.value=v.isFullscreen};return ie(()=>{v.isEnabled&&v.on("change",o)}),$e(()=>{v.isEnabled&&v.off("change",o)}),(l,g)=>{const i=F("FullScreen"),d=F("Aim"),a=N;return h(),y("div",{onClick:s},[u(a,{size:18},{default:p(()=>[c(t)?(h(),V(d,{key:1})):(h(),V(i,{key:0}))]),_:1})])}}}),it=A(st,[["__scopeId","data-v-91dbf9b4"]]),nt={class:"navbar"},ot={class:"navbar-right"},at={class:"avatar-wrapper"},rt=["src"],lt=$({__name:"Navbar",setup(e){const t=fe(),s=se(),o=he(),l=z();b(()=>!s.sidebarCollapsed);const g=b(()=>o.userAvatar),i=b({get:()=>l.showSettings,set:a=>l.setShowSettings(a)}),d=()=>le(this,null,function*(){try{yield Oe.confirm("确定注销并退出系统吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),yield o.logout(),t.push("/login")}catch(a){console.log("取消退出")}});return(a,n)=>{const r=F("Bell"),E=N,T=Pe,x=F("CaretBottom"),C=He,L=F("router-link"),D=Be,R=Ae;return h(),y("div",nt,[n[4]||(n[4]=m("div",{class:"navbar-left"},null,-1)),m("div",ot,[u(it,{class:"right-menu-item hover-effect"}),u(we,{class:"right-menu-item hover-effect"}),u(T,{value:12,max:99,class:"right-menu-item hover-effect"},{default:p(()=>[u(E,{size:18},{default:p(()=>[u(r)]),_:1})]),_:1}),u(R,{class:"avatar-container right-menu-item hover-effect",trigger:"click"},{dropdown:p(()=>[u(D,null,{default:p(()=>[u(L,{to:"/profile"},{default:p(()=>[u(C,null,{default:p(()=>n[1]||(n[1]=[pe("个人中心",-1)])),_:1,__:[1]})]),_:1}),u(C,{onClick:n[0]||(n[0]=U=>i.value=!0)},{default:p(()=>n[2]||(n[2]=[m("span",null,"系统设置",-1)])),_:1,__:[2]}),u(C,{divided:"",onClick:d},{default:p(()=>n[3]||(n[3]=[m("span",null,"退出登录",-1)])),_:1,__:[3]})]),_:1})]),default:p(()=>[m("div",at,[m("img",{src:c(g),class:"user-avatar",alt:"avatar"},null,8,rt),u(E,{class:"el-icon-caret-bottom"},{default:p(()=>[u(x)]),_:1})])]),_:1})])])}}}),ct=A(lt,[["__scopeId","data-v-c58ebee2"]]),ge=ue("tagsView",{state:()=>({visitedViews:[],cachedViews:[]}),getters:{allViews:e=>e.visitedViews,allCachedViews:e=>e.cachedViews},actions:{addView(e){this.addVisitedView(e),this.addCachedView(e)},addVisitedView(e){var t,s;this.visitedViews.some(o=>o.path===e.path)||this.visitedViews.push({name:e.name,path:e.path,title:((t=e.meta)==null?void 0:t.title)||"no-name",affix:(s=e.meta)==null?void 0:s.affix})},addCachedView(e){var t;typeof e.name=="string"&&(this.cachedViews.includes(e.name)||(t=e.meta)!=null&&t.noCache||this.cachedViews.push(e.name))},delView(e){return new Promise(t=>{this.delVisitedView(e),this.delCachedView(e),t({visitedViews:[...this.visitedViews],cachedViews:[...this.cachedViews]})})},delVisitedView(e){return new Promise(t=>{for(const[s,o]of this.visitedViews.entries())if(o.path===e.path){this.visitedViews.splice(s,1);break}t([...this.visitedViews])})},delCachedView(e){return new Promise(t=>{if(typeof e.name!="string"){t([...this.cachedViews]);return}const s=this.cachedViews.indexOf(e.name);s>-1&&this.cachedViews.splice(s,1),t([...this.cachedViews])})},delOthersViews(e){return new Promise(t=>{this.delOthersVisitedViews(e),this.delOthersCachedViews(e),t({visitedViews:[...this.visitedViews],cachedViews:[...this.cachedViews]})})},delOthersVisitedViews(e){return new Promise(t=>{this.visitedViews=this.visitedViews.filter(s=>s.affix||s.path===e.path),t([...this.visitedViews])})},delOthersCachedViews(e){return new Promise(t=>{if(typeof e.name!="string"){t([...this.cachedViews]);return}const s=this.cachedViews.indexOf(e.name);s>-1?this.cachedViews=this.cachedViews.slice(s,s+1):this.cachedViews=[],t([...this.cachedViews])})},delAllViews(){return new Promise(e=>{this.delAllVisitedViews(),this.delAllCachedViews(),e({visitedViews:[...this.visitedViews],cachedViews:[...this.cachedViews]})})},delAllVisitedViews(){return new Promise(e=>{const t=this.visitedViews.filter(s=>s.affix);this.visitedViews=t,e([...this.visitedViews])})},delAllCachedViews(){return new Promise(e=>{this.cachedViews=[],e([...this.cachedViews])})},updateVisitedView(e){for(let t of this.visitedViews)if(t.path===e.path){t=Object.assign(t,e);break}}}}),dt={class:"app-main"},ut=$({__name:"AppMain",setup(e){const t=te(),s=ge(),o=z(),l=b(()=>s.cachedViews),g=b(()=>t.path);return G(t,()=>{o.tagsView&&s.addView(t)},{immediate:!0}),(i,d)=>{const a=F("router-view");return h(),y("section",dt,[u(Ne,{name:"fade-transform",mode:"out-in"},{default:p(()=>[(h(),V(ze,{include:c(l)},[(h(),V(a,{key:c(g)}))],1032,["include"]))]),_:1})])}}}),ht=A(ut,[["__scopeId","data-v-4ba72a43"]]),mt={class:"tags-view-container"},ft=$({__name:"index",setup(e){const t=te(),s=fe(),o=ge();z();const l=B(!1),g=B(0),i=B(0),d=B(),a=B([]),n=b(()=>o.visitedViews),r=f=>f?f.path===t.path:!1,E=f=>f&&f.affix||!1,T=(f,w="/")=>{let S=[];return f.forEach(k=>{var H,O;if((H=k.meta)!=null&&H.affix){const _=w==="/"?k.path:w+"/"+k.path;S.push({fullPath:_,path:_,name:k.name,title:((O=k.meta)==null?void 0:O.title)||k.name,affix:!0,meta:j({},k.meta)})}if(k.children&&k.children.length>0){const _=T(k.children,k.path);_.length>=1&&(S=[...S,..._])}}),S},x=()=>{const f=s.getRoutes(),w=a.value=T(f);for(const S of w)S.name&&o.addVisitedView(S)},C=()=>{var w;const{name:f}=t;return f&&((w=t.meta)!=null&&w.title)&&o.addView(t),!1},L=f=>{o.delView(f).then(({visitedViews:w})=>{r(f)&&U(w,f)})},D=()=>{d.value&&(s.push(d.value),o.delOthersViews(d.value).then(()=>{}))},R=f=>{o.delAllViews().then(({visitedViews:w})=>{a.value.some(S=>S.path===f.path)||U(w,f)})},U=(f,w)=>{const S=f.slice(-1)[0];S?s.push(S.fullPath):w.name==="Dashboard"?s.replace({path:"/redirect"+w.fullPath}):s.push("/")},_e=f=>{o.delCachedView(f).then(()=>{const{fullPath:w}=f;Re(()=>{s.replace({path:"/redirect"+w})})})},ve=(f,w)=>{const _=w.clientX-0+15;_>-5?i.value=-5:i.value=_,g.value=w.clientY,l.value=!0,d.value=f},ne=()=>{l.value=!1};return G(t,()=>{C()}),G(l,f=>{f?document.body.addEventListener("click",ne):document.body.removeEventListener("click",ne)}),ie(()=>{x(),C()}),(f,w)=>{const S=F("Close"),k=N,H=F("router-link"),O=me;return h(),y("div",mt,[u(O,{class:"tags-view-wrapper"},{default:p(()=>[(h(!0),y(q,null,ee(c(n),_=>(h(),V(H,{key:_.path,class:Z([r(_)?"active":"","tags-view-item"]),to:{path:_.path,query:_.query,fullPath:_.fullPath},onMouseup:J(W=>E(_)?"":L(_),["middle"]),onContextmenu:J(W=>ve(_,W),["prevent"])},{default:p(()=>[pe(Y(_.title||_.name)+" ",1),E(_)?P("",!0):(h(),V(k,{key:0,class:"el-icon-close",onClick:J(W=>L(_),["prevent","stop"])},{default:p(()=>[u(S)]),_:2},1032,["onClick"]))]),_:2},1032,["class","to","onMouseup","onContextmenu"]))),128))]),_:1}),Ie(m("ul",{style:De({left:c(i)+"px",top:c(g)+"px"}),class:"contextmenu"},[m("li",{onClick:w[0]||(w[0]=_=>_e(c(d)))},"刷新"),E(c(d))?P("",!0):(h(),y("li",{key:0,onClick:w[1]||(w[1]=_=>L(c(d)))},"关闭")),m("li",{onClick:D},"关闭其它"),m("li",{onClick:w[2]||(w[2]=_=>R(c(d)))},"关闭所有")],4),[[qe,c(l)]])])}}}),pt=A(ft,[["__scopeId","data-v-c3b41e40"]]),wt={class:"drawer-container"},gt={class:"drawer-item"},_t={class:"drawer-item"},vt={class:"drawer-item"},Vt={class:"drawer-item"},bt=$({__name:"index",setup(e){const t=z(),s=b({get:()=>t.showSettings,set:n=>t.changeSetting({key:"showSettings",value:n})}),o=b({get:()=>t.tagsView,set:n=>t.changeSetting({key:"tagsView",value:n})}),l=b({get:()=>t.fixedHeader,set:n=>t.changeSetting({key:"fixedHeader",value:n})}),g=b({get:()=>t.sidebarLogo,set:n=>t.changeSetting({key:"sidebarLogo",value:n})}),i=n=>{t.changeSetting({key:"tagsView",value:n})},d=n=>{t.changeSetting({key:"fixedHeader",value:n})},a=n=>{t.changeSetting({key:"sidebarLogo",value:n})};return(n,r)=>{const E=Ue,T=We;return h(),y("div",wt,[u(T,{modelValue:c(s),"onUpdate:modelValue":r[3]||(r[3]=x=>I(s)?s.value=x:null),title:"系统设置","with-header":!1,direction:"rtl",size:"300px"},{default:p(()=>[r[8]||(r[8]=m("div",{class:"drawer-title"},[m("h4",null,"系统设置")],-1)),m("div",gt,[r[4]||(r[4]=m("span",null,"主题色",-1)),u(we,{style:{float:"right",height:"26px",margin:"-3px 8px 0 0"}})]),m("div",_t,[r[5]||(r[5]=m("span",null,"开启 Tags-View",-1)),u(E,{modelValue:c(o),"onUpdate:modelValue":r[0]||(r[0]=x=>I(o)?o.value=x:null),style:{float:"right"},onChange:i},null,8,["modelValue"])]),m("div",vt,[r[6]||(r[6]=m("span",null,"固定 Header",-1)),u(E,{modelValue:c(l),"onUpdate:modelValue":r[1]||(r[1]=x=>I(l)?l.value=x:null),style:{float:"right"},onChange:d},null,8,["modelValue"])]),m("div",Vt,[r[7]||(r[7]=m("span",null,"侧边栏 Logo",-1)),u(E,{modelValue:c(g),"onUpdate:modelValue":r[2]||(r[2]=x=>I(g)?g.value=x:null),style:{float:"right"},onChange:a},null,8,["modelValue"])])]),_:1,__:[8]},8,["modelValue"])])}}}),St=A(bt,[["__scopeId","data-v-69ef816a"]]),xt={class:"app-header"},kt={class:"header-left"},Ct={class:"logo-section"},yt={class:"logo-icon"},Et={class:"header-right"},Ft={class:"app-body"},Tt={class:"main-container"},Mt=$({__name:"index",setup(e){const t=se(),s=z(),o=b(()=>({hideSidebar:t.sidebarCollapsed,openSidebar:!t.sidebarCollapsed,withoutAnimation:s.withoutAnimation,mobile:s.device==="mobile"})),l=b(()=>s.tagsView),g=b(()=>s.showSettings),i=()=>{const a=document.body.getBoundingClientRect().width-1<992;s.toggleDevice(a?"mobile":"desktop"),a?t.setSidebarCollapsed(!0):K.get("sidebarCollapsed")||t.setSidebarCollapsed(!1)};return ie(()=>{i(),window.addEventListener("resize",i)}),je(()=>{window.removeEventListener("resize",i)}),(d,a)=>{const n=F("Monitor"),r=N;return h(),y("div",{class:Z(["app-wrapper",c(o)])},[m("div",xt,[m("div",kt,[m("div",Ct,[m("div",yt,[u(r,{size:"24"},{default:p(()=>[u(n)]),_:1})]),a[0]||(a[0]=m("h1",{class:"system-title"},"标考高清网上巡查管理平台",-1))])]),m("div",Et,[u(ct)])]),m("div",Ft,[u(tt,{class:"sidebar-container"}),m("div",Tt,[c(l)?(h(),V(pt,{key:0})):P("",!0),u(ht)])]),c(g)?(h(),V(St,{key:0})):P("",!0)],2)}}}),Rt=A(Mt,[["__scopeId","data-v-ca8172d6"]]);export{Rt as default};
