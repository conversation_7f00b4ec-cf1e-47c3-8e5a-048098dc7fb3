import{_ as r}from"./_plugin-vue_export-helper-fs8hP-CV.js";/* empty css                *//* empty css                 */import{b as c,j as _,o as d,l as o,w as a,q as e,ae as p,a9 as i}from"./index-DKnB9mwy.js";const l={class:"app-container"},m={class:"page-content"},f=c({__name:"index",setup(h){return(u,t)=>{const s=p,n=i;return d(),_("div",l,[o(n,{shadow:"hover"},{header:a(()=>t[0]||(t[0]=[e("div",{class:"card-header"},[e("span",null,"多级注册")],-1)])),default:a(()=>[e("div",m,[o(s,{description:"多级注册功能开发中..."})])]),_:1})])}}}),w=r(f,[["__scopeId","data-v-86f9bb3f"]]);export{w as default};
