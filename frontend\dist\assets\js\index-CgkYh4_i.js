var M=(Y,P,$)=>new Promise((i,S)=>{var C=x=>{try{E($.next(x))}catch(b){S(b)}},w=x=>{try{E($.throw(x))}catch(b){S(b)}},E=x=>x.done?i(x.value):Promise.resolve(x.value).then(C,w);E(($=$.apply(Y,P)).next())});import{_ as Ye}from"./_plugin-vue_export-helper-fs8hP-CV.js";import{d as Ze,c as Je,u as Ke,g as G}from"./organization-i5Lk5I8K.js";/* empty css                 *//* empty css                *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                    *//* empty css                  *//* empty css                   *//* empty css                     *//* empty css                 *//* empty css               *//* empty css                *//* empty css                        *//* empty css                  */import{b as Qe,h as y,_ as ie,c as Xe,O as ue,V as ce,C as We,j as h,o as p,q as r,l,Q as et,w as o,ap as tt,a5 as lt,M as c,p as at,u as I,ax as ot,ay as pe,az as st,aA as me,a9 as nt,a8 as rt,an as dt,e as q,r as it,t as _,a0 as ut,aB as ct,k,aC as pt,ad as mt,a7 as ft,aD as vt,ae as gt,aE as fe,aF as ve,aG as _t,aH as yt,aI as ht,a6 as xt,$ as bt,aJ as zt,a3 as jt,am as kt,aK as wt,ag as Nt,F as It,s as Ct,ah as Vt,ao as St,aL as Et,S as Ut,U as qt,G as v,N as Pt}from"./index-DKnB9mwy.js";const $t={class:"organization-management"},At={class:"toolbar"},Mt={class:"toolbar-content"},Dt={class:"toolbar-right"},Bt={class:"main-content"},Tt={class:"card-header"},Lt={class:"tree-node"},Rt={class:"node-label"},Ft={class:"node-code"},Ot={class:"card-header"},Ht={key:0,class:"header-actions"},Gt={key:0,class:"detail-content"},Yt={class:"info-section"},Zt={class:"stats-section"},Jt={key:1,class:"empty-content"},Kt={class:"import-content"},Qt={class:"import-options"},Xt={class:"import-config"},Wt={class:"dialog-footer"},el={key:0,class:"form-tip"},tl={key:0,class:"form-tip"},ll={class:"form-tip"},al={key:0},ol={key:1},sl={key:2},nl={key:3},rl={key:4},dl={class:"dialog-footer"},il=Qe({__name:"index",setup(Y){const P=y(""),$=y(),i=y(null),S=y(!1),C=y(!1),w=y(!1),E=y(!1),x=y(!1),b=y("add"),D=y(),B=y("platform"),A=ie({platformUrl:"http://exam-platform.edu.cn",databaseUrl:"",options:["backup","validate"]}),a=ie({id:"",parentId:"",level:"",code:"",fullName:"",shortName:"",singleName:"",uri:"",sort:0,status:1}),Z=y({left:"0px",top:"0px"}),J={children:"children",label:"fullName",value:"id"},z=y([]),ge={parentId:[{required:!0,message:"请选择上级机构",trigger:"change"}],level:[{required:!0,message:"请选择机构层级",trigger:"change"}],code:[{required:!0,message:"请输入机构码",trigger:"blur"},{pattern:/^\d+$/,message:"机构码只能包含数字",trigger:"blur"}],fullName:[{required:!0,message:"请输入机构全称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],shortName:[{required:!0,message:"请输入机构简称",trigger:"blur"},{min:1,max:20,message:"长度在 1 到 20 个字符",trigger:"blur"}],singleName:[{required:!0,message:"请输入单字简称",trigger:"blur"},{len:1,message:"单字简称只能是一个字符",trigger:"blur"}],uri:[{pattern:/^[a-z0-9]+(\.[a-z0-9]+)*\.cnjy$/i,message:"请输入正确的URI格式（如：学校简拼.教育简拼.cnjy）",trigger:"blur"}]},K=t=>{if(!t)return"未设置";try{let e;if(typeof t=="string")e=new Date(t);else if(t instanceof Date)e=t;else return"格式错误";if(isNaN(e.getTime()))return"无效时间";const s=e.getFullYear(),n=String(e.getMonth()+1).padStart(2,"0"),u=String(e.getDate()).padStart(2,"0"),f=String(e.getHours()).padStart(2,"0"),m=String(e.getMinutes()).padStart(2,"0"),V=String(e.getSeconds()).padStart(2,"0");return`${s}-${n}-${u} ${f}:${m}:${V}`}catch(e){return console.error("时间格式化错误:",e),"格式错误"}},_e=Xe(()=>{const t=[{label:"根节点",value:"root"},{label:"省份",value:"province"},{label:"地市",value:"city"},{label:"区县",value:"district"},{label:"考点",value:"examSite"}];if(!a.parentId)return t;const e=U(z.value,a.parentId);if(!e)return t;const n={root:["province"],province:["city","examSite"],city:["district","examSite"],district:["examSite"]}[e.level]||[];return t.filter(u=>n.includes(u.value))}),ye=(t,e)=>t?e.fullName.includes(t)||e.shortName.includes(t)||e.code.includes(t):!0,he=t=>({root:"OfficeBuilding",province:"MapLocation",city:"OfficeBuilding",district:"School",examSite:"House"})[t]||"Folder",xe=t=>({root:"根节点",province:"省份",city:"地市",district:"区县",examSite:"考点"})[t]||"未知",Q=t=>({root:"province",province:"city",city:"district",district:"examSite"})[t]||null,be=()=>{if(!a.parentId)return"";const t=U(z.value,a.parentId);return t&&{root:"根节点下只能创建省份",province:"省份下可以创建地市或省直属考点",city:"地市下可以创建区县或市直属考点",district:"区县下只能创建考点"}[t.level]||""},ze=t=>{if(!t)return"";const e=U(z.value,t);return e?`${e.fullName} (${e.code})`:""},je=(t,e)=>{switch(e){case"province":return ke("",2,"province");case"city":return t.replace(/[^0-9]/g,"")+"01";case"district":return t.replace(/[^0-9]/g,"")+"01";case"examSite":return t.replace(/[^0-9]/g,"")+"001";default:return""}},ke=(t,e,s)=>{const n=we(s);for(let f=11;f<=99;f++){const m=f.toString();if(!n.includes(m))return m}const u=t.replace(/[^0-9]/g,"");for(let f=1;f<=99;f++){const m=f.toString().padStart(2,"0"),V=u+m;if(!n.includes(V))return V}return u+"01"},we=t=>{const e=[];function s(n){for(const u of n)u.level===t&&e.push(u.code||u.id),u.children&&u.children.length>0&&s(u.children)}return s(z.value),e},Ne=t=>({root:"primary",province:"danger",city:"warning",district:"success",examSite:"info"})[t]||"info",Ie=t=>{i.value=t,w.value=!1},Ce=(t,e)=>{t.preventDefault(),i.value=e,Z.value={left:t.clientX+"px",top:t.clientY+"px"},w.value=!0},Ve=()=>{if(i.value&&i.value.level==="examSite"){v.warning("考点是最底层机构，不能在考点下新增机构");return}if(b.value="add",te(),i.value){a.parentId=i.value.id;const t=Q(i.value.level);t&&(a.level=t)}C.value=!0},Se=()=>{if(!i.value)return;if(i.value.level==="examSite"){v.warning("考点是最底层机构，不能在考点下新增机构"),w.value=!1;return}b.value="add",te(),a.parentId=i.value.id;const t=Q(i.value.level);t&&(a.level=t),C.value=!0,w.value=!1},X=()=>{i.value&&(b.value="edit",Object.assign(a,i.value),C.value=!0,w.value=!1)},W=()=>M(this,null,function*(){var t,e;if(i.value){try{yield Pt.confirm(`确定要删除机构"${i.value.fullName}"吗？此操作不可恢复！`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),yield Ze(i.value.id),v.success("删除成功"),i.value=null,T()}catch(s){s!=="cancel"&&(console.error("删除失败:",s),v.error("删除失败: "+(((e=(t=s.response)==null?void 0:t.data)==null?void 0:e.message)||s.message)))}w.value=!1}}),Ee=()=>M(this,null,function*(){var t,e;if(D.value)try{if(!(yield D.value.validate()))return;x.value=!0;const n={parentId:a.parentId,level:a.level,code:a.code,fullName:a.fullName,shortName:a.shortName,singleName:a.singleName,uri:a.uri,sort:a.sort,status:a.status};if(a.level==="examSite"&&a.parentId){const u=U(z.value,a.parentId);u&&(n.parentLevel=u.level)}b.value==="add"?yield Je(n):yield Ke(a.id,n),v.success(b.value==="add"?"新增成功":"编辑成功"),C.value=!1,T()}catch(s){console.error("保存失败:",s),v.error("保存失败: "+(((e=(t=s.response)==null?void 0:t.data)==null?void 0:e.message)||s.message))}finally{x.value=!1}}),Ue=()=>M(this,null,function*(){try{E.value=!0,yield new Promise(t=>setTimeout(t,2e3)),v.success("导入成功"),S.value=!1,T()}catch(t){console.error("导入失败:",t),v.error("导入失败")}finally{E.value=!1}}),T=()=>M(this,null,function*(){yield ee(),v.success("刷新成功")}),qe=()=>M(this,null,function*(){try{console.log("开始测试API连接...");const t=yield fetch("/api/organization/tree");if(console.log("Fetch响应:",t),t.ok){const s=yield t.json();console.log("Fetch数据:",s),v.success(`API测试成功！状态: ${t.status}, 数据: ${s.code}`)}else console.error("Fetch失败:",t.status,t.statusText),v.error(`API测试失败！状态: ${t.status}`);console.log("测试API函数...");const e=yield G();console.log("API函数响应:",e),v.success("API函数测试成功！")}catch(t){console.error("API测试失败:",t),v.error("API测试失败: "+t.message)}}),Pe=()=>{if(a.level==="root"){a.code="00";return}if(!a.parentId)return;const t=U(z.value,a.parentId);if(!t)return;const e=t.code||t.id;if(!a.code){a.code=je(e,a.level);return}let s=0;switch(a.level){case"province":s=2;break;case"city":s=4;break;case"district":s=6;break;case"examSite":s=9;break;default:return}if(a.code.length<s){const n=a.code.padStart(s,"0");if(a.level!=="province"){const u=e.length,f=e,m=n.substring(u);a.code=f+m.padStart(s-u,"0")}else a.code=n}},$e=()=>{a.level==="examSite"&&(!a.shortName||!a.shortName.trim()||R())},Ae=()=>{a.level!=="examSite"||a.uri||R()},R=()=>{const t=Me(a.shortName),e=U(z.value,a.parentId);if(!e||!t){console.warn("无法生成URI：缺少学校代码或上级机构信息");return}let s=[t];const u=(m=>{const V=[];let g=m;for(;g&&g.level!=="root";){const F=g.shortName||g.fullName,N=De(F);if(V.push(`${N}jy`),g.parentId||g.parent_id){const O=g.parentId||g.parent_id;g=U(z.value,O)}else break}return V})(e);s.push(...u),s.push("cnjy");const f=s.join(".");a.uri=f,v.success(`已自动生成URI: ${f}`)},Me=t=>{if(!t)return"";const e=t.split("");let s="";for(const n of e)if(/[\u4e00-\u9fa5]/.test(n)){const u={一:"y",二:"e",三:"s",四:"s",五:"w",六:"l",七:"q",八:"b",九:"j",十:"s",第:"d",零:"l",成:"c",都:"d",北:"b",京:"j",上:"s",海:"h",广:"g",州:"z",深:"s",圳:"z",杭:"h",西:"x",南:"n",宁:"n",武:"w",汉:"h",长:"c",沙:"s",重:"c",庆:"q",天:"t",津:"j",青:"q",岛:"d",大:"d",连:"l",沈:"s",阳:"y",哈:"h",尔:"e",滨:"b",石:"s",家:"j",庄:"z",太:"t",原:"y",呼:"h",和:"h",浩:"h",特:"t",乌:"w",鲁:"l",木:"m",齐:"q",银:"y",川:"c",兰:"l",拉:"l",萨:"s",中:"z",学:"x",华:"h",师:"s",范:"f",附:"f",属:"s",实:"s",验:"y",外:"w",国:"g",语:"y",人:"r",民:"m",交:"j",通:"t",科:"k",技:"j",电:"d",子:"z",理:"l",工:"g",农:"n",业:"y",医:"y",药:"y",财:"c",经:"j",政:"z",法:"f",文:"w",艺:"y",体:"t",育:"y",音:"y",乐:"l",美:"m",术:"s",建:"j",筑:"z",机:"j",械:"x",汽:"q",车:"c",船:"c",舶:"b",航:"h",空:"k",铁:"t",路:"l",邮:"y",油:"y",化:"h",纺:"f",织:"z",轻:"q",食:"s",品:"p",商:"s",贸:"m",金:"j",融:"r",保:"b",险:"x",管:"g",信:"x",息:"x",计:"j",算:"s",软:"r",件:"j",网:"w",络:"l",数:"s",据:"j",库:"k",系:"x",统:"t",树:"s",德:"d",才:"c",英:"y",博:"b",雅:"y",智:"z",慧:"h",启:"q",明:"m",星:"x",辰:"c",光:"g",希:"x",望:"w",锦:"j",江:"j",羊:"y",牛:"n",侯:"h",龙:"l",泉:"q",驿:"y",温:"w",新:"x",双:"s",流:"l",郫:"p",河:"h",越:"y",秀:"x",荔:"l",湾:"w",珠:"z",白:"b",云:"y",黄:"h",埔:"p",花:"h",番:"f",禺:"y",山:"s",福:"f",田:"t",罗:"l",湖:"h",盐:"y",宝:"b",安:"a",岗:"g",坪:"p",鹏:"p",淀:"d",朝:"c",东:"d",城:"c",丰:"f",台:"t",景:"j",门:"m",头:"t",沟:"g",房:"f",顺:"s",义:"y",昌:"c",平:"p",兴:"x",怀:"h",柔:"r",密:"m",延:"y"};u[n]&&(s+=u[n])}else/[a-zA-Z0-9]/.test(n)&&(s+=n.toLowerCase());return s.substring(0,6)},De=t=>{if(!t)return"jy";const e=t.replace(/(省|市|区|县|自治区|自治州|地区|盟|特别行政区)$/g,""),s={四川:"sc",广东:"gd",北京:"bj",上海:"sh",江苏:"js",山东:"sd",浙江:"zj",河南:"hn",湖北:"hb",湖南:"hn",安徽:"ah",江西:"jx",河北:"hb",山西:"sx",辽宁:"ln",吉林:"jl",黑龙江:"hlj",福建:"fj",台湾:"tw",海南:"han",重庆:"cq",天津:"tj",内蒙古:"nmg",广西:"gx",西藏:"xz",宁夏:"nx",新疆:"xj",青海:"qh",甘肃:"gs",陕西:"sx",云南:"yn",贵州:"gz",香港:"hk",澳门:"am",成都:"cd",绵阳:"my",德阳:"dy",南充:"nc",宜宾:"yb",自贡:"zg",泸州:"lz",达州:"dz",内江:"nj",乐山:"ls",眉山:"ms",广安:"ga",遂宁:"sn",雅安:"ya",巴中:"bz",资阳:"zy",攀枝花:"pzh",广元:"gy",广州:"gz",深圳:"sz",珠海:"zh",汕头:"st",佛山:"fs",韶关:"sg",湛江:"zj",肇庆:"zq",江门:"jm",茂名:"mm",惠州:"hz",梅州:"mz",汕尾:"sw",河源:"hy",阳江:"yj",清远:"qy",东莞:"dg",中山:"zs",潮州:"cz",揭阳:"jy",云浮:"yf",南京:"nj",苏州:"sz",无锡:"wx",常州:"cz",镇江:"zj",南通:"nt",泰州:"tz",扬州:"yz",盐城:"yc",连云港:"lyg",徐州:"xz",淮安:"ha",宿迁:"sq",济南:"jn",青岛:"qd",淄博:"zb",枣庄:"zz",东营:"dy",烟台:"yt",潍坊:"wf",济宁:"jn",泰安:"ta",威海:"wh",日照:"rz",临沂:"ly",德州:"dz",聊城:"lc",滨州:"bz",菏泽:"hz",杭州:"hz",宁波:"nb",温州:"wz",嘉兴:"jx",湖州:"hz",绍兴:"sx",金华:"jh",衢州:"qz",舟山:"zs",台州:"tz",丽水:"ls",武汉:"wh",黄石:"hs",十堰:"sy",宜昌:"yc",襄阳:"xy",鄂州:"ez",荆门:"jm",孝感:"xg",荆州:"jz",黄冈:"hg",咸宁:"xn",随州:"sz",长沙:"cs",株洲:"zz",湘潭:"xt",衡阳:"hy",邵阳:"sy",岳阳:"yy",常德:"cd",张家界:"zjj",益阳:"yy",郴州:"cz",永州:"yz",怀化:"hh",娄底:"ld",锦江:"jj",青羊:"qy",金牛:"jn",武侯:"wh",成华:"ch",龙泉驿:"lqy",青白江:"qbj",新都:"xd",温江:"wj",双流:"sl",郫都:"pd",新津:"xj",都江堰:"djy",彭州:"pz",邛崃:"ql",崇州:"cz",金堂:"jt",大邑:"dy",蒲江:"pj",简阳:"jy",天河:"th",越秀:"yx",荔湾:"lw",海珠:"hz",白云:"by",黄埔:"hp",番禺:"py",花都:"hd",南沙:"ns",从化:"ch",增城:"zc",海淀:"hd",朝阳:"cy",西城:"xc",东城:"dc",丰台:"ft",石景山:"sjs",门头沟:"mtg",房山:"fs",通州:"tz",顺义:"sy",昌平:"cp",大兴:"dx",怀柔:"hr",平谷:"pg",密云:"my",延庆:"yq",浦东:"pd",黄浦:"hp",静安:"ja",徐汇:"xh",长宁:"cn",普陀:"pt",虹口:"hk",杨浦:"yp",闵行:"mh",宝山:"bs",嘉定:"jd",金山:"js",松江:"sj",青浦:"qp",奉贤:"fx",崇明:"cm"};for(const[n,u]of Object.entries(s))if(e.includes(n))return u;for(const[n,u]of Object.entries(s))if(t.includes(n))return u;return Be(e||t)},Be=t=>{const e={安:"a",北:"b",成:"c",大:"d",二:"e",福:"f",广:"g",海:"h",江:"j",开:"k",辽:"l",美:"m",南:"n",欧:"o",平:"p",青:"q",人:"r",四:"s",天:"t",武:"w",西:"x",云:"y",浙:"z",中:"z",华:"h",师:"s",范:"f",学:"x",校:"x",区:"q",县:"x",市:"s",省:"s",州:"z",港:"g",澳:"a",台:"t"};let s="";for(let n=0;n<Math.min(t.length,2);n++){const u=t[n];e[u]?s+=e[u]:/[a-zA-Z]/.test(u)&&(s+=u.toLowerCase())}return s||"jy"},U=(t,e)=>{for(const s of t){if(s.id===e)return s;if(s.children&&s.children.length>0){const n=U(s.children,e);if(n)return n}}return null},ee=()=>M(this,null,function*(){var t,e;try{console.log("开始加载机构树数据..."),console.log("API函数:",G),console.log("请求参数:",{keyword:P.value});const s=Date.now(),n=yield G({keyword:P.value,_t:s});console.log("机构树API响应:",n),console.log("响应状态:",n==null?void 0:n.code),console.log("响应消息:",n==null?void 0:n.message),console.log("机构树数据:",n.data),console.log("数据类型:",typeof n.data,"是否为数组:",Array.isArray(n.data)),n&&n.code===200&&n.data&&Array.isArray(n.data)?(z.value=n.data,console.log("机构树数据设置成功，节点数量:",n.data.length),v.success(`机构树加载成功，共 ${n.data.length} 个根节点`)):(console.warn("机构树数据格式异常:",n),z.value=[],v.warning("机构树数据为空或格式异常"))}catch(s){console.error("加载机构树失败:",s),console.error("错误类型:",s.constructor.name),console.error("错误消息:",s.message),console.error("错误详情:",((t=s.response)==null?void 0:t.data)||s.stack);let n="加载机构树失败";s.response?n+=`: HTTP ${s.response.status} - ${((e=s.response.data)==null?void 0:e.message)||s.response.statusText}`:s.message&&(n+=`: ${s.message}`),v.error(n)}}),te=()=>{Object.assign(a,{id:"",parentId:"",level:"",code:"",fullName:"",shortName:"",singleName:"",uri:"",sort:0,status:1}),ce(()=>{var t;(t=D.value)==null||t.clearValidate()})};return ue(P,t=>{var e;(e=$.value)==null||e.filter(t)}),ue(()=>a.parentId,(t,e)=>{a.level==="examSite"&&a.shortName&&a.shortName.trim()&&t!==e&&t&&ce(()=>{R()})}),document.addEventListener("click",()=>{w.value=!1}),We(()=>{ee()}),(t,e)=>{const s=at,n=lt,u=tt,f=nt,m=ut,V=dt,g=rt,F=mt,N=pt,O=ct,L=vt,le=ft,Te=gt,Le=_t,ae=ht,oe=yt,j=bt,H=jt,Re=zt,se=xt,ne=kt,Fe=wt,Oe=Vt,He=Nt,Ge=St,re=Et;return p(),h("div",$t,[r("div",At,[l(f,{class:"toolbar-card"},{default:o(()=>[r("div",Mt,[e[23]||(e[23]=r("div",{class:"toolbar-left"},[r("h3",null,"机构数据管理"),r("p",null,"管理全国各省、地市、区县、考点的机构树结构")],-1)),r("div",Dt,[l(u,null,{default:o(()=>[l(n,{type:"primary",onClick:e[0]||(e[0]=d=>S.value=!0)},{default:o(()=>[l(s,null,{default:o(()=>[l(I(ot))]),_:1}),e[19]||(e[19]=c(" 导入机构数据 ",-1))]),_:1,__:[19]}),l(n,{type:"success",onClick:Ve},{default:o(()=>[l(s,null,{default:o(()=>[l(I(pe))]),_:1}),e[20]||(e[20]=c(" 新增机构 ",-1))]),_:1,__:[20]}),l(n,{onClick:T},{default:o(()=>[l(s,null,{default:o(()=>[l(I(st))]),_:1}),e[21]||(e[21]=c(" 刷新 ",-1))]),_:1,__:[21]}),l(n,{type:"warning",onClick:qe},{default:o(()=>[l(s,null,{default:o(()=>[l(I(me))]),_:1}),e[22]||(e[22]=c(" 测试API ",-1))]),_:1,__:[22]})]),_:1})])])]),_:1})]),r("div",Bt,[l(le,{gutter:20},{default:o(()=>[l(g,{span:8},{default:o(()=>[l(f,{class:"tree-card"},{header:o(()=>[r("div",Tt,[e[24]||(e[24]=r("span",null,"机构树",-1)),l(m,{modelValue:P.value,"onUpdate:modelValue":e[1]||(e[1]=d=>P.value=d),placeholder:"搜索机构",size:"small",style:{width:"200px"},clearable:""},{prefix:o(()=>[l(s,null,{default:o(()=>[l(I(me))]),_:1})]),_:1},8,["modelValue"])])]),default:o(()=>[l(V,{ref_key:"treeRef",ref:$,data:z.value,props:J,"filter-node-method":ye,"node-key":"id","show-checkbox":"","default-expand-all":"","expand-on-click-node":!1,onNodeClick:Ie,onNodeContextmenu:Ce},{default:o(({node:d,data:de})=>[r("div",Lt,[l(s,{class:"node-icon"},{default:o(()=>[(p(),q(it(he(de.level))))]),_:2},1024),r("span",Rt,_(d.label),1),r("span",Ft,"("+_(de.code)+")",1)])]),_:1},8,["data"])]),_:1})]),_:1}),l(g,{span:16},{default:o(()=>[l(f,{class:"detail-card"},{header:o(()=>[r("div",Ot,[r("span",null,_(i.value?"机构详情":"请选择机构节点"),1),i.value?(p(),h("div",Ht,[l(n,{size:"small",onClick:X},{default:o(()=>[l(s,null,{default:o(()=>[l(I(fe))]),_:1}),e[25]||(e[25]=c(" 编辑 ",-1))]),_:1,__:[25]}),l(n,{size:"small",type:"danger",onClick:W},{default:o(()=>[l(s,null,{default:o(()=>[l(I(ve))]),_:1}),e[26]||(e[26]=c(" 删除 ",-1))]),_:1,__:[26]})])):k("",!0)])]),default:o(()=>[i.value?(p(),h("div",Gt,[r("div",Yt,[e[27]||(e[27]=r("h4",null,"基本信息",-1)),l(O,{column:2,border:""},{default:o(()=>[l(N,{label:"机构层级"},{default:o(()=>[l(F,{type:Ne(i.value.level)},{default:o(()=>[c(_(xe(i.value.level)),1)]),_:1},8,["type"])]),_:1}),l(N,{label:"机构码"},{default:o(()=>[c(_(i.value.code),1)]),_:1}),l(N,{label:"全称"},{default:o(()=>[c(_(i.value.fullName),1)]),_:1}),l(N,{label:"简称"},{default:o(()=>[c(_(i.value.shortName),1)]),_:1}),i.value.level==="province"?(p(),q(N,{key:0,label:"单字简称"},{default:o(()=>[c(_(i.value.singleName),1)]),_:1})):k("",!0),i.value.level==="examSite"?(p(),q(N,{key:1,label:"URI"},{default:o(()=>[c(_(i.value.uri||"未设置"),1)]),_:1})):k("",!0),l(N,{label:"创建时间"},{default:o(()=>[c(_(K(i.value.createTime)),1)]),_:1}),l(N,{label:"更新时间"},{default:o(()=>[c(_(K(i.value.updateTime)),1)]),_:1})]),_:1})]),r("div",Zt,[e[28]||(e[28]=r("h4",null,"统计信息",-1)),l(le,{gutter:16},{default:o(()=>[l(g,{span:6},{default:o(()=>[l(L,{title:"下级机构数",value:i.value.childrenCount||0},null,8,["value"])]),_:1}),l(g,{span:6},{default:o(()=>[l(L,{title:"考点总数",value:i.value.examSiteCount||0},null,8,["value"])]),_:1}),l(g,{span:6},{default:o(()=>[l(L,{title:"状态",value:i.value.status===1?"正常":"禁用"},null,8,["value"])]),_:1}),l(g,{span:6},{default:o(()=>[l(L,{title:"排序",value:i.value.sort||0},null,8,["value"])]),_:1})]),_:1})])])):(p(),h("div",Jt,[l(Te,{description:"请从左侧选择机构节点查看详情"})]))]),_:1})]),_:1})]),_:1})]),l(ne,{modelValue:S.value,"onUpdate:modelValue":e[7]||(e[7]=d=>S.value=d),title:"导入机构数据",width:"600px","close-on-click-modal":!1},{footer:o(()=>[r("div",Wt,[l(n,{onClick:e[6]||(e[6]=d=>S.value=!1)},{default:o(()=>e[35]||(e[35]=[c("取消",-1)])),_:1,__:[35]}),l(n,{type:"primary",loading:E.value,onClick:Ue},{default:o(()=>[c(_(E.value?"导入中...":"开始导入"),1)]),_:1},8,["loading"])])]),default:o(()=>[r("div",Kt,[l(Le,{title:"导入说明",type:"info",closable:!1,"show-icon":""},{default:o(()=>e[29]||(e[29]=[r("p",null,"支持两种导入方式：",-1),r("ul",null,[r("li",null,"从综合考务平台导入：自动同步最新的机构数据"),r("li",null,"从全局数据库导入：直接从数据库获取完整机构树")],-1)])),_:1,__:[29]}),r("div",Qt,[l(oe,{modelValue:B.value,"onUpdate:modelValue":e[2]||(e[2]=d=>B.value=d),size:"large"},{default:o(()=>[l(ae,{label:"platform"},{default:o(()=>e[30]||(e[30]=[c("从综合考务平台导入",-1)])),_:1,__:[30]}),l(ae,{label:"database"},{default:o(()=>e[31]||(e[31]=[c("从全局数据库导入",-1)])),_:1,__:[31]})]),_:1},8,["modelValue"])]),r("div",Xt,[l(se,{model:A,"label-width":"120px"},{default:o(()=>[B.value==="platform"?(p(),q(j,{key:0,label:"平台地址"},{default:o(()=>[l(m,{modelValue:A.platformUrl,"onUpdate:modelValue":e[3]||(e[3]=d=>A.platformUrl=d),placeholder:"请输入综合考务平台地址"},null,8,["modelValue"])]),_:1})):k("",!0),B.value==="database"?(p(),q(j,{key:1,label:"数据库连接"},{default:o(()=>[l(m,{modelValue:A.databaseUrl,"onUpdate:modelValue":e[4]||(e[4]=d=>A.databaseUrl=d),placeholder:"请输入数据库连接字符串"},null,8,["modelValue"])]),_:1})):k("",!0),l(j,{label:"导入选项"},{default:o(()=>[l(Re,{modelValue:A.options,"onUpdate:modelValue":e[5]||(e[5]=d=>A.options=d)},{default:o(()=>[l(H,{label:"overwrite"},{default:o(()=>e[32]||(e[32]=[c("覆盖已存在的数据",-1)])),_:1,__:[32]}),l(H,{label:"backup"},{default:o(()=>e[33]||(e[33]=[c("导入前备份现有数据",-1)])),_:1,__:[33]}),l(H,{label:"validate"},{default:o(()=>e[34]||(e[34]=[c("验证数据完整性",-1)])),_:1,__:[34]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])])])]),_:1},8,["modelValue"]),l(ne,{modelValue:C.value,"onUpdate:modelValue":e[18]||(e[18]=d=>C.value=d),title:b.value==="add"?"新增机构":"编辑机构",width:"600px","close-on-click-modal":!1},{footer:o(()=>[r("div",dl,[l(n,{onClick:e[17]||(e[17]=d=>C.value=!1)},{default:o(()=>e[39]||(e[39]=[c("取消",-1)])),_:1,__:[39]}),l(n,{type:"primary",loading:x.value,onClick:Ee},{default:o(()=>[c(_(x.value?"保存中...":"保存"),1)]),_:1},8,["loading"])])]),default:o(()=>[l(se,{ref_key:"editFormRef",ref:D,model:a,rules:ge,"label-width":"120px"},{default:o(()=>[l(j,{label:"上级机构",prop:"parentId"},{default:o(()=>[l(Fe,{modelValue:a.parentId,"onUpdate:modelValue":e[8]||(e[8]=d=>a.parentId=d),data:z.value,props:J,placeholder:"请选择上级机构","check-strictly":"","render-after-expand":!1,"default-expanded-keys":a.parentId?[a.parentId]:[],filterable:""},null,8,["modelValue","data","default-expanded-keys"]),a.parentId?(p(),h("div",el,[r("span",null,"已选择："+_(ze(a.parentId)),1)])):k("",!0)]),_:1}),l(j,{label:"机构层级",prop:"level"},{default:o(()=>[l(He,{modelValue:a.level,"onUpdate:modelValue":e[9]||(e[9]=d=>a.level=d),placeholder:"请选择机构层级",disabled:b.value==="edit"},{default:o(()=>[(p(!0),h(It,null,Ct(_e.value,d=>(p(),q(Oe,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"]),a.parentId?(p(),h("div",tl,[r("span",null,_(be()),1)])):k("",!0)]),_:1}),l(j,{label:"机构码",prop:"code"},{default:o(()=>[l(m,{modelValue:a.code,"onUpdate:modelValue":e[10]||(e[10]=d=>a.code=d),placeholder:"请输入机构码",disabled:b.value==="edit",onBlur:Pe},null,8,["modelValue","disabled"]),r("div",ll,[a.level==="root"?(p(),h("span",al,"根节点码，固定为：00")):a.level==="province"?(p(),h("span",ol,"省份码，如：51（四川省）")):a.level==="city"?(p(),h("span",sl,"地市码，如：5101（成都市）")):a.level==="district"?(p(),h("span",nl,"区县码，如：510104（锦江区）")):a.level==="examSite"?(p(),h("span",rl,"考点码，如：510104001（第一个考点）")):k("",!0)])]),_:1}),l(j,{label:"全称",prop:"fullName"},{default:o(()=>[l(m,{modelValue:a.fullName,"onUpdate:modelValue":e[11]||(e[11]=d=>a.fullName=d),placeholder:"请输入机构全称"},null,8,["modelValue"])]),_:1}),l(j,{label:"简称",prop:"shortName"},{default:o(()=>[l(m,{modelValue:a.shortName,"onUpdate:modelValue":e[12]||(e[12]=d=>a.shortName=d),placeholder:"请输入机构简称",onBlur:$e},null,8,["modelValue"])]),_:1}),a.level==="province"?(p(),q(j,{key:0,label:"单字简称",prop:"singleName"},{default:o(()=>[l(m,{modelValue:a.singleName,"onUpdate:modelValue":e[13]||(e[13]=d=>a.singleName=d),placeholder:"请输入单字简称",maxlength:"1"},null,8,["modelValue"])]),_:1})):k("",!0),a.level==="examSite"?(p(),q(j,{key:1,label:"URI",prop:"uri"},{default:o(()=>[l(m,{modelValue:a.uri,"onUpdate:modelValue":e[14]||(e[14]=d=>a.uri=d),placeholder:"输入简称后自动生成，可手动修改",onBlur:Ae},null,8,["modelValue"]),e[36]||(e[36]=r("div",{class:"form-tip"},[r("p",null,[c("💡 "),r("strong",null,"自动生成规则"),c("：输入简称后系统会自动生成URI，您也可以手动修改")]),r("p",null,[r("strong",null,"省直属考点"),c("：学校简拼.省份教育简拼.cnjy")]),r("p",null,[r("strong",null,"市直属考点"),c("：学校简拼.地市教育简拼.省份教育简拼.cnjy")]),r("p",null,[r("strong",null,"区县考点"),c("：学校简拼.区县教育简拼.地市教育简拼.省份教育简拼.cnjy")]),r("p",null,[r("strong",null,"示例"),c("：cdqz.jjjy.cdjy.scjy.cnjy（成都七中.锦江教育.成都教育.四川教育.中国教育）")])],-1))]),_:1,__:[36]})):k("",!0),l(j,{label:"排序",prop:"sort"},{default:o(()=>[l(Ge,{modelValue:a.sort,"onUpdate:modelValue":e[15]||(e[15]=d=>a.sort=d),min:0,max:9999},null,8,["modelValue"])]),_:1}),l(j,{label:"状态",prop:"status"},{default:o(()=>[l(oe,{modelValue:a.status,"onUpdate:modelValue":e[16]||(e[16]=d=>a.status=d)},{default:o(()=>[l(re,{label:1},{default:o(()=>e[37]||(e[37]=[c("正常",-1)])),_:1,__:[37]}),l(re,{label:0},{default:o(()=>e[38]||(e[38]=[c("禁用",-1)])),_:1,__:[38]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),et(r("ul",{style:qt(Z.value),class:"context-menu"},[i.value&&i.value.level!=="examSite"?(p(),h("li",{key:0,onClick:Se},[l(s,null,{default:o(()=>[l(I(pe))]),_:1}),e[40]||(e[40]=c(" 新增下级 ",-1))])):k("",!0),r("li",{onClick:X},[l(s,null,{default:o(()=>[l(I(fe))]),_:1}),e[41]||(e[41]=c(" 编辑 ",-1))]),r("li",{onClick:W,class:"danger"},[l(s,null,{default:o(()=>[l(I(ve))]),_:1}),e[42]||(e[42]=c(" 删除 ",-1))])],4),[[Ut,w.value]])])}}}),Vl=Ye(il,[["__scopeId","data-v-ea1eaa40"]]);export{Vl as default};
