import{_ as r}from"./_plugin-vue_export-helper-fs8hP-CV.js";/* empty css                *//* empty css                 */import{b as c,j as d,o as _,l as o,w as a,q as e,ae as p,a9 as i}from"./index-DKnB9mwy.js";const l={class:"app-container"},m={class:"page-content"},f=c({__name:"index",setup(h){return(u,t)=>{const s=p,n=i;return _(),d("div",l,[o(n,{shadow:"hover"},{header:a(()=>t[0]||(t[0]=[e("div",{class:"card-header"},[e("span",null,"实时监控")],-1)])),default:a(()=>[e("div",m,[o(s,{description:"实时监控功能开发中..."})])]),_:1})])}}}),B=r(f,[["__scopeId","data-v-53b3d84e"]]);export{B as default};
