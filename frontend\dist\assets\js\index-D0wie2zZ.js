var _e=Object.defineProperty,ve=Object.defineProperties;var ge=Object.getOwnPropertyDescriptors;var M=Object.getOwnPropertySymbols;var be=Object.prototype.hasOwnProperty,we=Object.prototype.propertyIsEnumerable;var K=(p,i,n)=>i in p?_e(p,i,{enumerable:!0,configurable:!0,writable:!0,value:n}):p[i]=n,z=(p,i)=>{for(var n in i||(i={}))be.call(i,n)&&K(p,n,i[n]);if(M)for(var n of M(i))we.call(i,n)&&K(p,n,i[n]);return p},T=(p,i)=>ve(p,ge(i));var R=(p,i,n)=>new Promise((_,v)=>{var g=d=>{try{m(n.next(d))}catch(r){v(r)}},h=d=>{try{m(n.throw(d))}catch(r){v(r)}},m=d=>d.done?_(d.value):Promise.resolve(d.value).then(g,h);m((n=n.apply(p,i)).next())});import{_ as ye}from"./_plugin-vue_export-helper-fs8hP-CV.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                     *//* empty css               *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                *//* empty css                        *//* empty css                  */import{b as Ce,h as b,_ as N,c as xe,C as he,j as B,o as V,q as s,l as t,w as l,ap as ke,a5 as Se,M as c,p as De,u as E,ay as Ve,az as Ee,a9 as Oe,a8 as ze,Q as Te,e as Re,ai as Be,aj as Fe,ak as $e,al as je,a0 as Ie,aA as Pe,a7 as Ue,a6 as Le,$ as Me,aE as Ke,t as F,am as Ne,F as q,s as A,aM as qe,G as x,N as G}from"./index-DKnB9mwy.js";const Ae={class:"osd-rules-management"},Ge={class:"toolbar"},Qe={class:"toolbar-content"},He={class:"toolbar-right"},Je={class:"main-content"},We={class:"card-header"},Xe={class:"header-actions"},Ye={class:"pagination-wrapper"},Ze={class:"format-builder"},et={class:"format-display"},tt={class:"preview-area"},lt={class:"dialog-footer"},at={class:"format-builder-content"},ot={class:"builder-section"},st={class:"format-items"},nt={class:"builder-section"},rt={class:"format-options"},it={class:"builder-section"},dt={class:"format-preview"},ut={class:"dialog-footer"},pt=Ce({__name:"index",setup(p){const i=b(!1),n=b(""),_=b(!1),v=b(!1),g=b("add"),h=b(),m=N({currentPage:1,pageSize:20,total:0}),d=b([{id:1,name:"完整OSD格式",type:"osd",format:"[省份][地市][区县][考点名称][考点编号] 考场[考场号]",preview:"浙江省杭州西湖第一考点KD001 考场01",description:"包含完整地理信息的OSD格式",createTime:"2024-03-21 10:30:00"},{id:2,name:"简化OSD格式",type:"osd",format:"[省份简称][地市][考点名称] 考场[考场号]",preview:"浙江杭州第一考点 考场01",description:"简化的OSD格式，减少显示内容",createTime:"2024-03-21 11:00:00"},{id:3,name:"代码格式OSD",type:"osd",format:"[省份代码][地市][区县][考点编号] 考场[考场号]",preview:"33杭州西湖KD001 考场01",description:"使用省份代码的OSD格式",createTime:"2024-03-21 12:00:00"},{id:4,name:"单字简称格式",type:"osd",format:"[省份单字简称][地市][考点名称] 考场[考场号]",preview:"浙杭州第一考点 考场01",description:"使用省份单字简称的OSD格式",createTime:"2024-03-21 13:00:00"}]),r=N({id:null,name:"",type:"osd",format:"",description:""}),y=b([]),Q=[{label:"[省份]",value:"[省份]"},{label:"[省份简称]",value:"[省份简称]"},{label:"[省份单字简称]",value:"[省份单字简称]"},{label:"[省份代码]",value:"[省份代码]"},{label:"[地市]",value:"[地市]"},{label:"[区县]",value:"[区县]"},{label:"[考点名称]",value:"[考点名称]"},{label:"[考点编号]",value:"[考点编号]"},{label:"考场[考场号]",value:"考场[考场号]"},{label:"空格",value:" "},{label:"-",value:"-"},{label:"_",value:"_"}],H={name:[{required:!0,message:"请输入规则名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],format:[{required:!0,message:"请输入格式模板",trigger:"blur"}]},J=xe(()=>n.value?d.value.filter(a=>a.name.toLowerCase().includes(n.value.toLowerCase())):d.value),W=()=>{i.value=!0,setTimeout(()=>{i.value=!1,x.success("刷新成功")},1e3)},X=()=>{},Y=()=>{g.value="add",se(),_.value=!0},Z=a=>{g.value="edit",Object.assign(r,a),_.value=!0},ee=a=>{g.value="add",Object.assign(r,T(z({},a),{id:null,name:`${a.name}_副本`})),_.value=!0},te=a=>R(this,null,function*(){try{yield G.confirm(`确定要删除规则"${a.name}"吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=d.value.findIndex(f=>f.id===a.id);e>-1&&(d.value.splice(e,1),x.success("删除成功"))}catch(e){x.info("已取消删除")}}),le=a=>{},ae=a=>{m.pageSize=a},oe=a=>{m.currentPage=a},se=()=>{Object.assign(r,{id:null,name:"",type:"osd",format:"",description:""}),y.value=[]},ne=()=>R(this,null,function*(){if(h.value)try{if(yield h.value.validate(),g.value==="add"){const a=T(z({},r),{id:Date.now(),preview:$(),createTime:new Date().toLocaleString()});d.value.unshift(a),x.success("新增成功")}else{const a=d.value.findIndex(e=>e.id===r.id);a>-1&&(Object.assign(d.value[a],T(z({},r),{preview:$()})),x.success("保存成功"))}_.value=!1}catch(a){x.error("表单验证失败")}}),$=()=>I(r.format),I=a=>{if(!a)return"";const e={"[省份]":"浙江省","[省份简称]":"浙江","[省份单字简称]":"浙","[省份代码]":"33","[地市]":"杭州","[区县]":"西湖","[考点名称]":"第一考点","[考点编号]":"KD001","考场[考场号]":"考场01"};let f=a;return Object.entries(e).forEach(([u,j])=>{f=f.replace(new RegExp(u.replace(/[[\]]/g,"\\$&"),"g"),j)}),f},P=a=>{y.value.push(a)},re=a=>{y.value.splice(a,1)},ie=()=>R(this,null,function*(){try{const{value:a}=yield G.prompt("请输入自定义文本","自定义文本",{confirmButtonText:"确定",cancelButtonText:"取消"});a&&a.trim()&&P(a.trim())}catch(a){}}),de=()=>{const a=y.value.join("");return I(a)},ue=()=>{r.format=y.value.join(""),v.value=!1};return he(()=>{m.total=d.value.length}),(a,e)=>{const f=De,u=Se,j=ke,U=Oe,k=Ie,w=Fe,pe=Be,me=je,S=ze,D=Ue,O=Me,ce=Le,L=Ne,fe=$e;return V(),B("div",Ae,[s("div",Ge,[t(U,{class:"toolbar-card"},{default:l(()=>[s("div",Qe,[e[13]||(e[13]=s("div",{class:"toolbar-left"},[s("h3",null,"OSD生成规则管理"),s("p",null,"管理OSD标签的生成规则，配置考点信息显示格式")],-1)),s("div",He,[t(j,null,{default:l(()=>[t(u,{type:"primary",onClick:Y},{default:l(()=>[t(f,null,{default:l(()=>[t(E(Ve))]),_:1}),e[11]||(e[11]=c(" 新增规则 ",-1))]),_:1,__:[11]}),t(u,{onClick:W},{default:l(()=>[t(f,null,{default:l(()=>[t(E(Ee))]),_:1}),e[12]||(e[12]=c(" 刷新 ",-1))]),_:1,__:[12]})]),_:1})])])]),_:1})]),s("div",Je,[t(D,{gutter:20},{default:l(()=>[t(S,{span:24},{default:l(()=>[t(U,{class:"rules-card"},{header:l(()=>[s("div",We,[e[14]||(e[14]=s("span",null,"OSD规则列表",-1)),s("div",Xe,[t(k,{modelValue:n.value,"onUpdate:modelValue":e[0]||(e[0]=o=>n.value=o),placeholder:"搜索规则名称",style:{width:"200px"},clearable:"",onInput:X},{prefix:l(()=>[t(f,null,{default:l(()=>[t(E(Pe))]),_:1})]),_:1},8,["modelValue"])])])]),default:l(()=>[Te((V(),Re(pe,{data:J.value,stripe:"",style:{width:"100%"},onSelectionChange:le},{default:l(()=>[t(w,{type:"selection",width:"55"}),t(w,{prop:"id",label:"规则ID",width:"120"}),t(w,{prop:"name",label:"规则名称","min-width":"150"}),t(w,{prop:"format",label:"格式模板","min-width":"250","show-overflow-tooltip":""}),t(w,{prop:"preview",label:"预览效果","min-width":"250","show-overflow-tooltip":""}),t(w,{prop:"createTime",label:"创建时间",width:"180"}),t(w,{label:"操作",width:"280",fixed:"right","class-name":"no-wrap-column"},{default:l(({row:o})=>[t(u,{type:"primary",size:"small",onClick:C=>Z(o)},{default:l(()=>e[15]||(e[15]=[c(" 编辑 ",-1)])),_:2,__:[15]},1032,["onClick"]),t(u,{type:"success",size:"small",onClick:C=>ee(o)},{default:l(()=>e[16]||(e[16]=[c(" 复制 ",-1)])),_:2,__:[16]},1032,["onClick"]),t(u,{type:"danger",size:"small",onClick:C=>te(o)},{default:l(()=>e[17]||(e[17]=[c(" 删除 ",-1)])),_:2,__:[17]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[fe,i.value]]),s("div",Ye,[t(me,{"current-page":m.currentPage,"onUpdate:currentPage":e[1]||(e[1]=o=>m.currentPage=o),"page-size":m.pageSize,"onUpdate:pageSize":e[2]||(e[2]=o=>m.pageSize=o),total:m.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper","prev-text":"上一页","next-text":"下一页","page-size-text":"条/页","total-text":"共 {total} 条记录","jumper-text":"前往","page-text":"页",onSizeChange:ae,onCurrentChange:oe},null,8,["current-page","page-size","total"])])]),_:1})]),_:1})]),_:1})]),t(L,{modelValue:_.value,"onUpdate:modelValue":e[8]||(e[8]=o=>_.value=o),title:g.value==="add"?"新增OSD规则":"编辑OSD规则",width:"1000px","close-on-click-modal":!1},{footer:l(()=>[s("div",lt,[t(u,{onClick:e[7]||(e[7]=o=>_.value=!1)},{default:l(()=>e[19]||(e[19]=[c("取消",-1)])),_:1,__:[19]}),t(u,{type:"primary",onClick:ne},{default:l(()=>[c(F(g.value==="add"?"新增":"保存"),1)]),_:1})])]),default:l(()=>[t(ce,{ref_key:"ruleFormRef",ref:h,model:r,rules:H,"label-width":"100px",class:"rule-form"},{default:l(()=>[t(D,{gutter:20},{default:l(()=>[t(S,{span:24},{default:l(()=>[t(O,{label:"规则名称",prop:"name"},{default:l(()=>[t(k,{modelValue:r.name,"onUpdate:modelValue":e[3]||(e[3]=o=>r.name=o),placeholder:"请输入规则名称",class:"form-input"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),t(D,{gutter:20},{default:l(()=>[t(S,{span:24},{default:l(()=>[t(O,{label:"描述",prop:"description"},{default:l(()=>[t(k,{modelValue:r.description,"onUpdate:modelValue":e[4]||(e[4]=o=>r.description=o),type:"textarea",rows:3,placeholder:"请输入规则描述",class:"form-input"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),t(D,{gutter:20},{default:l(()=>[t(S,{span:24},{default:l(()=>[t(O,{label:"格式模板",prop:"format"},{default:l(()=>[s("div",Ze,[s("div",et,[t(k,{modelValue:r.format,"onUpdate:modelValue":e[5]||(e[5]=o=>r.format=o),placeholder:"格式模板将在这里显示",readonly:"",class:"form-input"},null,8,["modelValue"]),t(u,{type:"primary",onClick:e[6]||(e[6]=o=>v.value=!0),style:{"margin-top":"10px"}},{default:l(()=>[t(f,null,{default:l(()=>[t(E(Ke))]),_:1}),e[18]||(e[18]=c(" 编辑格式 ",-1))]),_:1,__:[18]})])])]),_:1})]),_:1})]),_:1}),t(D,{gutter:20},{default:l(()=>[t(S,{span:24},{default:l(()=>[t(O,{label:"预览效果"},{default:l(()=>[s("div",tt,[t(k,{value:$(),placeholder:"预览效果将在这里显示",readonly:"",class:"form-input"},null,8,["value"])])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),t(L,{modelValue:v.value,"onUpdate:modelValue":e[10]||(e[10]=o=>v.value=o),title:"OSD格式构建器",width:"900px","close-on-click-modal":!1},{footer:l(()=>[s("div",ut,[t(u,{onClick:e[9]||(e[9]=o=>v.value=!1)},{default:l(()=>e[24]||(e[24]=[c("取消",-1)])),_:1,__:[24]}),t(u,{type:"primary",onClick:ue},{default:l(()=>e[25]||(e[25]=[c("确定",-1)])),_:1,__:[25]})])]),default:l(()=>[s("div",at,[s("div",ot,[e[20]||(e[20]=s("h4",null,"格式构建器",-1)),s("div",st,[(V(!0),B(q,null,A(y.value,(o,C)=>(V(),B("div",{key:C,class:"format-item"},[s("span",null,F(o),1),t(u,{type:"danger",size:"small",circle:"",onClick:mt=>re(C)},{default:l(()=>[t(f,null,{default:l(()=>[t(E(qe))]),_:1})]),_:2},1032,["onClick"])]))),128))])]),s("div",nt,[e[22]||(e[22]=s("h4",null,"可用选项",-1)),s("div",rt,[(V(),B(q,null,A(Q,o=>t(u,{key:o.value,size:"small",onClick:C=>P(o.value)},{default:l(()=>[c(F(o.label),1)]),_:2},1032,["onClick"])),64)),t(u,{size:"small",onClick:ie},{default:l(()=>e[21]||(e[21]=[c(" 自定义文本 ",-1)])),_:1,__:[21]})])]),s("div",it,[e[23]||(e[23]=s("h4",null,"预览",-1)),s("div",dt,F(de()),1)])])]),_:1},8,["modelValue"])])}}}),Bt=ye(pt,[["__scopeId","data-v-cb48eecd"]]);export{Bt as default};
