import{_ as c}from"./_plugin-vue_export-helper-fs8hP-CV.js";/* empty css                *//* empty css                 */import{b as r,j as _,o as d,l as o,w as a,q as e,ae as p,a9 as i}from"./index-DKnB9mwy.js";const l={class:"app-container"},m={class:"page-content"},f=r({__name:"index",setup(h){return(u,t)=>{const s=p,n=i;return d(),_("div",l,[o(n,{shadow:"hover"},{header:a(()=>t[0]||(t[0]=[e("div",{class:"card-header"},[e("span",null,"源逻辑拓扑")],-1)])),default:a(()=>[e("div",m,[o(s,{description:"源逻辑拓扑功能开发中..."})])]),_:1})])}}}),B=c(f,[["__scopeId","data-v-4f4f253c"]]);export{B as default};
