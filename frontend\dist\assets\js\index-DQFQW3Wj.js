import{_ as c}from"./_plugin-vue_export-helper-fs8hP-CV.js";/* empty css                *//* empty css                 */import{b as r,j as d,o as _,l as a,w as o,q as e,ae as p,a9 as i}from"./index-DKnB9mwy.js";const l={class:"app-container"},m={class:"page-content"},f=r({__name:"index",setup(h){return(u,t)=>{const s=p,n=i;return _(),d("div",l,[a(n,{shadow:"hover"},{header:o(()=>t[0]||(t[0]=[e("div",{class:"card-header"},[e("span",null,"菜单管理")],-1)])),default:o(()=>[e("div",m,[a(s,{description:"菜单管理功能开发中..."})])]),_:1})])}}}),B=c(f,[["__scopeId","data-v-7d153c2a"]]);export{B as default};
