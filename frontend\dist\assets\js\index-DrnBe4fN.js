import{_ as m}from"./_plugin-vue_export-helper-fs8hP-CV.js";import{b as p,y as i,c as l,i as n,j as k,o as e,l as u,p as d,w as h,e as t,u as f}from"./index-DKnB9mwy.js";const x=p({__name:"index",setup(y){const o=i(),s=l(()=>o.isDark),c=()=>{o.toggleTheme()};return(T,g)=>{const a=n("Sunny"),r=n("Moon"),_=d;return e(),k("div",{class:"theme-picker",onClick:c},[u(_,{size:18},{default:h(()=>[f(s)?(e(),t(a,{key:0})):(e(),t(r,{key:1}))]),_:1})])}}}),v=m(x,[["__scopeId","data-v-65701e44"]]);export{v as T};
