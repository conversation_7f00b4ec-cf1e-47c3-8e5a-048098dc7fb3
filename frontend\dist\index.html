<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="标考高清网上巡查管理平台 - 专业的视频监控巡查管理系统" />
    <meta name="keywords" content="巡查管理,视频监控,安防系统,设备管理" />
    <meta name="author" content="AIDesign" />
    <title>标考高清网上巡查管理平台</title>
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- 防止页面闪烁 -->
    <style>
      html, body {
        margin: 0;
        padding: 0;
        height: 100%;
        background-color: #f2f3f5;
      }
      
      #app {
        height: 100%;
      }
      
      /* 加载动画 */
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
      }
      
      .loading-text {
        color: #fff;
        font-size: 16px;
        margin-top: 20px;
        font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      
      /* 暗色主题初始化 */
      .dark {
        background-color: #0a0a0a;
      }
    </style>
    <script type="module" crossorigin src="./assets/js/index-DKnB9mwy.js"></script>
    <link rel="stylesheet" crossorigin href="./assets/css/index-C8Q2qDk8.css">
  </head>
  <body>
    <div id="app">
      <!-- 初始加载动画 -->
      <div class="loading-container" id="initial-loading">
        <div style="text-align: center;">
          <div class="loading-spinner"></div>
          <div class="loading-text">正在加载系统...</div>
        </div>
      </div>
    </div>
    
    
    <!-- 移除加载动画 -->
    <script>
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loading = document.getElementById('initial-loading');
          if (loading) {
            loading.style.opacity = '0';
            loading.style.transition = 'opacity 0.5s ease-out';
            setTimeout(function() {
              loading.remove();
            }, 500);
          }
        }, 1000);
      });
      
      // 检测系统主题偏好
      if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        document.documentElement.classList.add('dark');
      }
      
      // 监听主题变化
      if (window.matchMedia) {
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
          if (e.matches) {
            document.documentElement.classList.add('dark');
          } else {
            document.documentElement.classList.remove('dark');
          }
        });
      }
    </script>
  </body>
</html>
